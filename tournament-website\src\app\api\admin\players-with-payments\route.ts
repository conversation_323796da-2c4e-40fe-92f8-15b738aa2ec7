import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const players = await prisma.user.findMany({
      where: {
        role: 'PLAYER'
      },
      include: {
        registrations: {
          include: {
            game: {
              select: {
                id: true,
                name: true
              }
            },
            tournamentSchedule: {
              select: {
                tournamentId: true
              }
            }
          },
          orderBy: {
            registrationDate: 'desc'
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Transform the data to include payment information
    const playersWithPayments = players.map(player => ({
      id: player.id,
      username: player.username,
      firstName: player.firstName,
      lastName: player.lastName,
      phoneNumber: player.phoneNumber,
      email: player.email,
      registrations: player.registrations.map(reg => ({
        id: reg.id,
        gameUsername: reg.gameUsername,
        registrationDate: reg.registrationDate.toISOString(),
        paymentStatus: reg.paymentStatus || 'UNPAID',
        paymentDate: reg.paymentDate?.toISOString(),
        paymentAmount: reg.paymentAmount ? Number(reg.paymentAmount) : null,
        paymentNotes: reg.paymentNotes,
        game: reg.game,
        tournamentSchedule: reg.tournamentSchedule
      }))
    }))

    return NextResponse.json(playersWithPayments)
  } catch (error) {
    logger.error('Error fetching players with payments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch players' },
      { status: 500 }
    )
  }
})
