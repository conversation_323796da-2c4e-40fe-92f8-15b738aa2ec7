'use client'

import { useState, useEffect } from 'react'

interface InactivityWarningProps {
  isVisible: boolean
  timeRemaining: number // in seconds
  onStayLoggedIn: () => void
  onLogout: () => void
}

export default function InactivityWarning({ 
  isVisible, 
  timeRemaining, 
  onStayLoggedIn, 
  onLogout 
}: InactivityWarningProps) {
  const [countdown, setCountdown] = useState(timeRemaining)

  useEffect(() => {
    setCountdown(timeRemaining)
  }, [timeRemaining])

  useEffect(() => {
    if (!isVisible || countdown <= 0) return

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          onLogout()
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [isVisible, countdown, onLogout])

  if (!isVisible) return null

  const minutes = Math.floor(countdown / 60)
  const seconds = countdown % 60

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0">
            <svg className="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-gray-900">
              Session Timeout Warning
            </h3>
          </div>
        </div>

        <div className="mb-6">
          <p className="text-sm text-gray-600 mb-3">
            You've been inactive for a while. For security reasons, you'll be automatically logged out in:
          </p>
          
          <div className="text-center">
            <div className="text-3xl font-bold text-red-600 mb-2">
              {minutes}:{seconds.toString().padStart(2, '0')}
            </div>
            <p className="text-sm text-gray-500">
              {minutes > 0 ? `${minutes} minute${minutes !== 1 ? 's' : ''} and ` : ''}
              {seconds} second{seconds !== 1 ? 's' : ''}
            </p>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={onStayLoggedIn}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Stay Logged In
          </button>
          <button
            onClick={onLogout}
            className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            Logout Now
          </button>
        </div>

        <div className="mt-4 text-xs text-gray-500 text-center">
          Click anywhere or press any key to stay logged in
        </div>
      </div>
    </div>
  )
}
