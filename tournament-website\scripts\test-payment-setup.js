const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🧪 Setting up test data for payment functionality...')

  try {
    // Get games
    const games = await prisma.game.findMany()
    console.log('Found games:', games.map(g => g.name))

    // Create a few test users
    const testUsers = [
      {
        username: 'testplayer1',
        firstName: '<PERSON>',
        lastName: 'Banda',
        phoneNumber: '+265991234567',
        password: await bcrypt.hash('password123', 12),
        role: 'PLAYER'
      },
      {
        username: 'testplayer2',
        firstName: 'Mary',
        lastName: 'Phiri',
        phoneNumber: '+265992345678',
        password: await bcrypt.hash('password123', 12),
        role: 'PLAYER'
      }
    ]

    // Create users and registrations
    for (const userData of testUsers) {
      const user = await prisma.user.upsert({
        where: { username: userData.username },
        update: {},
        create: userData
      })

      console.log(`✅ Created user: ${user.username}`)

      // Register for PUBG and Call of Duty
      for (const game of games.slice(0, 2)) { // Only PUBG and Call of Duty
        await prisma.playerRegistration.upsert({
          where: {
            userId_gameId: {
              userId: user.id,
              gameId: game.id
            }
          },
          update: {},
          create: {
            userId: user.id,
            gameId: game.id,
            gameUsername: `${user.username}_${game.name.toLowerCase().replace(/\s+/g, '')}`,
            paymentStatus: 'UNPAID' // Default status
          }
        })

        console.log(`✅ Registered ${user.username} for ${game.name}`)
      }
    }

    console.log('\n🎉 Test data setup complete!')
    console.log('You can now test payment functionality in the admin panel.')
    
  } catch (error) {
    console.error('❌ Error setting up test data:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Script failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
