import { NextRequest, NextResponse } from 'next/server'
import { validateSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'

export async function GET(request: NextRequest) {
  try {
    const sessionCookie = request.cookies.get('session')
    if (!sessionCookie) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    const session = await validateSession(sessionCookie.value)
    if (!session) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userIdParam = searchParams.get('userId')
    
    if (!userIdParam) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    const userId = parseInt(userIdParam)
    
    // Check if the user is requesting their own stats or if they're an admin
    if (session.userId !== userId && session.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    // Get player stats for the user
    const playerStats = await prisma.playerStats.findMany({
      where: {
        user: {
          id: userId
        }
      },
      include: {
        game: {
          select: {
            name: true
          }
        }
      }
    })

    return NextResponse.json(playerStats)
  } catch (error) {
    logger.error('Error fetching user stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
