import { NextRequest, NextResponse } from 'next/server'
import { validateSession, hashPassword } from '@/lib/auth'
import crypto from 'crypto'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const gameId = searchParams.get('gameId')
    const userId = searchParams.get('userId')

    const where: any = {}
    if (gameId) {
      const parsedGameId = parseInt(gameId, 10)
      if (isNaN(parsedGameId)) {
        return NextResponse.json({ error: 'Invalid game ID' }, { status: 400 })
      }
      where.gameId = parsedGameId
    }
    if (userId) {
      const parsedUserId = parseInt(userId, 10)
      if (isNaN(parsedUserId)) {
        return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 })
      }
      where.userId = parsedUserId
    }

    const registrations = await prisma.playerRegistration.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        },
        game: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        registrationDate: 'desc'
      }
    })

    return NextResponse.json(registrations)
  } catch (error) {
    logger.error('Error fetching registrations:', error)
    return NextResponse.json(
      { error: 'Failed to fetch registrations' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const sessionCookie = request.cookies.get('session')
    let session = null
    let actualUserId = null

    if (sessionCookie) {
      session = await validateSession(sessionCookie.value)
      if (session) {
        actualUserId = session.userId
      }
    }

    const body = await request.json()
    const { userId, gameId, gameUsername, userData } = body

    // If user is authenticated, use their ID
    if (session) {
      actualUserId = session.userId
    } else if (userData) {
      // Legacy support for non-authenticated registration
      const { username, firstName, lastName, phoneNumber } = userData

      if (!username || !firstName || !lastName) {
        return NextResponse.json(
          { error: 'Username, first name, and last name are required' },
          { status: 400 }
        )
      }

      // Create or update user
      const user = await prisma.user.upsert({
        where: { username },
        update: {
          firstName,
          lastName,
          phoneNumber
        },
        create: {
          username,
          firstName,
          lastName,
          phoneNumber,
          password: await hashPassword(crypto.randomBytes(32).toString('hex')) // Secure random password for legacy users
        }
      })
      actualUserId = user.id
    }

    if (!actualUserId || !gameId) {
      return NextResponse.json(
        { error: 'User ID and Game ID are required' },
        { status: 400 }
      )
    }

    // For PES, gameUsername is not required
    const selectedGame = await prisma.game.findUnique({
      where: { id: parseInt(gameId) }
    })

    const isPES = selectedGame?.name === 'PES'
    if (!isPES && !gameUsername) {
      return NextResponse.json(
        { error: 'Game Username is required for this game' },
        { status: 400 }
      )
    }

    // Check if user already registered for this game
    const existingRegistration = await prisma.playerRegistration.findUnique({
      where: {
        userId_gameId: {
          userId: actualUserId,
          gameId: parseInt(gameId, 10)
        }
      }
    })

    if (existingRegistration) {
      return NextResponse.json(
        { error: 'User already registered for this game' },
        { status: 400 }
      )
    }

    const registration = await prisma.playerRegistration.create({
      data: {
        userId: actualUserId,
        gameId: parseInt(gameId, 10),
        gameUsername: gameUsername || `${userData?.username || 'player'}_${selectedGame?.name.toLowerCase() || 'game'}`
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        },
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Create or update player stats
    await prisma.playerStats.upsert({
      where: {
        userId_gameId: {
          userId: actualUserId,
          gameId: parseInt(gameId, 10)
        }
      },
      update: {},
      create: {
        userId: actualUserId,
        gameId: parseInt(gameId, 10)
      }
    })

    return NextResponse.json(registration, { status: 201 })
  } catch (error) {
    logger.error('Error creating registration:', error)
    return NextResponse.json(
      { error: 'Failed to create registration' },
      { status: 500 }
    )
  }
}
