import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    // Get total players
    const totalPlayers = await prisma.user.count({
      where: {
        role: 'PLAYER'
      }
    })

    // Get total registrations
    const totalRegistrations = await prisma.playerRegistration.count()

    // Get upcoming tournaments
    const upcomingTournaments = await prisma.tournamentSchedule.count({
      where: {
        status: 'SCHEDULED',
        scheduledDate: {
          gte: new Date()
        }
      }
    })

    // Get completed tournaments
    const completedTournaments = await prisma.weeklyTournament.count({
      where: {
        status: 'COMPLETED'
      }
    })

    return NextResponse.json({
      totalPlayers,
      totalRegistrations,
      upcomingTournaments,
      completedTournaments
    })
  } catch (error) {
    logger.error('Error fetching admin stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    )
  }
})
