#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

async function createAdmin() {
  const prisma = new PrismaClient()
  
  try {
    console.log('👑 Creating Admin User...')
    
    // Hash the admin password
    const hashedPassword = await bcrypt.hash('Bsvca2223', 12)
    
    // Create or update admin user
    const adminUser = await prisma.user.upsert({
      where: { username: 'Tournaowner' },
      update: {
        password: hashedPassword,
        role: 'ADMIN',
        isActive: true
      },
      create: {
        username: '<PERSON>naowner',
        firstName: 'Tournament',
        lastName: 'Owner',
        phoneNumber: '+265983132770',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ADMIN',
        isActive: true
      }
    })

    console.log('✅ Admin user created/updated successfully!')
    console.log(`   Username: ${adminUser.username}`)
    console.log(`   Name: ${adminUser.firstName} ${adminUser.lastName}`)
    console.log(`   Role: ${adminUser.role}`)
    console.log(`   Phone: ${adminUser.phoneNumber}`)
    console.log('\n🔑 Admin Login Credentials:')
    console.log('   Username: Tournaowner')
    console.log('   Password: Bsvca2223')
    console.log('\n🌐 Admin Login URL: http://localhost:3000/admin/login')

  } catch (error) {
    console.error('❌ Error creating admin user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAdmin()
