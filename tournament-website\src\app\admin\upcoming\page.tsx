'use client'

import { logger } from '@/lib/logger'
import { AdminAuthGuard } from '@/components/AdminAuthGuard'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Tournament {
  id: number
  gameId: number
  weekNumber: number
  year: number
  tournamentDate: string
  status: string
  winnerId?: number
  totalParticipants: number
  createdAt: string
  game: {
    id: number
    name: string
  }
  winner?: {
    id: number
    firstName: string
    lastName: string
    username: string
  }
}

function AdminUpcomingContent() {
  const [tournaments, setTournaments] = useState<Tournament[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGame, setSelectedGame] = useState<string>('')
  const [games, setGames] = useState<{id: number, name: string}[]>([])
  const router = useRouter()
  const { logout } = useAdminAuth()

  const fetchTournaments = async () => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/tournaments?status=upcoming', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setTournaments(data)
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error fetching tournaments:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchGames = async () => {
    try {
      const response = await fetch('/api/games')
      if (response.ok) {
        const data = await response.json()
        setGames(data)
      }
    } catch (error) {
      logger.error('Error fetching games:', error)
    }
  }

  useEffect(() => {
    // Check if admin is logged in
    const token = localStorage.getItem('adminToken')
    if (!token) {
      router.push('/admin/login')
      return
    }

    fetchTournaments()
    fetchGames()
  }, [router])

  const handleLogout = () => {
    logout()
  }

  const markAsCompleted = async (tournamentId: number) => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch(`/api/admin/tournaments/${tournamentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status: 'COMPLETED' }),
      })

      if (response.ok) {
        // Refresh the tournaments list
        fetchTournaments()
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error updating tournament:', error)
    }
  }

  const filteredTournaments = selectedGame 
    ? tournaments.filter(tournament => tournament.game.name === selectedGame)
    : tournaments

  const sortedTournaments = filteredTournaments.sort((a, b) => {
    // Sort by date (earliest first)
    const dateA = new Date(a.tournamentDate)
    const dateB = new Date(b.tournamentDate)
    return dateA.getTime() - dateB.getTime()
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading upcoming tournaments...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="text-3xl font-bold text-blue-600">
                eSports RXP Admin
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/admin/dashboard" className="text-gray-500 hover:text-gray-900">
                Dashboard
              </Link>
              <Link href="/admin/schedules" className="text-gray-500 hover:text-gray-900">
                Manage Schedules
              </Link>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Upcoming Tournaments</h1>
          <p className="text-gray-600">All planned and scheduled tournaments that haven't been completed yet</p>
        </div>

        {/* Filter */}
        <div className="mb-6">
          <label htmlFor="gameFilter" className="block text-sm font-medium text-gray-700 mb-2">
            Filter by Game
          </label>
          <select
            id="gameFilter"
            value={selectedGame}
            onChange={(e) => setSelectedGame(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Games</option>
            {games.map(game => (
              <option key={game.id} value={game.name}>{game.name}</option>
            ))}
          </select>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Upcoming</h3>
            <p className="text-3xl font-bold text-yellow-600">{tournaments.length}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Filtered Results</h3>
            <p className="text-3xl font-bold text-green-600">{filteredTournaments.length}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">This Week</h3>
            <p className="text-3xl font-bold text-blue-600">
              {tournaments.filter(t => {
                const tournamentDate = new Date(t.tournamentDate)
                const now = new Date()
                const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
                return tournamentDate >= now && tournamentDate <= weekFromNow
              }).length}
            </p>
          </div>
        </div>

        {/* Tournaments List */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Upcoming Tournaments {selectedGame && `- ${selectedGame}`}
            </h2>
          </div>
          
          {sortedTournaments.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No upcoming tournaments found {selectedGame && `for ${selectedGame}`}.
              <div className="mt-4">
                <Link 
                  href="/admin/schedules"
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  Create a new tournament schedule
                </Link>
              </div>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {sortedTournaments.map((tournament) => (
                <div key={tournament.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {tournament.game.name} Tournament - Week {tournament.weekNumber}, {tournament.year}
                        </h3>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {tournament.game.name}
                        </span>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          {tournament.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Date:</span>
                          <div className="text-gray-900">{new Date(tournament.tournamentDate).toLocaleDateString()}</div>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Week:</span>
                          <div className="text-gray-900">Week {tournament.weekNumber}</div>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Total Participants:</span>
                          <div className="text-gray-900">{tournament.totalParticipants}</div>
                        </div>
                      </div>
                    </div>
                    <div className="ml-6">
                      <button
                        onClick={() => markAsCompleted(tournament.id)}
                        className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-sm"
                      >
                        Mark as Completed
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default function AdminUpcomingPage() {
  return (
    <AdminAuthGuard>
      <AdminUpcomingContent />
    </AdminAuthGuard>
  )
}