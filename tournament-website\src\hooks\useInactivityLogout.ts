'use client'

import { useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { logger } from '@/lib/logger'


interface UseInactivityLogoutOptions {
  timeout?: number // Timeout in milliseconds (default: 30 minutes)
  warningTime?: number // Warning time before logout in milliseconds (default: 5 minutes)
  onWarning?: () => void // Callback when warning is shown
  onLogout?: () => void // Callback when logout occurs
  enabled?: boolean // Whether inactivity logout is enabled
}

export function useInactivityLogout(options: UseInactivityLogoutOptions = {}) {
  const {
    timeout = 30 * 60 * 1000, // 30 minutes
    warningTime = 5 * 60 * 1000, // 5 minutes
    onWarning,
    onLogout,
    enabled = true
  } = options

  const router = useRouter()
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const warningTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const lastActivityRef = useRef<number>(Date.now())

  const logout = useCallback(async () => {
    try {
      // Call logout API
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      })
      
      // Clear any stored tokens
      if (typeof window !== 'undefined') {
        localStorage.removeItem('adminToken')
      }
      
      // Call custom logout callback
      onLogout?.()
      
      // Redirect to login
      router.push('/login')
      
      logger.info('User logged out due to inactivity')
    } catch (error) {
      logger.error('Error during inactivity logout:', error)
      // Still redirect even if logout API fails
      router.push('/login')
    }
  }, [router, onLogout])

  const showWarning = useCallback(() => {
    onWarning?.()
    logger.info('Inactivity warning shown to user')
  }, [onWarning])

  const resetTimer = useCallback(() => {
    if (!enabled) return

    lastActivityRef.current = Date.now()

    // Clear existing timers
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current)
    }

    // Set warning timer
    warningTimeoutRef.current = setTimeout(() => {
      showWarning()
    }, timeout - warningTime)

    // Set logout timer
    timeoutRef.current = setTimeout(() => {
      logout()
    }, timeout)
  }, [enabled, timeout, warningTime, showWarning, logout])

  const handleActivity = useCallback(() => {
    resetTimer()
  }, [resetTimer])

  useEffect(() => {
    if (!enabled) return

    // Activity events to monitor
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click'
    ]

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true)
    })

    // Start the timer
    resetTimer()

    // Cleanup
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true)
      })
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current)
      }
    }
  }, [enabled, handleActivity, resetTimer])

  // Handle visibility change (tab switching)
  useEffect(() => {
    if (!enabled) return

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Check if we've been inactive too long while tab was hidden
        const timeSinceLastActivity = Date.now() - lastActivityRef.current
        
        if (timeSinceLastActivity >= timeout) {
          logout()
        } else {
          // Reset timer when tab becomes visible again
          resetTimer()
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [enabled, timeout, logout, resetTimer])

  return {
    resetTimer,
    getLastActivity: () => lastActivityRef.current,
    getTimeUntilLogout: () => {
      const timeSinceActivity = Date.now() - lastActivityRef.current
      return Math.max(0, timeout - timeSinceActivity)
    }
  }
}
