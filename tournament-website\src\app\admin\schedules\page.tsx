'use client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { logger } from '@/lib/logger'
import Link from 'next/link'
import { getCurrentTournamentWeek, getTournamentWeekForDate, getWeekDescription, getCurrentWeekendDates, isWeekendDate } from '@/lib/tournament-utils'
import { AdminAuthGuard } from '@/components/AdminAuthGuard'
import { useAdminAuth } from '@/hooks/useAdminAuth'

interface Schedule {
  id: number
  game: {
    id: number
    name: string
  }
  scheduledDate: string
  scheduledTime: string
  description: string | null
  status: string
}

function AdminSchedulesContent() {
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [games, setGames] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [currentWeek, setCurrentWeek] = useState(1)
  const [selectedWeek, setSelectedWeek] = useState<number | null>(null)
  const [newSchedule, setNewSchedule] = useState({
    gameId: '',
    scheduledDate: '',
    scheduledTime: '',
    description: ''
  })
  const router = useRouter()
  const { logout } = useAdminAuth()

  const fetchGames = async () => {
    try {
      const response = await fetch('/api/games')
      if (response.ok) {
        const data = await response.json()
        setGames(data)
      }
    } catch (error) {
      logger.error('Error fetching games:', error)
    }
  }

  const fetchSchedules = async () => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/schedules', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setSchedules(data)
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error fetching schedules:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Check if admin is logged in
    const token = localStorage.getItem('adminToken')
    if (!token) {
      router.push('/admin/login')
      return
    }

    fetchGames()
    fetchSchedules()
    setCurrentWeek(getCurrentTournamentWeek())
  }, [router])

  // Update selected week when date changes
  useEffect(() => {
    if (newSchedule.scheduledDate) {
      const selectedDate = new Date(newSchedule.scheduledDate)
      const weekForDate = getTournamentWeekForDate(selectedDate)
      setSelectedWeek(weekForDate)
    } else {
      setSelectedWeek(null)
    }
  }, [newSchedule.scheduledDate])

  const handleAddSchedule = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/schedules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          gameId: parseInt(newSchedule.gameId),
          scheduledDate: newSchedule.scheduledDate,
          scheduledTime: newSchedule.scheduledTime,
          description: newSchedule.description
        })
      })

      if (response.ok) {
        setNewSchedule({ gameId: '', scheduledDate: '', scheduledTime: '', description: '' })
        setShowAddForm(false)
        fetchSchedules()
        logger.info('✅ Schedule added successfully!')
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      } else {
        const errorData = await response.json()
        logger.error('❌ Error adding schedule:', errorData)
      }
    } catch (error) {
      logger.error('Error adding schedule:', error)
    }
  }

  const updateScheduleStatus = async (scheduleId: number, status: string) => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch(`/api/admin/schedules/${scheduleId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status })
      })

      if (response.ok) {
        fetchSchedules()
        logger.info('✅ Schedule status updated to: ${status}')
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error updating schedule:', error)
    }
  }

  const handleLogout = () => {
    logout()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading schedules...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="text-3xl font-bold text-blue-600">
                eSports RXP
              </Link>
              <span className="ml-4 px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                Manage Schedules
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowAddForm(!showAddForm)}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                Add Schedule
              </button>
              <Link
                href="/admin/dashboard"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Add Schedule Form */}
        {showAddForm && (
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Add New Schedule</h3>
              <div className="text-sm text-gray-600">
                <div>
                  <span className="font-medium">
                    {currentWeek === 0 ? 'Pre-season' : `Current Week: ${currentWeek}`}
                  </span>
                  {currentWeek > 0 && (
                    <span className="ml-2 text-xs text-gray-500">
                      ({getWeekDescription(currentWeek)})
                    </span>
                  )}
                </div>
                {selectedWeek !== null && (
                  <div className="mt-1">
                    Selected Date: <span className={`font-medium ${
                      selectedWeek === 0 ? 'text-gray-600' :
                      selectedWeek === currentWeek ? 'text-green-600' :
                      selectedWeek > currentWeek ? 'text-blue-600' : 'text-orange-600'
                    }`}>
                      {selectedWeek === 0 ? 'Pre-season' : `Week ${selectedWeek}`}
                    </span>
                    {selectedWeek === 0 && <span className="text-gray-600 ml-1">⚠ Before tournament season</span>}
                    {selectedWeek === currentWeek && selectedWeek > 0 && <span className="text-green-600 ml-1">✓ Current Weekend</span>}
                    {selectedWeek > currentWeek && <span className="text-blue-600 ml-1">→ Future Weekend</span>}
                    {selectedWeek < currentWeek && selectedWeek > 0 && <span className="text-orange-600 ml-1">⚠ Past Weekend</span>}
                  </div>
                )}
              </div>
            </div>
            <form onSubmit={handleAddSchedule} className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="schedule-game" className="block text-sm font-medium text-gray-700 mb-2">Game</label>
                <select
                  id="schedule-game"
                  name="gameId"
                  value={newSchedule.gameId}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, gameId: e.target.value }))}
                  required
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Select a game</option>
                  {games.map((game) => (
                    <option key={game.id} value={game.id}>
                      {game.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="schedule-date" className="block text-sm font-medium text-gray-700 mb-2">
                  Tournament Date
                  {selectedWeek !== null && newSchedule.scheduledDate && (
                    <span className="ml-2 text-xs">
                      ({selectedWeek === 0 ? 'Pre-season' : `Week ${selectedWeek}`}
                      {selectedWeek === 0 && <span className="text-gray-600"> - Before season starts</span>}
                      {selectedWeek === currentWeek && selectedWeek > 0 && <span className="text-green-600"> - Current Weekend</span>}
                      {selectedWeek > currentWeek && <span className="text-blue-600"> - Future Weekend</span>}
                      {selectedWeek < currentWeek && selectedWeek > 0 && <span className="text-orange-600"> - Past Weekend</span>}
                      {newSchedule.scheduledDate && (
                        <span className={`ml-1 ${isWeekendDate(new Date(newSchedule.scheduledDate)) ? 'text-green-600' : 'text-orange-600'}`}>
                          • {isWeekendDate(new Date(newSchedule.scheduledDate)) ? 'Weekend' : 'Weekday'}
                        </span>
                      )}
                      )
                    </span>
                  )}
                </label>
                <input
                  type="date"
                  id="schedule-date"
                  name="scheduledDate"
                  value={newSchedule.scheduledDate}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, scheduledDate: e.target.value }))}
                  required
                  className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 ${
                    selectedWeek === 0
                      ? 'border-gray-400 focus:ring-gray-500 bg-gray-50'
                      : selectedWeek !== null && selectedWeek < currentWeek
                        ? 'border-orange-300 focus:ring-orange-500 bg-orange-50'
                        : newSchedule.scheduledDate && !isWeekendDate(new Date(newSchedule.scheduledDate))
                          ? 'border-yellow-300 focus:ring-yellow-500 bg-yellow-50'
                          : 'border-gray-300 focus:ring-green-500'
                  }`}
                />
                {selectedWeek === 0 && (
                  <p className="text-xs text-gray-600 mt-1">
                    ⚠ Pre-season: Tournament season starts Saturday, January 12th, 2025. Consider scheduling for Week 1 or later.
                  </p>
                )}
                {selectedWeek !== null && selectedWeek < currentWeek && selectedWeek > 0 && (
                  <p className="text-xs text-orange-600 mt-1">
                    ⚠ Past Weekend: You're scheduling for Week {selectedWeek}. Consider scheduling for current weekend (Week {currentWeek}) or future weekends.
                  </p>
                )}
                {newSchedule.scheduledDate && !isWeekendDate(new Date(newSchedule.scheduledDate)) && (
                  <p className="text-xs text-yellow-600 mt-1">
                    💡 Weekday Selected: Tournaments typically run on weekends (Saturday-Sunday). Consider selecting a weekend date.
                  </p>
                )}
              </div>
              <div>
                <label htmlFor="schedule-time" className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                <input
                  type="time"
                  id="schedule-time"
                  name="scheduledTime"
                  value={newSchedule.scheduledTime}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, scheduledTime: e.target.value }))}
                  required
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                />
              </div>
              <div>
                <label htmlFor="schedule-description" className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <input
                  type="text"
                  id="schedule-description"
                  name="description"
                  value={newSchedule.description}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Tournament description"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                />
              </div>
              <div className="md:col-span-2">
                <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">📅 Week Information</h4>
                  <div className="text-xs text-blue-700 space-y-1">
                    <p><strong>Tournament Season:</strong> Starts Saturday, January 12th, 2025 (First Weekend)</p>
                    <p><strong>Current Status:</strong> {currentWeek === 0 ? 'Pre-season' : getWeekDescription(currentWeek)}</p>
                    <p><strong>Weekend Schedule:</strong> Tournaments run Saturday-Sunday each week</p>
                    <p><strong>Week Calculation:</strong> Based on weekends since January 12th (Saturday-Sunday = 1 week)</p>
                    {selectedWeek !== null && newSchedule.scheduledDate && (
                      <div className="mt-2 p-2 bg-blue-100 rounded">
                        <p><strong>Selected Date Analysis:</strong></p>
                        <p>• Date: {new Date(newSchedule.scheduledDate).toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })}</p>
                        <p>• Tournament Week: {selectedWeek === 0 ? 'Pre-season' : getWeekDescription(selectedWeek)}</p>
                        <p>• Day Type: {isWeekendDate(new Date(newSchedule.scheduledDate)) ? '✅ Weekend Day (Recommended)' : '⚠️ Weekday (Not typical for tournaments)'}</p>
                        {selectedWeek === currentWeek && selectedWeek > 0 && <p className="text-green-600">• ✅ Current Weekend - Perfect timing!</p>}
                        {selectedWeek > currentWeek && <p className="text-blue-600">• 📅 Future Weekend - Good for advance planning</p>}
                        {selectedWeek < currentWeek && selectedWeek > 0 && <p className="text-orange-600">• ⚠️ Past Weekend - Consider current or future weekends</p>}
                        {selectedWeek === 0 && <p className="text-gray-600">• ⚠️ Pre-season - Tournament season hasn't started yet</p>}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    Add Schedule
                  </button>
                </div>
              </div>
            </form>
          </div>
        )}

        {/* Schedules Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Tournament Schedules</h2>
            <p className="text-sm text-gray-600">Manage upcoming tournament schedules</p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Game
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time / Week
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {schedules.map((schedule) => (
                  <tr key={schedule.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        {schedule.game.name}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>
                        {new Date(schedule.scheduledDate).toLocaleDateString()} at {
                          schedule.scheduledTime.includes('T') || schedule.scheduledTime.includes('Z')
                            ? new Date(schedule.scheduledTime).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })
                            : schedule.scheduledTime
                        }
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {(() => {
                          const scheduleWeek = getTournamentWeekForDate(new Date(schedule.scheduledDate))
                          const scheduleDate = new Date(schedule.scheduledDate)
                          const isWeekend = isWeekendDate(scheduleDate)

                          return (
                            <>
                              {scheduleWeek === 0 ? 'Pre-season' : `Week ${scheduleWeek}`}
                              {scheduleWeek === currentWeek && scheduleWeek > 0 &&
                                <span className="text-green-600 ml-1">• Current Weekend</span>
                              }
                              {scheduleWeek > currentWeek &&
                                <span className="text-blue-600 ml-1">• Future Weekend</span>
                              }
                              {scheduleWeek < currentWeek && scheduleWeek > 0 &&
                                <span className="text-orange-600 ml-1">• Past Weekend</span>
                              }
                              {scheduleWeek === 0 &&
                                <span className="text-gray-600 ml-1">• Pre-season</span>
                              }
                              <br />
                              <span className={isWeekend ? 'text-green-600' : 'text-yellow-600'}>
                                {isWeekend ? '✅ Weekend' : '⚠️ Weekday'}
                              </span>
                            </>
                          )
                        })()}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {schedule.description || 'No description'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        schedule.status === 'SCHEDULED' ? 'bg-yellow-100 text-yellow-800' :
                        schedule.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {schedule.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <select
                        value={schedule.status}
                        onChange={(e) => updateScheduleStatus(schedule.id, e.target.value)}
                        className="text-sm border border-gray-300 rounded px-2 py-1"
                      >
                        <option value="SCHEDULED">Scheduled</option>
                        <option value="COMPLETED">Completed</option>
                        <option value="CANCELLED">Cancelled</option>
                      </select>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {schedules.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No schedules found. Add your first tournament schedule!</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default function AdminSchedulesPage() {
  return (
    <AdminAuthGuard>
      <AdminSchedulesContent />
    </AdminAuthGuard>
  )
}