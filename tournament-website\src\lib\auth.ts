import bcrypt from 'bcryptjs'
import crypto from 'crypto'
import { prisma } from '@/lib/db'

export interface AuthUser {
  userId: number
  username: string
  firstName: string
  lastName: string
  phoneNumber: string
  email?: string
  gender?: string
  address?: string
  role: string
}

export interface SessionData {
  userId: number
  username: string
  role: string
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12
  return bcrypt.hash(password, saltRounds)
}

// Verify password
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// Generate session token
export function generateSessionToken(): string {
  return crypto.randomBytes(32).toString('hex')
}

// Create user session
export async function createSession(userId: number): Promise<string> {
  const token = generateSessionToken()
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

  await prisma.userSession.create({
    data: {
      userId,
      token,
      expiresAt
    }
  })

  return token
}

// Validate session token
export async function validateSession(token: string): Promise<AuthUser | null> {
  if (!token) return null

  const session = await prisma.userSession.findUnique({
    where: { token },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          phoneNumber: true,
          email: true,
          gender: true,
          address: true,
          role: true,
          isActive: true
        }
      }
    }
  })

  if (!session || session.expiresAt < new Date() || !session.user.isActive) {
    // Clean up expired session
    if (session) {
      await prisma.userSession.delete({ where: { id: session.id } })
    }
    return null
  }

  return {
    userId: session.user.id,
    username: session.user.username,
    firstName: session.user.firstName,
    lastName: session.user.lastName,
    phoneNumber: session.user.phoneNumber,
    email: session.user.email || undefined,
    gender: session.user.gender || undefined,
    address: session.user.address || undefined,
    role: session.user.role
  }
}

// Delete session (logout)
export async function deleteSession(token: string): Promise<void> {
  await prisma.userSession.deleteMany({
    where: { token }
  })
}

// Clean up expired sessions
export async function cleanupExpiredSessions(): Promise<void> {
  await prisma.userSession.deleteMany({
    where: {
      expiresAt: {
        lt: new Date()
      }
    }
  })
}

// Validate admin session
export async function validateAdminSession(token: string): Promise<AuthUser | null> {
  const user = await validateSession(token)
  if (!user || user.role !== 'ADMIN') {
    return null
  }
  return user
}

// Authenticate user
export async function authenticateUser(username: string, password: string): Promise<AuthUser | null> {
  const user = await prisma.user.findUnique({
    where: { username },
    select: {
      id: true,
      username: true,
      firstName: true,
      lastName: true,
      phoneNumber: true,
      email: true,
      gender: true,
      address: true,
      password: true,
      role: true,
      isActive: true
    }
  })

  if (!user || !user.isActive) {
    return null
  }

  const isValidPassword = await verifyPassword(password, user.password)
  if (!isValidPassword) {
    return null
  }

  return {
    userId: user.id,
    username: user.username,
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.phoneNumber,
    email: user.email || undefined,
    gender: user.gender || undefined,
    address: user.address || undefined,
    role: user.role
  }
}


