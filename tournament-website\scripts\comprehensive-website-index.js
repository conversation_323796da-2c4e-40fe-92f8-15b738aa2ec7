#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🗂️  COMPREHENSIVE WEBSITE INDEX & SECURITY AUDIT')
console.log('================================================\n')

let totalFiles = 0
let totalLines = 0
let securityIssues = 0
let codeQualityIssues = 0

const fileIndex = {
  components: [],
  pages: [],
  apis: [],
  styles: [],
  configs: [],
  database: [],
  scripts: [],
  assets: [],
  security: []
}

function getFileStats(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const lines = content.split('\n').length
    const size = fs.statSync(filePath).size
    return { lines, size, content }
  } catch (error) {
    return { lines: 0, size: 0, content: '' }
  }
}

function analyzeSecurityIssues(content, filePath) {
  const issues = []
  
  // Check for hardcoded secrets
  const secretPatterns = [
    { pattern: /password\s*[:=]\s*["'][^"']+["']/gi, severity: 'HIGH', type: 'Hardcoded Password' },
    { pattern: /secret\s*[:=]\s*["'][^"']+["']/gi, severity: 'HIGH', type: 'Hardcoded Secret' },
    { pattern: /api[_-]?key\s*[:=]\s*["'][^"']+["']/gi, severity: 'HIGH', type: 'Hardcoded API Key' },
    { pattern: /token\s*[:=]\s*["'][^"']+["']/gi, severity: 'MEDIUM', type: 'Hardcoded Token' }
  ]
  
  secretPatterns.forEach(({ pattern, severity, type }) => {
    const matches = content.match(pattern)
    if (matches) {
      matches.forEach(match => {
        if (!match.includes('process.env') && !match.includes('${')) {
          issues.push({ type, severity, match: match.substring(0, 50) + '...', line: 'N/A' })
        }
      })
    }
  })
  
  // Check for SQL injection vulnerabilities
  if (content.includes('$queryRaw') || content.includes('$executeRaw')) {
    issues.push({ type: 'Potential SQL Injection', severity: 'HIGH', match: 'Raw SQL queries detected', line: 'N/A' })
  }
  
  // Check for XSS vulnerabilities
  if (content.includes('dangerouslySetInnerHTML')) {
    issues.push({ type: 'Potential XSS', severity: 'MEDIUM', match: 'dangerouslySetInnerHTML usage', line: 'N/A' })
  }
  
  // Check for console.log in production
  if (content.includes('console.log') && !filePath.includes('scripts/')) {
    issues.push({ type: 'Debug Code', severity: 'LOW', match: 'console.log statements', line: 'N/A' })
  }
  
  return issues
}

function indexDirectory(dir, category = 'other') {
  if (!fs.existsSync(dir)) return
  
  const files = fs.readdirSync(dir)
  
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      if (file === 'node_modules' || file === '.git' || file === '.next') continue
      
      let subCategory = category
      if (file === 'components') subCategory = 'components'
      else if (file === 'pages' || file === 'app') subCategory = 'pages'
      else if (file === 'api') subCategory = 'apis'
      else if (file === 'styles') subCategory = 'styles'
      else if (file === 'prisma') subCategory = 'database'
      else if (file === 'scripts') subCategory = 'scripts'
      
      indexDirectory(filePath, subCategory)
    } else {
      totalFiles++
      const stats = getFileStats(filePath)
      totalLines += stats.lines
      
      const fileInfo = {
        path: filePath,
        name: file,
        size: stats.size,
        lines: stats.lines,
        type: path.extname(file),
        category: category
      }
      
      // Analyze security issues
      if (file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js') || file.endsWith('.jsx')) {
        const securityIssuesFound = analyzeSecurityIssues(stats.content, filePath)
        if (securityIssuesFound.length > 0) {
          fileInfo.securityIssues = securityIssuesFound
          securityIssues += securityIssuesFound.length
        }
      }
      
      // Categorize files
      if (category === 'components') fileIndex.components.push(fileInfo)
      else if (category === 'pages') fileIndex.pages.push(fileInfo)
      else if (category === 'apis') fileIndex.apis.push(fileInfo)
      else if (category === 'styles') fileIndex.styles.push(fileInfo)
      else if (category === 'database') fileIndex.database.push(fileInfo)
      else if (category === 'scripts') fileIndex.scripts.push(fileInfo)
      else if (file.includes('config') || file.includes('.env')) fileIndex.configs.push(fileInfo)
      else if (file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.svg')) fileIndex.assets.push(fileInfo)
    }
  }
}

// Start indexing
console.log('📁 INDEXING WEBSITE FILES...')
indexDirectory(path.join(__dirname, '..', 'src'))
indexDirectory(path.join(__dirname, '..', 'prisma'), 'database')
indexDirectory(path.join(__dirname, '..', 'scripts'), 'scripts')
indexDirectory(path.join(__dirname, '..', 'public'), 'assets')

// Index config files
const configFiles = ['.env.local', '.env', 'package.json', 'tsconfig.json', 'next.config.js', '.gitignore']
configFiles.forEach(configFile => {
  const configPath = path.join(__dirname, '..', configFile)
  if (fs.existsSync(configPath)) {
    const stats = getFileStats(configPath)
    totalFiles++
    totalLines += stats.lines
    
    const fileInfo = {
      path: configPath,
      name: configFile,
      size: stats.size,
      lines: stats.lines,
      type: path.extname(configFile),
      category: 'configs'
    }
    
    // Special security check for env files
    if (configFile.includes('.env')) {
      const securityIssuesFound = analyzeSecurityIssues(stats.content, configPath)
      if (securityIssuesFound.length > 0) {
        fileInfo.securityIssues = securityIssuesFound
        securityIssues += securityIssuesFound.length
      }
    }
    
    fileIndex.configs.push(fileInfo)
  }
})

// Display results
console.log('\n📊 WEBSITE INDEX SUMMARY')
console.log('========================')
console.log(`📁 Total Files: ${totalFiles}`)
console.log(`📝 Total Lines of Code: ${totalLines.toLocaleString()}`)
console.log(`🔒 Security Issues Found: ${securityIssues}`)

console.log('\n📂 FILE BREAKDOWN BY CATEGORY')
console.log('=============================')
Object.entries(fileIndex).forEach(([category, files]) => {
  if (files.length > 0) {
    console.log(`\n${category.toUpperCase()}: ${files.length} files`)
    files.forEach(file => {
      const sizeKB = (file.size / 1024).toFixed(1)
      console.log(`  📄 ${file.name} (${file.lines} lines, ${sizeKB}KB)`)
      if (file.securityIssues && file.securityIssues.length > 0) {
        file.securityIssues.forEach(issue => {
          console.log(`    🚨 ${issue.severity}: ${issue.type} - ${issue.match}`)
        })
      }
    })
  }
})

// Security summary
console.log('\n🔒 SECURITY AUDIT RESULTS')
console.log('=========================')
if (securityIssues === 0) {
  console.log('✅ No security issues detected!')
} else {
  console.log(`🚨 ${securityIssues} security issues found - review above details`)
}

// Database files check
console.log('\n🗄️  DATABASE FILES')
console.log('==================')
fileIndex.database.forEach(file => {
  console.log(`📄 ${file.name} (${file.lines} lines)`)
})

// API endpoints summary
console.log('\n🌐 API ENDPOINTS')
console.log('================')
fileIndex.apis.forEach(file => {
  if (file.name === 'route.ts') {
    const pathParts = file.path.split(path.sep)
    const apiPath = pathParts.slice(pathParts.indexOf('api')).join('/')
    console.log(`🔗 /${apiPath.replace('/route.ts', '')}`)
  }
})

console.log('\n✅ WEBSITE INDEXING COMPLETE!')
console.log(`📊 Summary: ${totalFiles} files, ${totalLines.toLocaleString()} lines, ${securityIssues} security issues`)

process.exit(securityIssues > 0 ? 1 : 0)
