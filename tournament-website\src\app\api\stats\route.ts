import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const gameId = searchParams.get('gameId')
    const userId = searchParams.get('userId')

    const where: any = {}
    if (gameId) {
      const parsedGameId = parseInt(gameId, 10)
      if (isNaN(parsedGameId)) {
        return NextResponse.json({ error: 'Invalid game ID' }, { status: 400 })
      }
      where.gameId = parsedGameId
    }
    if (userId) {
      const parsedUserId = parseInt(userId, 10)
      if (isNaN(parsedUserId)) {
        return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 })
      }
      where.userId = parsedUserId
    }

    // Get existing stats (keep original functionality)
    const stats = await prisma.playerStats.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        },
        game: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: [
        { tournamentsWon: 'desc' },
        { winPercentage: 'desc' },
        { totalWins: 'desc' }
      ]
    })

    return NextResponse.json(stats)
  } catch (error) {
    logger.error('Error fetching stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stats' },
      { status: 500 }
    )
  }
}
