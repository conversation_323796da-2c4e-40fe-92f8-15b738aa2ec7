/**
 * Performance monitoring system for Mzuni Tournaments
 * Tracks server performance, database connections, and API response times
 */

import { logger } from './logger'

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  timestamp: Date
  metadata?: Record<string, any>
}

interface DatabaseMetrics {
  connectionCount: number
  activeQueries: number
  avgResponseTime: number
  errorRate: number
}

interface ServerMetrics {
  memoryUsage: NodeJS.MemoryUsage
  cpuUsage: number
  uptime: number
  requestCount: number
  errorCount: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private requestCount = 0
  private errorCount = 0
  private startTime = Date.now()
  private dbMetrics: DatabaseMetrics = {
    connectionCount: 0,
    activeQueries: 0,
    avgResponseTime: 0,
    errorRate: 0
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, unit: string, metadata?: Record<string, any>) {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      metadata
    }

    this.metrics.push(metric)

    // Keep only last 1000 metrics to prevent memory leaks
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }

    // Log significant performance issues
    if (this.isSignificantMetric(metric)) {
      logger.warn(`Performance Alert: ${name} = ${value}${unit}`, metadata)
    }
  }

  /**
   * Track API request timing
   */
  trackApiRequest(endpoint: string, method: string, startTime: number, success: boolean) {
    const duration = Date.now() - startTime
    this.requestCount++
    
    if (!success) {
      this.errorCount++
    }

    this.recordMetric('api_response_time', duration, 'ms', {
      endpoint,
      method,
      success
    })

    // Alert on slow requests
    if (duration > 5000) { // 5 seconds
      logger.warn(`Slow API request detected`, {
        endpoint,
        method,
        duration: `${duration}ms`
      })
    }
  }

  /**
   * Track database query performance
   */
  trackDatabaseQuery(query: string, duration: number, success: boolean) {
    this.recordMetric('db_query_time', duration, 'ms', {
      query: query.substring(0, 100), // Truncate long queries
      success
    })

    // Update database metrics
    this.dbMetrics.avgResponseTime = this.calculateAverageResponseTime()
    this.dbMetrics.errorRate = this.calculateErrorRate()

    // Alert on slow queries
    if (duration > 2000) { // 2 seconds
      logger.warn(`Slow database query detected`, {
        query: query.substring(0, 200),
        duration: `${duration}ms`
      })
    }
  }

  /**
   * Get current server metrics
   */
  getServerMetrics(): ServerMetrics {
    const memoryUsage = process.memoryUsage()
    const uptime = Date.now() - this.startTime

    return {
      memoryUsage,
      cpuUsage: process.cpuUsage().user / 1000000, // Convert to seconds
      uptime,
      requestCount: this.requestCount,
      errorCount: this.errorCount
    }
  }

  /**
   * Get database metrics
   */
  getDatabaseMetrics(): DatabaseMetrics {
    return { ...this.dbMetrics }
  }

  /**
   * Get recent metrics
   */
  getRecentMetrics(minutes: number = 5): PerformanceMetric[] {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000)
    return this.metrics.filter(metric => metric.timestamp > cutoff)
  }

  /**
   * Generate performance report
   */
  generateReport(): string {
    const serverMetrics = this.getServerMetrics()
    const dbMetrics = this.getDatabaseMetrics()
    const recentMetrics = this.getRecentMetrics(5)

    const report = `
MZUNI TOURNAMENTS - PERFORMANCE REPORT
=====================================
Generated: ${new Date().toISOString()}

SERVER METRICS:
- Memory Usage: ${Math.round(serverMetrics.memoryUsage.heapUsed / 1024 / 1024)}MB
- Uptime: ${Math.round(serverMetrics.uptime / 1000 / 60)}m
- Total Requests: ${serverMetrics.requestCount}
- Error Count: ${serverMetrics.errorCount}
- Error Rate: ${((serverMetrics.errorCount / serverMetrics.requestCount) * 100).toFixed(2)}%

DATABASE METRICS:
- Average Response Time: ${dbMetrics.avgResponseTime.toFixed(2)}ms
- Error Rate: ${(dbMetrics.errorRate * 100).toFixed(2)}%
- Active Queries: ${dbMetrics.activeQueries}

RECENT ACTIVITY (Last 5 minutes):
- Total Metrics: ${recentMetrics.length}
- API Requests: ${recentMetrics.filter(m => m.name === 'api_response_time').length}
- DB Queries: ${recentMetrics.filter(m => m.name === 'db_query_time').length}

PERFORMANCE ALERTS:
${this.getPerformanceAlerts().join('\n')}
`

    return report
  }

  /**
   * Check if metric indicates a performance issue
   */
  private isSignificantMetric(metric: PerformanceMetric): boolean {
    switch (metric.name) {
      case 'api_response_time':
        return metric.value > 3000 // 3 seconds
      case 'db_query_time':
        return metric.value > 1000 // 1 second
      case 'memory_usage':
        return metric.value > 500 // 500MB
      default:
        return false
    }
  }

  /**
   * Calculate average response time for recent queries
   */
  private calculateAverageResponseTime(): number {
    const dbMetrics = this.getRecentMetrics(5).filter(m => m.name === 'db_query_time')
    if (dbMetrics.length === 0) return 0
    
    const total = dbMetrics.reduce((sum, metric) => sum + metric.value, 0)
    return total / dbMetrics.length
  }

  /**
   * Calculate error rate for recent operations
   */
  private calculateErrorRate(): number {
    const recentMetrics = this.getRecentMetrics(5)
    const totalOperations = recentMetrics.length
    const errorOperations = recentMetrics.filter(m => 
      m.metadata?.success === false
    ).length
    
    return totalOperations > 0 ? errorOperations / totalOperations : 0
  }

  /**
   * Get current performance alerts
   */
  private getPerformanceAlerts(): string[] {
    const alerts: string[] = []
    const serverMetrics = this.getServerMetrics()
    
    // Memory usage alert
    if (serverMetrics.memoryUsage.heapUsed > 500 * 1024 * 1024) { // 500MB
      alerts.push(`⚠️  High memory usage: ${Math.round(serverMetrics.memoryUsage.heapUsed / 1024 / 1024)}MB`)
    }
    
    // Error rate alert
    const errorRate = serverMetrics.requestCount > 0 ? 
      (serverMetrics.errorCount / serverMetrics.requestCount) * 100 : 0
    if (errorRate > 5) { // 5% error rate
      alerts.push(`⚠️  High error rate: ${errorRate.toFixed(2)}%`)
    }
    
    // Database response time alert
    if (this.dbMetrics.avgResponseTime > 1000) { // 1 second
      alerts.push(`⚠️  Slow database responses: ${this.dbMetrics.avgResponseTime.toFixed(2)}ms`)
    }
    
    if (alerts.length === 0) {
      alerts.push('✅ No performance issues detected')
    }
    
    return alerts
  }

  /**
   * Start periodic monitoring
   */
  startMonitoring(intervalMinutes: number = 5) {
    setInterval(() => {
      const serverMetrics = this.getServerMetrics()
      
      // Record memory usage
      this.recordMetric('memory_usage', serverMetrics.memoryUsage.heapUsed / 1024 / 1024, 'MB')
      
      // Log performance summary
      logger.info('Performance Summary', {
        memoryMB: Math.round(serverMetrics.memoryUsage.heapUsed / 1024 / 1024),
        requestCount: serverMetrics.requestCount,
        errorCount: serverMetrics.errorCount,
        uptimeMinutes: Math.round(serverMetrics.uptime / 1000 / 60)
      })
      
    }, intervalMinutes * 60 * 1000)
    
    logger.info(`Performance monitoring started (interval: ${intervalMinutes}m)`)
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor()

// Export types for use in other modules
export type { PerformanceMetric, DatabaseMetrics, ServerMetrics }
