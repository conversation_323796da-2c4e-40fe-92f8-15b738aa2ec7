import { render, screen, waitFor } from '@testing-library/react'
import Home from '@/app/page'

// Mock fetch
global.fetch = jest.fn()

const mockFetch = fetch as jest.MockedFunction<typeof fetch>

describe('Home Page', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFetch.mockClear()
  })

  it('renders the main heading', async () => {
    // Mock API responses
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ user: null }),
      } as Response)

    render(<Home />)

    expect(screen.getByText('Mzuni Tournaments')).toBeInTheDocument()
    expect(screen.getByText('Mzuni Gaming Tournaments')).toBeInTheDocument()
  })

  it('displays games when loaded', async () => {
    const mockGames = [
      {
        id: 1,
        name: 'PUBG',
        description: 'PlayerUnknown\'s Battlegrounds',
        _count: { registrations: 5 }
      },
      {
        id: 2,
        name: 'Call of Duty',
        description: 'Call of Duty Mobile',
        _count: { registrations: 3 }
      }
    ]

    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockGames,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ user: null }),
      } as Response)

    render(<Home />)

    await waitFor(() => {
      expect(screen.getByText('PUBG')).toBeInTheDocument()
      expect(screen.getByText('Call of Duty')).toBeInTheDocument()
    })

    expect(screen.getByText('5')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
  })

  it('shows loading state initially', () => {
    mockFetch
      .mockImplementation(() => new Promise(() => {})) // Never resolves

    render(<Home />)

    expect(screen.getByText('Loading games...')).toBeInTheDocument()
  })

  it('displays sign up buttons for non-authenticated users', async () => {
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ user: null }),
      } as Response)

    render(<Home />)

    await waitFor(() => {
      expect(screen.getByText('Create Account')).toBeInTheDocument()
      expect(screen.getByText('Login')).toBeInTheDocument()
    })
  })
})
