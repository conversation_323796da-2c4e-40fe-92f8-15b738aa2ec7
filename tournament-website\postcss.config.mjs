const config = {
  plugins: {
    // Tailwind CSS processing
    "@tailwindcss/postcss": {},

    // Autoprefixer for browser compatibility
    autoprefixer: {},

    // Production optimizations
    ...(process.env.NODE_ENV === "production" && {
      // CSS minification and optimization
      cssnano: {
        preset: [
          "default",
          {
            // Optimize CSS for production
            discardComments: { removeAll: true },
            normalizeWhitespace: true,
            colormin: true,
            convertValues: true,
            discardDuplicates: true,
            discardEmpty: true,
            mergeRules: true,
            minifyFontValues: true,
            minifySelectors: true,
            reduceIdents: false, // Keep for CSS variables
            svgo: true,
            // Safe optimizations
            calc: { precision: 5 },
            zindex: false, // Don't optimize z-index values
          },
        ],
      },
    }),
  },
};

export default config;
