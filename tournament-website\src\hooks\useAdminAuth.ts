'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface AdminUser {
  username: string
  role: string
  firstName?: string
  lastName?: string
}

interface UseAdminAuthReturn {
  isAuthenticated: boolean
  isLoading: boolean
  user: AdminUser | null
  login: (username: string, password: string) => Promise<boolean>
  logout: () => void
  checkAuth: () => boolean
}

/**
 * Validate admin token format and expiry
 */
function validateTokenFormat(token: string): boolean {
  try {
    const decoded = Buffer.from(token, 'base64').toString('utf-8')
    const [username, timestamp] = decoded.split(':')
    
    if (!username || !timestamp) return false
    
    // Check if token is not too old (24 hours)
    const tokenTime = parseInt(timestamp)
    const now = Date.now()
    const maxAge = 24 * 60 * 60 * 1000 // 24 hours
    
    return (now - tokenTime) < maxAge
  } catch {
    return false
  }
}

/**
 * Custom hook for admin authentication
 */
export function useAdminAuth(): UseAdminAuthReturn {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [user, setUser] = useState<AdminUser | null>(null)
  const router = useRouter()

  /**
   * Check if user is authenticated
   */
  const checkAuth = (): boolean => {
    if (typeof window === 'undefined') return false
    
    const token = localStorage.getItem('adminToken')
    if (!token) return false
    
    if (!validateTokenFormat(token)) {
      localStorage.removeItem('adminToken')
      return false
    }
    
    return true
  }

  /**
   * Login function
   */
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      })

      if (response.ok) {
        const data = await response.json()
        localStorage.setItem('adminToken', data.token)
        
        // Set cookie for middleware
        document.cookie = `adminToken=${data.token}; path=/; max-age=${24 * 60 * 60}; secure; samesite=strict`
        
        setUser(data.user)
        setIsAuthenticated(true)
        return true
      }
      
      return false
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }

  /**
   * Logout function
   */
  const logout = () => {
    localStorage.removeItem('adminToken')
    
    // Clear cookie
    document.cookie = 'adminToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    
    setUser(null)
    setIsAuthenticated(false)
    router.push('/admin/login')
  }

  /**
   * Initialize authentication state
   */
  useEffect(() => {
    const initAuth = () => {
      const isAuth = checkAuth()
      setIsAuthenticated(isAuth)
      
      if (isAuth) {
        const token = localStorage.getItem('adminToken')
        if (token) {
          try {
            const decoded = Buffer.from(token, 'base64').toString('utf-8')
            const [username] = decoded.split(':')
            setUser({ username, role: 'ADMIN' })
            
            // Ensure cookie is set for middleware
            document.cookie = `adminToken=${token}; path=/; max-age=${24 * 60 * 60}; secure; samesite=strict`
          } catch (error) {
            console.error('Error decoding token:', error)
            logout()
          }
        }
      }
      
      setIsLoading(false)
    }

    initAuth()
  }, [])

  /**
   * Set up token validation interval
   */
  useEffect(() => {
    if (!isAuthenticated) return

    const interval = setInterval(() => {
      if (!checkAuth()) {
        logout()
      }
    }, 60000) // Check every minute

    return () => clearInterval(interval)
  }, [isAuthenticated])

  return {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout,
    checkAuth
  }
}
