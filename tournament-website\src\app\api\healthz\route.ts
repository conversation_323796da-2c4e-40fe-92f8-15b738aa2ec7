import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

/**
 * Simple health check endpoint for Render
 * Returns 200 OK if service is healthy, 503 if not
 * Minimal response for fast health checks
 */
export async function GET() {
  try {
    // Quick database ping with short timeout
    await Promise.race([
      prisma.$queryRaw`SELECT 1`,
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Timeout')), 2000)
      )
    ])
    
    return NextResponse.json({ 
      status: 'ok',
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
    
  } catch (error) {
    return NextResponse.json({ 
      status: 'error',
      timestamp: new Date().toISOString()
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}

/**
 * HEAD request for even faster health checks
 */
export async function HEAD() {
  try {
    await Promise.race([
      prisma.$queryRaw`SELECT 1`,
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Timeout')), 2000)
      )
    ])
    
    return new NextResponse(null, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
    
  } catch (error) {
    return new NextResponse(null, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}
