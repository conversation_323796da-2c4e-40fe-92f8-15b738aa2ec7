#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')

async function diagnoseSchedules() {
  const prisma = new PrismaClient()
  
  try {
    console.log('🔍 DIAGNOSING SCHEDULE ISSUES')
    console.log('============================\n')

    // Get all schedules from database
    const schedules = await prisma.tournamentSchedule.findMany({
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: [
        { scheduledDate: 'asc' },
        { scheduledTime: 'asc' }
      ]
    })

    console.log(`📊 Total schedules in database: ${schedules.length}\n`)

    if (schedules.length === 0) {
      console.log('❌ No schedules found in database!')
      console.log('💡 Solution: Admin needs to create schedules at http://localhost:3000/admin/schedules')
      return
    }

    // Analyze each schedule
    schedules.forEach((schedule, index) => {
      console.log(`📅 Schedule ${index + 1}:`)
      console.log(`   ID: ${schedule.id}`)
      console.log(`   Game: ${schedule.game.name}`)
      console.log(`   Date: ${schedule.scheduledDate}`)
      console.log(`   Time: ${schedule.scheduledTime}`)
      console.log(`   Status: ${schedule.status}`)
      console.log(`   Description: ${schedule.description || 'None'}`)
      
      // Check if it's scheduled
      const isScheduled = schedule.status === 'SCHEDULED'
      console.log(`   Is Scheduled: ${isScheduled ? '✅ YES' : '❌ NO (' + schedule.status + ')'}`)
      
      // Check if it's upcoming
      try {
        const scheduleDate = new Date(schedule.scheduledDate)
        const now = new Date()
        const isUpcoming = scheduleDate >= now
        console.log(`   Is Upcoming: ${isUpcoming ? '✅ YES' : '❌ NO (Past date)'}`)
        
        if (!isUpcoming) {
          const daysDiff = Math.floor((now - scheduleDate) / (1000 * 60 * 60 * 24))
          console.log(`   Days ago: ${daysDiff}`)
        }
      } catch (error) {
        console.log(`   Date parsing error: ${error.message}`)
      }
      
      console.log('')
    })

    // Check what would be shown on frontend
    const scheduledOnly = schedules.filter(s => s.status === 'SCHEDULED')
    console.log(`🔍 Schedules with SCHEDULED status: ${scheduledOnly.length}`)
    
    const upcomingScheduled = scheduledOnly.filter(schedule => {
      try {
        const scheduleDate = new Date(schedule.scheduledDate)
        const now = new Date()
        return scheduleDate >= now
      } catch (error) {
        return false
      }
    })
    console.log(`🔍 Upcoming SCHEDULED schedules: ${upcomingScheduled.length}`)

    if (upcomingScheduled.length === 0) {
      console.log('\n❌ PROBLEM IDENTIFIED:')
      if (scheduledOnly.length === 0) {
        console.log('   No schedules have SCHEDULED status')
        console.log('   Current statuses:', [...new Set(schedules.map(s => s.status))])
      } else {
        console.log('   All SCHEDULED tournaments are in the past')
      }
      
      console.log('\n💡 SOLUTIONS:')
      console.log('   1. Admin can update schedule statuses to SCHEDULED')
      console.log('   2. Admin can create new schedules with future dates')
      console.log('   3. Admin can modify existing schedule dates')
      console.log('   🔗 Admin panel: http://localhost:3000/admin/schedules')
    } else {
      console.log('\n✅ SCHEDULES SHOULD BE VISIBLE')
      console.log('   If they\'re not showing, there might be a frontend issue')
    }

    // Show current date for reference
    console.log(`\n📅 Current date/time: ${new Date().toISOString()}`)

  } catch (error) {
    console.error('❌ Error diagnosing schedules:', error)
  } finally {
    await prisma.$disconnect()
  }
}

diagnoseSchedules()
