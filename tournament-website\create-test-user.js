#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

async function createTestUser() {
  const prisma = new PrismaClient()
  
  try {
    console.log('👤 Creating Test User...')
    
    // Hash the test user password
    const hashedPassword = await bcrypt.hash('testpass123', 12)
    
    // Create or update test user
    const testUser = await prisma.user.upsert({
      where: { username: 'testuser' },
      update: {
        password: hashedPassword,
        role: 'PLAYER',
        isActive: true
      },
      create: {
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '+265999123456',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'PLAYER',
        isActive: true,
        gender: 'MALE',
        address: 'Test Address, Lilongwe'
      }
    })

    console.log('✅ Test user created/updated successfully!')
    console.log(`   Username: ${testUser.username}`)
    console.log(`   Name: ${testUser.firstName} ${testUser.lastName}`)
    console.log(`   Role: ${testUser.role}`)
    console.log(`   Phone: ${testUser.phoneNumber}`)
    console.log('\n🔑 Test User Login Credentials:')
    console.log('   Username: testuser')
    console.log('   Password: testpass123')
    console.log('\n🌐 User Login URL: http://localhost:3000/login')

  } catch (error) {
    console.error('❌ Error creating test user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUser()
