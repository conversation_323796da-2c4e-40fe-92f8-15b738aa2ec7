const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function clearSchedulesAnnouncementsAndResults() {
  console.log('🗑️  Starting to clear schedules, announcements, and recent tournament results...\n')

  try {
    // Step 1: Clear weekly winners (tournament results)
    console.log('1️⃣ Clearing weekly winners (tournament results)...')
    const deletedWinners = await prisma.weeklyWinner.deleteMany({})
    console.log(`✅ Deleted ${deletedWinners.count} weekly winners`)

    // Step 2: Clear weekly tournaments (recent tournaments)
    console.log('\n2️⃣ Clearing weekly tournaments (recent tournaments)...')
    const deletedTournaments = await prisma.weeklyTournament.deleteMany({})
    console.log(`✅ Deleted ${deletedTournaments.count} weekly tournaments`)

    // Step 3: Clear tournament schedules
    console.log('\n3️⃣ Clearing tournament schedules...')
    const deletedSchedules = await prisma.tournamentSchedule.deleteMany({})
    console.log(`✅ Deleted ${deletedSchedules.count} tournament schedules`)

    // Step 4: Clear announcements
    console.log('\n4️⃣ Clearing announcements...')
    const deletedAnnouncements = await prisma.announcement.deleteMany({})
    console.log(`✅ Deleted ${deletedAnnouncements.count} announcements`)

    // Step 5: Verify what's preserved
    console.log('\n5️⃣ Verifying preserved data...')
    
    const userCount = await prisma.user.count()
    console.log(`✅ Preserved ${userCount} user accounts`)
    
    const gameCount = await prisma.game.count()
    console.log(`✅ Preserved ${gameCount} games`)
    
    const registrationCount = await prisma.playerRegistration.count()
    console.log(`✅ Preserved ${registrationCount} player registrations`)
    
    const statsCount = await prisma.playerStats.count()
    console.log(`✅ Preserved ${statsCount} player stats records`)
    
    const adminCount = await prisma.user.count({
      where: { role: 'ADMIN' }
    })
    console.log(`✅ Preserved ${adminCount} admin accounts`)

    console.log('\n🎉 Successfully cleared schedules, announcements, and recent tournament results!')
    console.log('📊 All other data (users, games, registrations, stats) has been preserved.')
    
  } catch (error) {
    console.error('❌ Error clearing schedules, announcements, and tournament results:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
clearSchedulesAnnouncementsAndResults()
  .then(() => {
    console.log('\n✨ Script completed successfully!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Script failed:', error)
    process.exit(1)
  })
