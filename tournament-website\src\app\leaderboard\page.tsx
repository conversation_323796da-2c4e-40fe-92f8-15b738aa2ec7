'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { logger } from '@/lib/logger'

interface Game {
  id: number
  name: string
  _count?: {
    registrations: number
  }
}

interface PlayerStats {
  id: number
  user: {
    id: number
    username: string
    firstName: string
    lastName: string
  }
  game: {
    id: number
    name: string
  }
  tournamentsParticipated: number
  tournamentsWon: number
  totalWins: number
  totalLosses: number
  winPercentage: number
}

interface WeeklyTournament {
  id: number
  gameId: number
  weekNumber: number
  year: number
  tournamentDate: string
  status: string
  totalParticipants: number
  game: {
    name: string
  }
  winner: {
    firstName: string
    lastName: string
    username: string
  } | null
}

export default function LeaderboardPage() {
  const [games, setGames] = useState<Game[]>([])
  const [selectedGame, setSelectedGame] = useState<string>('all')
  const [viewType, setViewType] = useState<'cumulative' | 'weekly'>('cumulative')
  const [activeTab, setActiveTab] = useState<'rankings' | 'gameStats' | 'recentWinners'>('rankings')
  const [selectedWeek, setSelectedWeek] = useState<string>('')
  const [playerStats, setPlayerStats] = useState<PlayerStats[]>([])
  const [weeklyTournaments, setWeeklyTournaments] = useState<WeeklyTournament[]>([])
  const [recentWinners, setRecentWinners] = useState<WeeklyTournament[]>([])
  const [availableWeeks, setAvailableWeeks] = useState<{week: number, year: number}[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [playersPerPage] = useState(20)
  const [searchTerm, setSearchTerm] = useState('')

  const fetchGames = async () => {
    try {
      const response = await fetch('/api/games')
      if (response.ok) {
        const data = await response.json()
        setGames(data)
      }
    } catch (error) {
      logger.error('Error fetching games:', error)
    }
  }

  const fetchRecentWinners = async () => {
    try {
      const response = await fetch('/api/weekly-tournaments?status=COMPLETED')
      if (response.ok) {
        const data = await response.json()
        setRecentWinners(data.slice(0, 10)) // Get last 10 winners
      }
    } catch (error) {
      logger.error('Error fetching recent winners:', error)
    }
  }

  const fetchPlayerStats = async () => {
    try {
      const url = selectedGame === 'all' ? '/api/stats' : `/api/stats?gameId=${selectedGame}`
      const response = await fetch(url)
      if (response.ok) {
        const data = await response.json()
        setPlayerStats(data)
      }
    } catch (error) {
      logger.error('Error fetching player stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchWeeklyTournaments = async () => {
    try {
      const currentYear = new Date().getFullYear()
      let url = `/api/weekly-tournaments?year=${currentYear}&status=COMPLETED`
      if (selectedGame !== 'all') {
        url += `&gameId=${selectedGame}`
      }

      const response = await fetch(url)
      if (response.ok) {
        const data = await response.json()
        setWeeklyTournaments(data)
      }
    } catch (error) {
      logger.error('Error fetching weekly tournaments:', error)
    }
  }

  const fetchAvailableWeeks = async () => {
    try {
      const currentYear = new Date().getFullYear()
      let url = `/api/weekly-tournaments?year=${currentYear}&status=COMPLETED`
      if (selectedGame !== 'all') {
        url += `&gameId=${selectedGame}`
      }

      const response = await fetch(url)
      if (response.ok) {
        const data = await response.json()
        const weeks = data.map((t: WeeklyTournament) => ({
          week: t.weekNumber,
          year: t.year
        }))
        // Remove duplicates and sort
        const uniqueWeeks = weeks.filter((week: any, index: number, self: any[]) =>
          index === self.findIndex((w) => w.week === week.week && w.year === week.year)
        ).sort((a: any, b: any) => b.week - a.week)

        setAvailableWeeks(uniqueWeeks)
        if (uniqueWeeks.length > 0 && !selectedWeek) {
          setSelectedWeek(`${uniqueWeeks[0].week}-${uniqueWeeks[0].year}`)
        }
      }
    } catch (error) {
      logger.error('Error fetching available weeks:', error)
    }
  }

  useEffect(() => {
    fetchGames()
    fetchPlayerStats()
    fetchWeeklyTournaments()
    fetchRecentWinners()
  }, [selectedGame])

  useEffect(() => {
    if (viewType === 'weekly') {
      fetchAvailableWeeks()
    }
  }, [viewType, selectedGame])

  const getWeeklyLeaderboard = () => {
    if (!selectedWeek) return []
    
    const [week, year] = selectedWeek.split('-').map(Number)
    return weeklyTournaments
      .filter(t => t.weekNumber === week && t.year === year)
      .sort((a, b) => {
        // Winners first, then by total participants (bigger tournaments first)
        if (a.winner && !b.winner) return -1
        if (!a.winner && b.winner) return 1
        return b.totalParticipants - a.totalParticipants
      })
  }

  const getCumulativeLeaderboard = () => {
    if (playerStats.length === 0) return []

    // Filter by search term first
    let filteredStats = playerStats
    if (searchTerm) {
      filteredStats = playerStats.filter(stat =>
        stat.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stat.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stat.user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stat.game.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Only include players who have actually participated in tournaments or have meaningful stats
    // Exclude players with zero participation unless they have registrations
    const meaningfulStats = filteredStats.filter(stat =>
      stat.tournamentsParticipated > 0 ||
      stat.tournamentsWon > 0 ||
      stat.totalWins > 0 ||
      (stat.game.name !== 'Not Registered' && stat.tournamentsParticipated === 0)
    )

    // Group players by game and rank within each game
    const gameGroups: { [key: string]: PlayerStats[] } = {}

    meaningfulStats.forEach(stat => {
      const gameName = stat.game.name
      if (!gameGroups[gameName]) {
        gameGroups[gameName] = []
      }
      gameGroups[gameName].push(stat)
    })

    // Sort players within each game
    Object.keys(gameGroups).forEach(gameName => {
      gameGroups[gameName].sort((a, b) => {
        // First priority: Players who have actually participated in tournaments
        const aHasParticipated = a.tournamentsParticipated > 0 || a.tournamentsWon > 0 || a.totalWins > 0
        const bHasParticipated = b.tournamentsParticipated > 0 || b.tournamentsWon > 0 || b.totalWins > 0

        if (aHasParticipated !== bHasParticipated) {
          return bHasParticipated ? 1 : -1 // Participated players first
        }

        // Second priority: Sort by tournaments won
        if (b.tournamentsWon !== a.tournamentsWon) {
          return b.tournamentsWon - a.tournamentsWon
        }

        // Third priority: Sort by win percentage (only meaningful if they've played)
        if (aHasParticipated && bHasParticipated && b.winPercentage !== a.winPercentage) {
          return b.winPercentage - a.winPercentage
        }

        // Fourth priority: Sort by total wins
        if (b.totalWins !== a.totalWins) {
          return b.totalWins - a.totalWins
        }

        // Final priority: Sort by tournaments participated (engagement level)
        return b.tournamentsParticipated - a.tournamentsParticipated
      })
    })

    // Interleave rankings: #1 from each game, then #2 from each game, etc.
    const result: PlayerStats[] = []
    const maxRankings = Math.max(...Object.values(gameGroups).map(group => group.length))

    for (let rank = 0; rank < maxRankings; rank++) {
      Object.keys(gameGroups).sort().forEach(gameName => {
        if (gameGroups[gameName][rank]) {
          result.push(gameGroups[gameName][rank])
        }
      })
    }

    return result
  }

  const getPaginatedLeaderboard = () => {
    const leaderboard = getCumulativeLeaderboard()
    const startIndex = (currentPage - 1) * playersPerPage
    const endIndex = startIndex + playersPerPage
    return leaderboard.slice(startIndex, endIndex)
  }

  const getTotalPages = () => {
    const leaderboard = getCumulativeLeaderboard()
    return Math.ceil(leaderboard.length / playersPerPage)
  }

  const hasPlayedGames = (stats: PlayerStats) => {
    return stats.tournamentsParticipated > 0 || stats.totalWins > 0 || stats.totalLosses > 0
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading leaderboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-3xl font-bold text-blue-600">eSports RXP</Link>
              <span className="ml-4 px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                Leaderboard
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-gray-500 hover:text-gray-900">Home</Link>
              <Link href="/schedule" className="text-gray-500 hover:text-gray-900">Schedule</Link>
              <Link href="/how-it-works" className="text-gray-500 hover:text-gray-900">How It Works</Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Tournament Leaderboard</h1>
          <p className="text-lg text-gray-600">
            Complete tournament statistics, rankings, and performance tracking across all games.
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('rankings')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'rankings'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              🏆 Rankings & Leaderboard
            </button>
            <button
              onClick={() => setActiveTab('gameStats')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'gameStats'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              📊 Game Statistics
            </button>
            <button
              onClick={() => setActiveTab('recentWinners')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'recentWinners'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              🎉 Recent Winners
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'rankings' && (
          <>
            {/* Controls */}
            <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Game Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Game</label>
              <select
                value={selectedGame}
                onChange={(e) => {
                  setSelectedGame(e.target.value)
                  setCurrentPage(1) // Reset to first page when filtering
                }}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Games</option>
                {games.map((game) => (
                  <option key={game.id} value={game.id.toString()}>
                    {game.name}
                  </option>
                ))}
              </select>
            </div>

            {/* View Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">View Type</label>
              <select
                value={viewType}
                onChange={(e) => {
                  setViewType(e.target.value as 'cumulative' | 'weekly')
                  setCurrentPage(1)
                }}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="cumulative">Cumulative Rankings</option>
                <option value="weekly">Weekly Results</option>
              </select>
            </div>

            {/* Search (only for cumulative view) */}
            {viewType === 'cumulative' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Search Players</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value)
                    setCurrentPage(1)
                  }}
                  placeholder="Name, username, or game..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            )}

            {/* Week Filter (only for weekly view) */}
            {viewType === 'weekly' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Week</label>
                <select
                  value={selectedWeek}
                  onChange={(e) => setSelectedWeek(e.target.value)}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Week</option>
                  {availableWeeks.map((week) => (
                    <option key={`${week.week}-${week.year}`} value={`${week.week}-${week.year}`}>
                      Week {week.week}, {week.year}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Stats (for cumulative view) */}
            {viewType === 'cumulative' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Stats</label>
                <div className="text-sm text-gray-600">
                  <p>Total: {getCumulativeLeaderboard().length} players</p>
                  <p>Page: {currentPage} of {getTotalPages()}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Leaderboard Content */}
        {viewType === 'cumulative' ? (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Cumulative Rankings</h2>
              <p className="text-sm text-gray-600">
                Rankings by game: #1 players from each game, then #2 players, and so on
              </p>
              <div className="mt-2 text-xs text-blue-600">
                🏆 = Game champion | Rankings are grouped by game performance
              </div>
            </div>
            
            {getCumulativeLeaderboard().length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">🏆</div>
                <h3 className="text-xl font-medium text-gray-900 mb-2">No Rankings Available</h3>
                <p className="text-gray-600">No games have been played yet. Rankings will appear after tournaments are completed.</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Player</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Game</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tournaments</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wins/Losses</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Win Rate</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {getPaginatedLeaderboard().map((stats, index) => {
                      // Calculate game-specific rank and overall rank
                      const allLeaderboard = getCumulativeLeaderboard()
                      const gameStats = allLeaderboard.filter(s => s.game.name === stats.game.name)
                      const gameRank = gameStats.findIndex(s => s.id === stats.id) + 1
                      const overallRank = allLeaderboard.findIndex(s => s.id === stats.id) + 1
                      const isGameWinner = gameRank === 1

                      return (
                        <tr key={stats.id} className={isGameWinner ? 'bg-yellow-50' : ''}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {isGameWinner && <span className="text-2xl mr-2">🏆</span>}
                              <div>
                                <span className="text-sm font-medium text-gray-900">
                                  {stats.game.name} #{gameRank}
                                </span>
                                <div className="text-xs text-gray-500">
                                  Overall #{overallRank}
                                </div>
                              </div>
                            </div>
                          </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {stats.user.firstName} {stats.user.lastName}
                            </div>
                            <div className="text-sm text-gray-500">@{stats.user.username}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {stats.game.name}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {hasPlayedGames(stats) ? (
                            <span>{stats.tournamentsParticipated} played / {stats.tournamentsWon} won</span>
                          ) : (
                            <span className="text-gray-400 italic">No games played</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {hasPlayedGames(stats) ? (
                            <span>{stats.totalWins}W - {stats.totalLosses}L</span>
                          ) : (
                            <span className="text-gray-400 italic">No actual rank</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {hasPlayedGames(stats) ? (
                            <span className="font-medium">{Number(stats.winPercentage).toFixed(1)}%</span>
                          ) : (
                            <span className="text-gray-400 italic">N/A</span>
                          )}
                        </td>
                      </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            )}

            {/* Pagination Controls */}
            {viewType === 'cumulative' && getTotalPages() > 1 && (
              <div className="px-6 py-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {((currentPage - 1) * playersPerPage) + 1} to {Math.min(currentPage * playersPerPage, getCumulativeLeaderboard().length)} of {getCumulativeLeaderboard().length} players
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, getTotalPages()) }, (_, i) => {
                        let pageNum
                        if (getTotalPages() <= 5) {
                          pageNum = i + 1
                        } else if (currentPage <= 3) {
                          pageNum = i + 1
                        } else if (currentPage >= getTotalPages() - 2) {
                          pageNum = getTotalPages() - 4 + i
                        } else {
                          pageNum = currentPage - 2 + i
                        }

                        return (
                          <button
                            key={pageNum}
                            onClick={() => setCurrentPage(pageNum)}
                            className={`px-3 py-1 text-sm border rounded-md ${
                              currentPage === pageNum
                                ? 'bg-blue-600 text-white border-blue-600'
                                : 'border-gray-300 hover:bg-gray-50'
                            }`}
                          >
                            {pageNum}
                          </button>
                        )
                      })}
                    </div>

                    <button
                      onClick={() => setCurrentPage(Math.min(getTotalPages(), currentPage + 1))}
                      disabled={currentPage === getTotalPages()}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Weekly Results {selectedWeek && `- Week ${selectedWeek.split('-')[0]}, ${selectedWeek.split('-')[1]}`}
              </h2>
              <p className="text-sm text-gray-600">Tournament results and positions for the selected week</p>
            </div>
            
            {getWeeklyLeaderboard().length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📅</div>
                <h3 className="text-xl font-medium text-gray-900 mb-2">No Results Available</h3>
                <p className="text-gray-600">
                  {selectedWeek ? 'No tournaments were completed for the selected week.' : 'Please select a week to view results.'}
                </p>
              </div>
            ) : (
              <div className="space-y-4 p-6">
                {getWeeklyLeaderboard().map((tournament, index) => (
                  <div key={tournament.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-lg font-semibold text-gray-900">{tournament.game.name} Tournament</h3>
                      <span className="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                        {tournament.totalParticipants} participants
                      </span>
                    </div>
                    
                    {tournament.winner ? (
                      <div className="flex items-center">
                        <span className="text-2xl mr-3">🏆</span>
                        <div>
                          <p className="text-lg font-medium text-gray-900">
                            {tournament.winner.firstName} {tournament.winner.lastName}
                          </p>
                          <p className="text-sm text-gray-500">@{tournament.winner.username}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center text-gray-500">
                        <span className="text-2xl mr-3">❓</span>
                        <p>Winner not yet determined</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        </>
        )}

        {/* Game Statistics Tab */}
        {activeTab === 'gameStats' && (
          <div className="space-y-8">
            {/* Game Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {games.map((game) => (
                <div key={game.id} className="bg-white rounded-lg shadow-md p-6 text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-xl font-bold text-blue-600">{game.name.charAt(0)}</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{game.name}</h3>
                  <div className="text-3xl font-bold text-blue-600 mb-1">
                    {game._count?.registrations || 0}
                  </div>
                  <p className="text-sm text-gray-600">Total Registrations</p>
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="text-sm text-gray-600">
                      <p className="mb-1">Active Players: {playerStats.filter(s => s.game.id === game.id && (s.tournamentsParticipated > 0 || s.totalWins > 0)).length}</p>
                      <p>Tournament Participants: {playerStats.filter(s => s.game.id === game.id && s.tournamentsParticipated > 0).length}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Game Performance Summary */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Game Performance Summary</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Game</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Players</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active Players</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tournaments Played</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Wins</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {games.map((game) => {
                      const gameStats = playerStats.filter(s => s.game.id === game.id)
                      const activePlayers = gameStats.filter(s => s.tournamentsParticipated > 0 || s.totalWins > 0)
                      const totalTournaments = gameStats.reduce((sum, s) => sum + s.tournamentsParticipated, 0)
                      const totalWins = gameStats.reduce((sum, s) => sum + s.totalWins, 0)

                      return (
                        <tr key={game.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                              {game.name}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {game._count?.registrations || 0}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {activePlayers.length}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {totalTournaments}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {totalWins}
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Recent Winners Tab */}
        {activeTab === 'recentWinners' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Tournament Winners</h3>
              {recentWinners.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-gray-400 text-6xl mb-4">🏆</div>
                  <p className="text-gray-600">No tournament winners yet.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {recentWinners.filter(winner => winner.winner).map((winner) => (
                    <div key={winner.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-lg font-semibold text-gray-900">
                          {winner.game.name}
                        </span>
                        <span className="text-2xl">🏆</span>
                      </div>
                      <div className="text-sm text-gray-600 mb-1">
                        Week {winner.weekNumber}, {winner.year}
                      </div>
                      <div className="text-lg font-medium text-blue-600 mb-1">
                        {winner.winner?.username || 'Unknown Winner'}
                      </div>
                      <div className="text-sm text-gray-500 mb-2">
                        {winner.winner?.firstName} {winner.winner?.lastName}
                      </div>
                      <div className="text-xs text-gray-400">
                        {winner.totalParticipants} participants • {new Date(winner.tournamentDate).toLocaleDateString()}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
