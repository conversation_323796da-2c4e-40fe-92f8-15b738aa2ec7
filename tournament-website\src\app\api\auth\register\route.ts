import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { hashPassword, createSession } from '@/lib/auth'
import { cookies } from 'next/headers'
import { logger } from '@/lib/logger'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      username,
      password,
      firstName,
      lastName,
      phoneNumber,
      email,
      gender,
      address
    } = body

    // Validation
    if (!username || !password || !firstName || !lastName || !phoneNumber) {
      return NextResponse.json(
        { error: 'Username, password, first name, last name, and phone number are required' },
        { status: 400 }
      )
    }

    // Validate phone number format (Malawi)
    const phoneRegex = /^\+265[0-9]{9}$/
    if (!phoneRegex.test(phoneNumber)) {
      return NextResponse.json(
        { error: 'Phone number must be in format +265XXXXXXXXX' },
        { status: 400 }
      )
    }

    // Check if username already exists
    const existingUser = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'Username already exists' },
        { status: 409 }
      )
    }

    // Check if phone number already exists
    const existingPhone = await prisma.user.findFirst({
      where: { phoneNumber }
    })

    if (existingPhone) {
      return NextResponse.json(
        { error: 'Phone number already registered' },
        { status: 409 }
      )
    }

    // Check if email already exists (if provided)
    if (email) {
      const existingEmail = await prisma.user.findUnique({
        where: { email }
      })

      if (existingEmail) {
        return NextResponse.json(
          { error: 'Email already registered' },
          { status: 409 }
        )
      }
    }

    // Hash password
    const hashedPassword = await hashPassword(password)

    // Create user
    const user = await prisma.user.create({
      data: {
        username,
        password: hashedPassword,
        firstName,
        lastName,
        phoneNumber,
        email: email || null,
        gender: gender || null,
        address: address || null,
        role: 'PLAYER'
      },
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        phoneNumber: true,
        email: true,
        role: true,
        createdAt: true
      }
    })

    // Create session for auto-login
    const sessionToken = await createSession(user.id)

    // Set cookie
    const cookieStore = await cookies()
    cookieStore.set('session', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    })

    return NextResponse.json({
      message: 'User registered successfully',
      user,
      autoLogin: true
    }, { status: 201 })

  } catch (error) {
    logger.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
