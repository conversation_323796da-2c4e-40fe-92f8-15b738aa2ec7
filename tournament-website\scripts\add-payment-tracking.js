const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function addPaymentTracking() {
  console.log('💰 Adding Payment Tracking to Tournament System...\n')

  try {
    // Step 1: Add payment tracking columns to player_registrations
    console.log('1️⃣ Adding payment tracking columns...')
    
    await prisma.$executeRaw`
      DO $$ 
      BEGIN
        -- Create payment status enum if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'PaymentStatus') THEN
          CREATE TYPE "PaymentStatus" AS ENUM ('PAID', 'UNPAID', 'PARTIAL', 'REFUNDED');
        END IF;
        
        -- Add payment columns if they don't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'player_registrations' AND column_name = 'payment_status') THEN
          ALTER TABLE player_registrations 
          ADD COLUMN payment_status "PaymentStatus" DEFAULT 'UNPAID',
          ADD COLUMN payment_date TIMESTAMP,
          ADD COLUMN payment_amount DECIMAL(10, 2),
          ADD COLUMN payment_notes TEXT;
        END IF;
      END $$;
    `
    console.log('✅ Payment tracking columns added')

    // Step 2: Update existing registrations to UNPAID status
    console.log('\n2️⃣ Setting existing registrations to UNPAID status...')
    
    const existingRegistrations = await prisma.$queryRaw`
      SELECT id, user_id FROM player_registrations
    `

    console.log(`   Found ${existingRegistrations.length} existing registrations`)
    
    await prisma.$executeRaw`
      UPDATE player_registrations 
      SET payment_status = 'UNPAID'
      WHERE payment_status IS NULL
    `
    console.log('✅ Existing registrations set to UNPAID')

    // Step 3: Verify the changes
    console.log('\n3️⃣ Verifying payment tracking setup...')
    
    const registrationsWithPayment = await prisma.$queryRaw`
      SELECT 
        pr.id,
        u.first_name,
        u.last_name,
        u.phone_number,
        g.name as game_name,
        pr.payment_status,
        pr.registration_date
      FROM player_registrations pr
      JOIN users u ON pr.user_id = u.id
      JOIN games g ON pr.game_id = g.id
      ORDER BY pr.registration_date DESC
      LIMIT 10
    `

    console.log('\n📊 Sample registrations with payment status:')
    registrationsWithPayment.forEach(reg => {
      console.log(`   ${reg.first_name} ${reg.last_name} - ${reg.game_name}`)
      console.log(`      Phone: ${reg.phone_number}`)
      console.log(`      Payment: ${reg.payment_status}`)
      console.log(`      Registered: ${reg.registration_date.toDateString()}`)
    })

    console.log('\n🎉 Payment tracking successfully added!')
    console.log('\nNew Features Available:')
    console.log('✅ Payment status tracking (PAID/UNPAID/PARTIAL/REFUNDED)')
    console.log('✅ Payment date and amount recording')
    console.log('✅ Payment notes for admin reference')
    console.log('✅ Player contact information accessible')

  } catch (error) {
    console.error('❌ Error adding payment tracking:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the migration
addPaymentTracking()
