import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const registrations = await prisma.playerRegistration.findMany({
      include: {
        user: true,
        game: true,
        tournamentSchedule: {
          select: {
            id: true,
            tournamentId: true,
            scheduledDate: true,
            scheduledTime: true,
            description: true
          }
        }
      },
      orderBy: {
        registrationDate: 'desc'
      }
    })

    return NextResponse.json(registrations)
  } catch (error) {
    logger.error('Error fetching registrations:', error)
    return NextResponse.json(
      { error: 'Failed to fetch registrations' },
      { status: 500 }
    )
  }
})
