const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testAccountCreation() {
  console.log('🧪 Testing account creation and password hashing...')

  try {
    // Test data
    const testUser = {
      username: 'testuser123',
      password: 'testpassword123',
      firstName: 'Test',
      lastName: 'User',
      phoneNumber: '+************',
      email: '<EMAIL>',
      gender: 'MALE',
      address: '<PERSON><PERSON><PERSON>'
    }

    console.log('\n1. Testing password hashing...')
    const hashedPassword = await bcrypt.hash(testUser.password, 12)
    console.log('✅ Password hashed successfully')
    console.log(`Original: ${testUser.password}`)
    console.log(`Hashed: ${hashedPassword.substring(0, 20)}...`)

    console.log('\n2. Testing password verification...')
    const isValid = await bcrypt.compare(testUser.password, hashedPassword)
    console.log(`✅ Password verification: ${isValid ? 'PASSED' : 'FAILED'}`)

    console.log('\n3. Creating test user in database...')
    const user = await prisma.user.create({
      data: {
        username: testUser.username,
        password: hashedPassword,
        firstName: testUser.firstName,
        lastName: testUser.lastName,
        phoneNumber: testUser.phoneNumber,
        email: testUser.email,
        gender: testUser.gender,
        address: testUser.address,
        role: 'PLAYER'
      },
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        phoneNumber: true,
        email: true,
        gender: true,
        address: true,
        role: true,
        createdAt: true,
        password: true // Include password to verify it's hashed
      }
    })

    console.log('✅ User created successfully:')
    console.log(`   ID: ${user.id}`)
    console.log(`   Username: ${user.username}`)
    console.log(`   Name: ${user.firstName} ${user.lastName}`)
    console.log(`   Phone: ${user.phoneNumber}`)
    console.log(`   Email: ${user.email}`)
    console.log(`   Gender: ${user.gender}`)
    console.log(`   Address: ${user.address}`)
    console.log(`   Role: ${user.role}`)
    console.log(`   Created: ${user.createdAt}`)
    console.log(`   Password (hashed): ${user.password.substring(0, 20)}...`)

    console.log('\n4. Testing password verification from database...')
    const dbPasswordValid = await bcrypt.compare(testUser.password, user.password)
    console.log(`✅ Database password verification: ${dbPasswordValid ? 'PASSED' : 'FAILED'}`)

    console.log('\n5. Testing duplicate username prevention...')
    try {
      await prisma.user.create({
        data: {
          username: testUser.username, // Same username
          password: hashedPassword,
          firstName: 'Another',
          lastName: 'User',
          phoneNumber: '+265991234568',
          role: 'PLAYER'
        }
      })
      console.log('❌ Duplicate username check FAILED - should have thrown error')
    } catch (error) {
      if (error.code === 'P2002') {
        console.log('✅ Duplicate username prevention: PASSED')
      } else {
        console.log(`❌ Unexpected error: ${error.message}`)
      }
    }

    console.log('\n6. Testing duplicate phone number prevention...')
    try {
      await prisma.user.create({
        data: {
          username: 'anotheruser',
          password: hashedPassword,
          firstName: 'Another',
          lastName: 'User',
          phoneNumber: testUser.phoneNumber, // Same phone number
          role: 'PLAYER'
        }
      })
      console.log('❌ Duplicate phone number check FAILED - should have thrown error')
    } catch (error) {
      console.log('✅ Duplicate phone number prevention: PASSED')
    }

    console.log('\n7. Cleaning up test user...')
    await prisma.user.delete({
      where: { id: user.id }
    })
    console.log('✅ Test user deleted')

    console.log('\n🎉 All account creation tests PASSED!')
    console.log('✅ Password hashing is working correctly')
    console.log('✅ User accounts are being stored properly')
    console.log('✅ Duplicate prevention is working')

  } catch (error) {
    console.error('❌ Test failed:', error)
    throw error
  }
}

testAccountCreation()
  .catch((e) => {
    console.error('❌ Test script failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
