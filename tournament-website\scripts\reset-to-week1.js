const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function resetToWeek1() {
  console.log('🔄 Resetting tournament system to start from Week 1...')

  try {
    // Step 1: Clear all existing tournament data except admin and user accounts
    console.log('\n1️⃣ Clearing existing tournament data...')
    
    // Clear weekly winners
    await prisma.weeklyWinner.deleteMany({})
    console.log('✅ Cleared weekly winners')
    
    // Clear weekly tournaments
    await prisma.weeklyTournament.deleteMany({})
    console.log('✅ Cleared weekly tournaments')
    
    // Clear tournament schedules
    await prisma.tournamentSchedule.deleteMany({})
    console.log('✅ Cleared tournament schedules')
    
    // Clear player registrations
    await prisma.playerRegistration.deleteMany({})
    console.log('✅ Cleared player registrations')
    
    // Reset player stats
    await prisma.playerStats.updateMany({
      data: {
        tournamentsParticipated: 0,
        tournamentsWon: 0,
        totalWins: 0,
        totalLosses: 0,
        winPercentage: 0.00
      }
    })
    console.log('✅ Reset player statistics')

    // Step 2: Create Week 1 tournaments for all games
    console.log('\n2️⃣ Creating Week 1 tournaments...')
    
    const games = await prisma.game.findMany()
    const week1Date = new Date('2025-07-07') // This Sunday (Week 1)
    
    for (const game of games) {
      // Create Week 1 tournament
      const tournament = await prisma.weeklyTournament.create({
        data: {
          gameId: game.id,
          weekNumber: 1,
          year: 2025,
          tournamentDate: week1Date,
          status: 'UPCOMING',
          totalParticipants: 0
        }
      })
      
      // Create corresponding schedule
      const schedule = await prisma.tournamentSchedule.create({
        data: {
          tournamentId: `T2025${String(game.id).padStart(3, '0')}`,
          gameId: game.id,
          scheduledDate: week1Date,
          scheduledTime: new Date('1970-01-01T14:00:00'), // 2:00 PM
          description: `${game.name} Tournament - Week 1`,
          status: 'SCHEDULED',
          maxParticipants: game.name === 'PES' ? 32 : 100,
          currentParticipants: 0
        }
      })
      
      console.log(`✅ Created Week 1 tournament for ${game.name}`)
    }

    // Step 3: Update any admin-created schedules to use correct week numbers
    console.log('\n3️⃣ Tournament system reset complete!')
    console.log(`📅 Tournament season now starts: July 1, 2025`)
    console.log(`🏆 Week 1 tournaments scheduled for: ${week1Date.toDateString()}`)
    console.log(`🎮 Games available: ${games.map(g => g.name).join(', ')}`)
    
    console.log('\n🎉 Ready for Week 1 tournaments!')

  } catch (error) {
    console.error('❌ Error resetting tournament system:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the reset
resetToWeek1()
