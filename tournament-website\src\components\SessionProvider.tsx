'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useInactivityLogout } from '@/hooks/useInactivityLogout'
import InactivityWarning from './InactivityWarning'
import { logger } from '@/lib/logger'

interface SessionContextType {
  isAuthenticated: boolean
  user: any | null
  logout: () => void
  refreshSession: () => void
}

const SessionContext = createContext<SessionContextType | undefined>(undefined)

interface SessionProviderProps {
  children: ReactNode
}

export function SessionProvider({ children }: SessionProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState(null)
  const [showWarning, setShowWarning] = useState(false)
  const [loading, setLoading] = useState(true)

  // Check session on mount
  useEffect(() => {
    checkSession()
  }, [])

  const checkSession = async () => {
    try {
      const response = await fetch('/api/auth/me', {
        credentials: 'include'
      })

      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
        setIsAuthenticated(true)
      } else {
        setUser(null)
        setIsAuthenticated(false)
      }
    } catch (error) {
      logger.error('Error checking session:', error)
      setUser(null)
      setIsAuthenticated(false)
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      })
    } catch (error) {
      logger.error('Error during logout:', error)
    } finally {
      setUser(null)
      setIsAuthenticated(false)
      setShowWarning(false)
      
      // Clear any stored tokens
      if (typeof window !== 'undefined') {
        localStorage.removeItem('adminToken')
      }
    }
  }

  const refreshSession = () => {
    checkSession()
  }

  // Inactivity logout hook
  const { resetTimer } = useInactivityLogout({
    timeout: 30 * 60 * 1000, // 30 minutes
    warningTime: 5 * 60 * 1000, // 5 minutes before logout
    enabled: isAuthenticated,
    onWarning: () => {
      setShowWarning(true)
    },
    onLogout: () => {
      logout()
    }
  })

  const handleStayLoggedIn = () => {
    setShowWarning(false)
    resetTimer()
  }

  const handleLogoutFromWarning = () => {
    setShowWarning(false)
    logout()
  }

  // Handle multi-tab session sync
  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'session_sync') {
        // Another tab logged out
        if (e.newValue === 'logout') {
          setUser(null)
          setIsAuthenticated(false)
          setShowWarning(false)
        }
        // Another tab logged in
        else if (e.newValue === 'login') {
          checkSession()
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  // Sync logout across tabs
  useEffect(() => {
    if (!isAuthenticated && typeof window !== 'undefined') {
      localStorage.setItem('session_sync', 'logout')
      // Clear the sync flag after a short delay
      setTimeout(() => {
        localStorage.removeItem('session_sync')
      }, 100)
    }
  }, [isAuthenticated])

  // Sync login across tabs
  useEffect(() => {
    if (isAuthenticated && typeof window !== 'undefined') {
      localStorage.setItem('session_sync', 'login')
      // Clear the sync flag after a short delay
      setTimeout(() => {
        localStorage.removeItem('session_sync')
      }, 100)
    }
  }, [isAuthenticated])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <SessionContext.Provider 
      value={{ 
        isAuthenticated, 
        user, 
        logout, 
        refreshSession 
      }}
    >
      {children}
      
      {/* Inactivity Warning Modal */}
      <InactivityWarning
        isVisible={showWarning}
        timeRemaining={5 * 60} // 5 minutes in seconds
        onStayLoggedIn={handleStayLoggedIn}
        onLogout={handleLogoutFromWarning}
      />
    </SessionContext.Provider>
  )
}

export function useSession() {
  const context = useContext(SessionContext)
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider')
  }
  return context
}
