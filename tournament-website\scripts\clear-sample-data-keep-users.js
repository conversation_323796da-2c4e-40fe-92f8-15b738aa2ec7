// Load environment variables
require('dotenv').config({ path: '.env.local' })

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  console.log('🧹 Clearing sample tournament data while preserving all user accounts...')
  console.log('🔗 Connecting to database...')

  try {
    // Test database connection first
    await prisma.$connect()
    console.log('✅ Database connected successfully')
    // Get count of users before clearing
    const userCount = await prisma.user.count()
    console.log(`📊 Found ${userCount} user accounts (will be preserved)`)

    // Delete tournament-related data in order to respect foreign key constraints
    console.log('Deleting weekly winners...')
    const deletedWinners = await prisma.weeklyWinner.deleteMany({})
    console.log(`   Deleted ${deletedWinners.count} weekly winners`)

    console.log('Deleting weekly tournaments...')
    const deletedTournaments = await prisma.weeklyTournament.deleteMany({})
    console.log(`   Deleted ${deletedTournaments.count} weekly tournaments`)

    console.log('Deleting tournament schedules...')
    const deletedSchedules = await prisma.tournamentSchedule.deleteMany({})
    console.log(`   Deleted ${deletedSchedules.count} tournament schedules`)

    console.log('Deleting player stats...')
    const deletedStats = await prisma.playerStats.deleteMany({})
    console.log(`   Deleted ${deletedStats.count} player stats records`)

    console.log('Deleting player registrations...')
    const deletedRegistrations = await prisma.playerRegistration.deleteMany({})
    console.log(`   Deleted ${deletedRegistrations.count} player registrations`)

    console.log('Deleting announcements...')
    const deletedAnnouncements = await prisma.announcement.deleteMany({})
    console.log(`   Deleted ${deletedAnnouncements.count} announcements`)

    // Clear user sessions (they can log in again)
    console.log('Clearing user sessions (users will need to log in again)...')
    const deletedSessions = await prisma.userSession.deleteMany({})
    console.log(`   Deleted ${deletedSessions.count} user sessions`)

    // Verify all users are still there
    const finalUserCount = await prisma.user.count()
    console.log(`✅ User accounts preserved: ${finalUserCount} users`)

    // Verify admin user exists
    const adminUser = await prisma.user.findUnique({
      where: { username: 'Tournaowner' }
    })

    if (!adminUser) {
      console.log('⚠️  Admin user not found, creating...')
      const bcrypt = require('bcryptjs')
      const hashedPassword = await bcrypt.hash('Bsvca2223', 12)

      await prisma.user.create({
        data: {
          username: 'Tournaowner',
          password: hashedPassword,
          firstName: 'Tournament',
          lastName: 'Owner',
          phoneNumber: '+************',
          role: 'ADMIN'
        }
      })
      console.log('✅ Admin user created')
    } else {
      console.log('✅ Admin user verified')
    }

    // Verify games exist (these should be preserved)
    const games = await prisma.game.findMany()
    console.log(`✅ Games preserved: ${games.map(g => g.name).join(', ')}`)

    // Show summary
    console.log('\n🎉 Sample data cleared successfully!')
    console.log('📊 Summary:')
    console.log(`   • User accounts preserved: ${finalUserCount}`)
    console.log(`   • Games preserved: ${games.length}`)
    console.log(`   • Tournament data cleared: All`)
    console.log(`   • User sessions cleared: All (users need to log in again)`)
    console.log('\n📅 Tournament system is now clean and ready for fresh tournament season.')
    console.log('🚀 System will start from Week 1 when new tournaments are created.')
    
  } catch (error) {
    console.error('❌ Error clearing sample data:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Script failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
