'use client'

import { logger } from '@/lib/logger'
import { AdminAuthGuard } from '@/components/AdminAuthGuard'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Game {
  id: number
  name: string
}

interface User {
  id: number
  username: string
  firstName: string
  lastName: string
}

// Removed unused TournamentResult interface

interface WeeklyTournament {
  id: number
  gameId: number
  weekNumber: number
  year: number
  tournamentDate: string
  status: string
  totalParticipants: number
  game: {
    name: string
  }
  winner: {
    firstName: string
    lastName: string
    username: string
  } | null
}

interface PlayerStats {
  id: number
  userId: number
  gameId: number
  tournamentsParticipated: number
  tournamentsWon: number
  totalWins: number
  totalLosses: number
  winPercentage: number
  user: {
    firstName: string
    lastName: string
    username: string
  }
  game: {
    name: string
  }
}

function AdminLeaderboardContent() {
  const [games, setGames] = useState<Game[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [recentTournaments, setRecentTournaments] = useState<WeeklyTournament[]>([])
  const [topPlayers, setTopPlayers] = useState<PlayerStats[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [message, setMessage] = useState('')
  const [activeTab, setActiveTab] = useState<'add' | 'recent' | 'leaderboard'>('add')
  const [formData, setFormData] = useState({
    gameId: '',
    winnerId: '',
    position: 1,
    weekNumber: '',
    year: new Date().getFullYear()
  })
  const router = useRouter()
  const { logout } = useAdminAuth()

  const fetchGames = useCallback(async () => {
    try {
      const response = await fetch('/api/games')
      if (response.ok) {
        const data = await response.json()
        setGames(data)
      }
    } catch (error) {
      logger.error('Error fetching games:', error)
    }
  }, [])

  const fetchUsers = useCallback(async () => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/players', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setUsers(data)
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error fetching users:', error)
    }
  }, [router])

  const fetchRecentTournaments = useCallback(async () => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/tournaments/recent', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setRecentTournaments(data)
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error fetching recent tournaments:', error)
    }
  }, [router])

  const fetchTopPlayers = useCallback(async () => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/leaderboard/top-players', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setTopPlayers(data)
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error fetching top players:', error)
    } finally {
      setLoading(false)
    }
  }, [router])

  useEffect(() => {
    // Check if admin is logged in
    const token = localStorage.getItem('adminToken')
    if (!token) {
      router.push('/admin/login')
      return
    }

    fetchGames()
    fetchUsers()
    fetchRecentTournaments()
    fetchTopPlayers()
  }, [router, fetchGames, fetchUsers, fetchRecentTournaments, fetchTopPlayers])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    setMessage('')

    try {
      // Create tournament with winner
      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/leaderboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          gameId: parseInt(formData.gameId),
          winnerId: parseInt(formData.winnerId),
          position: formData.position,
          weekNumber: parseInt(formData.weekNumber),
          year: formData.year,
          tournamentDate: new Date().toISOString()
        })
      })

      if (response.ok) {
        await response.json() // Remove unused result variable
        setMessage('✅ Leaderboard updated successfully! Winner added and stats updated.')
        setFormData({
          gameId: '',
          winnerId: '',
          position: 1,
          weekNumber: '',
          year: new Date().getFullYear()
        })

        // Refresh data to show updated leaderboard
        fetchRecentTournaments()
        fetchTopPlayers()

        // Switch to recent tournaments tab to show the update
        setActiveTab('recent')
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      } else {
        const error = await response.json()
        setMessage(`❌ Error: ${error.error || 'Failed to update leaderboard'}`)
      }
    } catch (error) {
      logger.error('Error updating leaderboard:', error)
      setMessage('❌ Error: Failed to update leaderboard')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="text-3xl font-bold text-blue-600">
                eSports RXP
              </Link>
              <span className="ml-4 px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full">
                Update Leaderboard
              </span>
            </div>
            <Link
              href="/admin/dashboard"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('add')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'add'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              🏆 Add Tournament Result
            </button>
            <button
              onClick={() => setActiveTab('recent')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'recent'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              📅 Recent Tournaments
            </button>
            <button
              onClick={() => setActiveTab('leaderboard')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'leaderboard'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              🥇 Top Players
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'add' && (
          <>
            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
              <h2 className="text-lg font-semibold text-blue-900 mb-2">📋 How to Update Leaderboard</h2>
              <ul className="text-blue-800 space-y-1">
                <li>• Select the game and tournament week</li>
                <li>• Choose the winning player from the dropdown</li>
                <li>• Position 1 = Winner (updates tournament wins + total wins)</li>
                <li>• Position 2-3 = Runner-up (updates participation only)</li>
                <li>• Stats and leaderboard will update in real-time</li>
              </ul>
            </div>

        {/* Winner Input Form */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Add Tournament Result</h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Game Selection */}
              <div>
                <label htmlFor="game" className="block text-sm font-medium text-gray-700 mb-2">
                  Game *
                </label>
                <select
                  id="game"
                  name="gameId"
                  value={formData.gameId}
                  onChange={(e) => setFormData({ ...formData, gameId: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Select Game</option>
                  {games.map((game) => (
                    <option key={game.id} value={game.id}>
                      {game.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Player Selection */}
              <div>
                <label htmlFor="player" className="block text-sm font-medium text-gray-700 mb-2">
                  Player Name *
                </label>
                <select
                  id="player"
                  name="winnerId"
                  value={formData.winnerId}
                  onChange={(e) => setFormData({ ...formData, winnerId: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Select Player</option>
                  {users.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.firstName} {user.lastName} (@{user.username})
                    </option>
                  ))}
                </select>
              </div>

              {/* Position */}
              <div>
                <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-2">
                  Winning Position *
                </label>
                <select
                  id="position"
                  name="position"
                  value={formData.position}
                  onChange={(e) => setFormData({ ...formData, position: parseInt(e.target.value) })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value={1}>🥇 1st Place (Winner)</option>
                  <option value={2}>🥈 2nd Place (Runner-up)</option>
                  <option value={3}>🥉 3rd Place (Runner-up)</option>
                </select>
              </div>

              {/* Week Number */}
              <div>
                <label htmlFor="week" className="block text-sm font-medium text-gray-700 mb-2">
                  Week Number *
                </label>
                <input
                  type="number"
                  id="week"
                  name="weekNumber"
                  min="1"
                  max="52"
                  value={formData.weekNumber}
                  onChange={(e) => setFormData({ ...formData, weekNumber: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., 1, 2, 3..."
                  required
                />
              </div>
            </div>

            {/* Year */}
            <div className="w-full md:w-1/2">
              <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-2">
                Year *
              </label>
              <input
                type="number"
                id="year"
                name="year"
                min="2020"
                max="2030"
                value={formData.year}
                onChange={(e) => setFormData({ ...formData, year: parseInt(e.target.value) })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            {/* Submit Button */}
            <div className="flex items-center justify-between">
              <button
                type="submit"
                disabled={submitting}
                className="bg-purple-600 text-white px-6 py-3 rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    🏆 Update Leaderboard
                  </>
                )}
              </button>
            </div>

            {/* Message */}
            {message && (
              <div className={`p-4 rounded-md ${message.includes('✅') ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                {message}
              </div>
            )}
          </form>
        </div>
        </>
        )}

        {/* Recent Tournaments Tab */}
        {activeTab === 'recent' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Recent Tournament Results</h2>
              <p className="text-gray-600">Latest tournament winners and results</p>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tournament
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Winner
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Participants
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {recentTournaments.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                        No recent tournaments found
                      </td>
                    </tr>
                  ) : (
                    recentTournaments.map((tournament) => (
                      <tr key={tournament.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {tournament.game.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              Week {tournament.weekNumber}, {tournament.year}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {tournament.winner ? (
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {tournament.winner.firstName} {tournament.winner.lastName}
                              </div>
                              <div className="text-sm text-gray-500">@{tournament.winner.username}</div>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500">No winner recorded</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(tournament.tournamentDate).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {tournament.totalParticipants || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            tournament.status === 'COMPLETED'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {tournament.status}
                          </span>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Top Players Tab */}
        {activeTab === 'leaderboard' && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Top Players Leaderboard</h2>
              <p className="text-gray-600">Current player rankings across all games</p>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rank
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Player
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Game
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tournaments Won
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Win Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Games
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {topPlayers.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                        No player stats found
                      </td>
                    </tr>
                  ) : (
                    topPlayers.map((player, index) => (
                      <tr key={player.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <span className={`text-lg font-bold ${
                              index === 0 ? 'text-yellow-500' :
                              index === 1 ? 'text-gray-400' :
                              index === 2 ? 'text-yellow-600' : 'text-gray-600'
                            }`}>
                              {index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `#${index + 1}`}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {player.user.firstName} {player.user.lastName}
                            </div>
                            <div className="text-sm text-gray-500">@{player.user.username}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {player.game.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {player.tournamentsWon}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-900">
                              {player.winPercentage ? Number(player.winPercentage).toFixed(1) : '0.0'}%
                            </span>
                            <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${player.winPercentage || 0}%` }}
                              ></div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {player.totalWins + player.totalLosses}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default function AdminLeaderboardPage() {
  return (
    <AdminAuthGuard>
      <AdminLeaderboardContent />
    </AdminAuthGuard>
  )
}