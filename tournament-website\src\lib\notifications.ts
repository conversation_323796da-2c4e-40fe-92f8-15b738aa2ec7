/**
 * Real-time notification system for tournament updates
 */
import { logger } from '@/lib/logger'

// Import the broadcast functions (will be available at runtime)
let broadcastNotification: any
let sendNotificationToUser: any

// Dynamically import to avoid circular dependencies
if (typeof window === 'undefined') {
  // Server-side only
  import('../app/api/notifications/sse/route').then(module => {
    broadcastNotification = module.broadcastNotification
    sendNotificationToUser = module.sendNotificationToUser
  })
}

export enum NotificationType {
  TOURNAMENT_CREATED = 'tournament_created',
  TOURNAMENT_UPDATED = 'tournament_updated',
  REGISTRATION_CONFIRMED = 'registration_confirmed',
  LEADERBOARD_UPDATED = 'leaderboard_updated',
  ANNOUNCEMENT_POSTED = 'announcement_posted',
  TOURNAMENT_STARTING = 'tournament_starting',
  TOURNAMENT_COMPLETED = 'tournament_completed',
  ADMIN_LOGIN = 'admin_login'
}

export interface Notification {
  type: NotificationType
  title: string
  message: string
  data?: any
  targetUsers?: string[]
  priority?: 'low' | 'medium' | 'high'
}

/**
 * Send notification to all connected users
 */
export function notifyAll(notification: Notification) {
  if (broadcastNotification) {
    broadcastNotification({
      type: notification.type,
      message: `${notification.title}: ${notification.message}`,
      data: notification.data
    })
  }
}

/**
 * Send notification to specific users
 */
export function notifyUsers(userIds: string[], notification: Notification) {
  if (broadcastNotification) {
    broadcastNotification({
      type: notification.type,
      message: `${notification.title}: ${notification.message}`,
      data: notification.data,
      targetUsers: userIds
    })
  }
}

/**
 * Send notification to a single user
 */
export function notifyUser(userId: string, notification: Notification) {
  if (sendNotificationToUser) {
    sendNotificationToUser(userId, {
      type: notification.type,
      message: `${notification.title}: ${notification.message}`,
      data: notification.data
    })
  }
}

/**
 * Tournament-specific notifications
 */
export const TournamentNotifications = {
  tournamentCreated: (tournamentName: string, gameId: number) => {
    notifyAll({
      type: NotificationType.TOURNAMENT_CREATED,
      title: 'New Tournament',
      message: `${tournamentName} tournament is now open for registration!`,
      data: { gameId },
      priority: 'high'
    })
  },

  registrationConfirmed: (userId: string, tournamentName: string, gameName: string) => {
    notifyUser(userId, {
      type: NotificationType.REGISTRATION_CONFIRMED,
      title: 'Registration Confirmed',
      message: `You're registered for ${tournamentName} - ${gameName}`,
      data: { tournamentName, gameName },
      priority: 'medium'
    })
  },

  tournamentRegistration: (playerName: string, gameName: string, tournamentId: string) => {
    notifyAll({
      type: NotificationType.REGISTRATION_CONFIRMED,
      title: 'New Registration',
      message: `${playerName} registered for ${gameName} Tournament ${tournamentId}`,
      data: { playerName, gameName, tournamentId },
      priority: 'medium'
    })
  },

  leaderboardUpdated: (winnerName: string, gameName: string) => {
    notifyAll({
      type: NotificationType.LEADERBOARD_UPDATED,
      title: 'Leaderboard Updated',
      message: `${winnerName} won the latest ${gameName} tournament!`,
      data: { winnerName, gameName },
      priority: 'medium'
    })
  },

  announcementPosted: (title: string, content: string) => {
    notifyAll({
      type: NotificationType.ANNOUNCEMENT_POSTED,
      title: 'New Announcement',
      message: title,
      data: { content },
      priority: 'high'
    })
  },

  tournamentStarting: (tournamentName: string, gameId: number) => {
    // Notify only registered players for this game
    notifyAll({
      type: NotificationType.TOURNAMENT_STARTING,
      title: 'Tournament Starting',
      message: `${tournamentName} is starting soon! Get ready!`,
      data: { gameId },
      priority: 'high'
    })
  },

  adminLogin: (adminUsername: string) => {
    // This could be sent to a monitoring system or admin WhatsApp
    logger.info('Admin login: ${adminUsername} at ${new Date().toISOString()}')
  }
}

/**
 * Client-side notification handler
 */
export class NotificationClient {
  private eventSource: EventSource | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  connect() {
    if (typeof window === 'undefined') return

    this.eventSource = new EventSource('/api/notifications/sse')

    this.eventSource.onopen = () => {
      logger.info('Connected to live notifications')
      this.reconnectAttempts = 0
    }

    this.eventSource.onmessage = (event) => {
      try {
        const notification = JSON.parse(event.data)
        this.handleNotification(notification)
      } catch (error) {
        logger.error('Error parsing notification:', error)
      }
    }

    this.eventSource.onerror = () => {
      logger.info('Notification connection error')
      this.reconnect()
    }
  }

  private handleNotification(notification: any) {
    // Handle different notification types
    switch (notification.type) {
      case NotificationType.LEADERBOARD_UPDATED:
        this.showToast(notification.message, 'success')
        this.refreshLeaderboard()
        break
      case NotificationType.ANNOUNCEMENT_POSTED:
        this.showToast(notification.message, 'info')
        this.refreshAnnouncements()
        break
      case NotificationType.TOURNAMENT_CREATED:
        this.showToast(notification.message, 'success')
        break
      case NotificationType.REGISTRATION_CONFIRMED:
        this.showToast(notification.message, 'success')
        break
      default:
        this.showToast(notification.message, 'info')
    }
  }

  private showToast(message: string, type: 'success' | 'error' | 'info' | 'warning') {
    // Simple toast notification - you can enhance this with a proper toast library
    const toast = document.createElement('div')
    toast.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
      type === 'success' ? 'bg-green-500' :
      type === 'error' ? 'bg-red-500' :
      type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
    } text-white`
    toast.textContent = message
    document.body.appendChild(toast)

    setTimeout(() => {
      toast.remove()
    }, 5000)
  }

  private refreshLeaderboard() {
    // Trigger leaderboard refresh if on stats page
    if (window.location.pathname.includes('/stats')) {
      window.location.reload()
    }
  }

  private refreshAnnouncements() {
    // Trigger announcements refresh if on home page
    if (window.location.pathname === '/') {
      window.location.reload()
    }
  }

  private reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      setTimeout(() => {
        logger.info('Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})')
        this.connect()
      }, this.reconnectDelay * this.reconnectAttempts)
    }
  }

  disconnect() {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
  }
}

// Export singleton instance for client-side use
export const notificationClient = new NotificationClient()
