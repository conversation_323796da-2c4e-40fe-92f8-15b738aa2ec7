import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'
import { TournamentNotifications } from '@/lib/notifications'

/**
 * Auto-create weekend tournaments for all games
 * This can be called manually or via cron job
 */
export const POST = withAdminAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const { weekNumber, year, scheduledDate } = body

    // Get current week if not provided
    const currentDate = new Date()
    const currentWeek = weekNumber || getWeekNumber(currentDate)
    const currentYear = year || currentDate.getFullYear()

    // Calculate next weekend if no date provided
    const nextWeekend = scheduledDate ? new Date(scheduledDate) : getNextWeekend()

    // Get all games (remove isActive filter as it doesn't exist)
    const games = await prisma.game.findMany()

    const createdTournaments = []

    // Create tournaments for each game
    for (const game of games) {
      // Check if tournament already exists for this week/game
      const existingTournament = await prisma.weeklyTournament.findFirst({
        where: {
          gameId: game.id,
          weekNumber: currentWeek,
          year: currentYear
        }
      })

      if (!existingTournament) {
        // Generate tournament ID
        const currentYear = new Date().getFullYear()
        const existingCount = await prisma.tournamentSchedule.count({
          where: {
            createdAt: {
              gte: new Date(`${currentYear}-01-01`),
              lt: new Date(`${currentYear + 1}-01-01`)
            }
          }
        })
        const tournamentId = `T${currentYear}${String(existingCount + 1).padStart(3, '0')}`

        // Create tournament schedule (add required scheduledTime)
        const schedule = await prisma.tournamentSchedule.create({
          data: {
            tournamentId,
            gameId: game.id,
            scheduledDate: nextWeekend,
            scheduledTime: nextWeekend,
            status: 'SCHEDULED',
            description: `Weekend ${game.name} Tournament - Week ${currentWeek}`
          }
        })

        // Create weekly tournament record (remove scheduleId as it doesn't exist)
        const tournament = await prisma.weeklyTournament.create({
          data: {
            gameId: game.id,
            weekNumber: currentWeek,
            year: currentYear,
            status: 'UPCOMING',
            tournamentDate: nextWeekend
          }
        })

        createdTournaments.push({
          tournament,
          schedule,
          game: game.name
        })

        // Send notification
        TournamentNotifications.tournamentCreated(
          `Week ${currentWeek} ${game.name}`,
          game.id
        )

        logger.info(`Auto-created tournament for ${game.name}`, {
          tournamentId: tournament.id,
          scheduleId: schedule.id,
          weekNumber: currentWeek,
          year: currentYear
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: `Created ${createdTournaments.length} tournaments for week ${currentWeek}`,
      tournaments: createdTournaments,
      weekNumber: currentWeek,
      year: currentYear,
      scheduledDate: nextWeekend
    })

  } catch (error) {
    logger.apiError('/api/admin/tournaments/auto-create', error)
    return NextResponse.json(
      { error: 'Failed to create tournaments' },
      { status: 500 }
    )
  }
})

/**
 * Get the week number for a given date
 */
function getWeekNumber(date: Date): number {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1)
  const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7)
}

/**
 * Get the next weekend (Saturday)
 */
function getNextWeekend(): Date {
  const today = new Date()
  const daysUntilSaturday = (6 - today.getDay()) % 7
  const nextSaturday = new Date(today)
  nextSaturday.setDate(today.getDate() + (daysUntilSaturday === 0 ? 7 : daysUntilSaturday))
  nextSaturday.setHours(14, 0, 0, 0) // 2 PM
  return nextSaturday
}

/**
 * Get maximum participants based on game type
 */
function getMaxParticipants(gameName: string): number {
  switch (gameName.toLowerCase()) {
    case 'pubg':
      return 100 // Battle royale can handle more players
    case 'call of duty':
      return 64  // Typical COD lobby size
    case 'pes':
      return 32  // Tournament bracket size
    default:
      return 50  // Default
  }
}

/**
 * Manual tournament creation endpoint
 */
export const PUT = withAdminAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const { 
      gameId, 
      scheduledDate, 
      maxParticipants, 
      description,
      registrationDeadline 
    } = body

    // Validate required fields
    if (!gameId || !scheduledDate) {
      return NextResponse.json(
        { error: 'Game ID and scheduled date are required' },
        { status: 400 }
      )
    }

    const game = await prisma.game.findUnique({
      where: { id: parseInt(gameId) }
    })

    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      )
    }

    const tournamentDate = new Date(scheduledDate)
    const weekNumber = getWeekNumber(tournamentDate)
    const year = tournamentDate.getFullYear()

    // Generate tournament ID
    const currentYear = new Date().getFullYear()
    const existingCount = await prisma.tournamentSchedule.count({
      where: {
        createdAt: {
          gte: new Date(`${currentYear}-01-01`),
          lt: new Date(`${currentYear + 1}-01-01`)
        }
      }
    })
    const tournamentId = `T${currentYear}${String(existingCount + 1).padStart(3, '0')}`

    // Create tournament schedule (add required scheduledTime)
    const schedule = await prisma.tournamentSchedule.create({
      data: {
        tournamentId,
        gameId: parseInt(gameId),
        scheduledDate: tournamentDate,
        scheduledTime: tournamentDate,
        status: 'SCHEDULED',
        description: description || `${game.name} Tournament - Week ${weekNumber}`
      }
    })

    // Create weekly tournament record (remove scheduleId)
    const tournament = await prisma.weeklyTournament.create({
      data: {
        gameId: parseInt(gameId),
        weekNumber,
        year,
        status: 'UPCOMING',
        tournamentDate: tournamentDate
      }
    })

    // Send notification
    TournamentNotifications.tournamentCreated(
      `${game.name} Tournament`,
      game.id
    )

    logger.info(`Manually created tournament for ${game.name}`, {
      tournamentId: tournament.id,
      scheduleId: schedule.id,
      weekNumber,
      year
    })

    return NextResponse.json({
      success: true,
      message: `Tournament created for ${game.name}`,
      tournament,
      schedule
    })

  } catch (error) {
    logger.apiError('/api/admin/tournaments/auto-create PUT', error)
    return NextResponse.json(
      { error: 'Failed to create tournament' },
      { status: 500 }
    )
  }
})
