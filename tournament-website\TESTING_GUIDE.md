# Mzuni Tournaments - Testing Guide

## Overview
This guide provides comprehensive testing instructions for the Mzuni Tournaments website, covering all implemented features including user authentication, tournament registration, admin management, and profile functionality.

## System Requirements
- PostgreSQL database with "Gaming" database
- Node.js and npm
- Next.js development server running on port 3001

## Test Environment Setup
1. Ensure PostgreSQL is running with the Gaming database
2. Run `npm run dev` in the tournament-website directory
3. Access the website at `http://localhost:3001`

## Feature Testing Checklist

### 1. User Authentication System ✅
**Registration Flow:**
- [ ] Navigate to `/signup`
- [ ] Fill out registration form with:
  - Username (unique)
  - Password (minimum requirements)
  - First Name, Last Name
  - Malawi phone number (+265XXXXXXXXX format)
  - Optional: Email, Date of Birth, Gender, Address, Emergency Contact
- [ ] Verify form validation works
- [ ] Confirm successful registration redirects to profile

**Login Flow:**
- [ ] Navigate to `/login`
- [ ] Test login with registered credentials
- [ ] Verify successful login redirects to profile for players
- [ ] Test admin login (Tournaowner/Bsvca2223) redirects to admin dashboard
- [ ] Test invalid credentials show error message

**Session Management:**
- [ ] Verify user stays logged in across page refreshes
- [ ] Test logout functionality
- [ ] Confirm session expires appropriately

### 2. User Profile Management ✅
**Profile Display:**
- [ ] Access profile at `/profile`
- [ ] Verify personal information displays correctly
- [ ] Check gaming statistics are shown
- [ ] Confirm tournament history is visible

**Profile Editing:**
- [ ] Test profile edit functionality
- [ ] Verify monthly edit restriction works
- [ ] Test form validation for profile updates
- [ ] Confirm phone number and email uniqueness validation

### 3. Tournament Registration System ✅
**Authenticated Registration:**
- [ ] Access `/tournament-register` (requires login)
- [ ] Verify user details are pre-filled
- [ ] Test game selection dropdown
- [ ] For non-PES games: verify in-game username is required
- [ ] For PES: verify in-game username field is hidden
- [ ] Test WhatsApp integration with pre-filled message

**Legacy Registration:**
- [ ] Access `/register` (original form)
- [ ] Test manual entry of user details
- [ ] Verify WhatsApp integration works

### 4. Admin Dashboard ✅
**Admin Authentication:**
- [ ] Login with admin credentials (Tournaowner/Bsvca2223)
- [ ] Verify redirect to `/admin`
- [ ] Confirm admin navigation is visible

**Dashboard Functionality:**
- [ ] Test "Total Players" button - shows player count by game
- [ ] Test "Registrations" button - displays all registration requests
- [ ] Test "Upcoming" button - shows scheduled tournaments
- [ ] Test "Completed" button - displays finished tournaments

**Admin Management:**
- [ ] Test stats management functionality
- [ ] Test schedule creation and management
- [ ] Test announcement system
- [ ] Verify leaderboard management works

### 5. Public Pages ✅
**Home Page:**
- [ ] Verify navigation changes based on authentication status
- [ ] For unauthenticated: shows Login/Sign Up buttons
- [ ] For authenticated: shows user name, profile link, logout
- [ ] For admin: shows additional admin dashboard link
- [ ] Test all navigation links work correctly

**Stats Page:**
- [ ] Access `/stats`
- [ ] Verify player statistics display
- [ ] Test leaderboard functionality
- [ ] Confirm game-specific stats work

**Schedule Page:**
- [ ] Access `/schedule`
- [ ] Verify upcoming tournaments display
- [ ] Test schedule filtering and sorting

### 6. Database Integration ✅
**Data Persistence:**
- [ ] Verify user registration creates database records
- [ ] Test tournament registration saves to PlayerRegistration table
- [ ] Confirm profile updates modify User table
- [ ] Check stats are properly linked to users and games

**Data Relationships:**
- [ ] Verify User-PlayerRegistration relationships
- [ ] Test User-PlayerStats connections
- [ ] Confirm Game-PlayerRegistration links

### 7. Security Features ✅
**Authentication Security:**
- [ ] Verify passwords are hashed (bcrypt with 12 rounds)
- [ ] Test session security (HTTP-only cookies)
- [ ] Confirm protected routes require authentication
- [ ] Test role-based access control (admin vs player)

**Input Validation:**
- [ ] Test SQL injection protection
- [ ] Verify XSS prevention
- [ ] Test phone number format validation
- [ ] Confirm email format validation

### 8. WhatsApp Integration ✅
**Registration Integration:**
- [ ] Test WhatsApp link generation
- [ ] Verify message includes all user details
- [ ] Confirm correct WhatsApp number (+265983132770)
- [ ] Test message formatting and encoding

### 9. User Experience Features ✅
**Navigation:**
- [ ] Test responsive navigation menu
- [ ] Verify authentication-aware navigation
- [ ] Test mobile compatibility
- [ ] Confirm all links work correctly

**Form Usability:**
- [ ] Test form validation messages
- [ ] Verify loading states during submissions
- [ ] Test error handling and display
- [ ] Confirm success messages appear

### 10. Performance and Reliability
**Database Performance:**
- [ ] Test with multiple concurrent users
- [ ] Verify query performance with sample data
- [ ] Test connection pooling

**Error Handling:**
- [ ] Test database connection failures
- [ ] Verify graceful error handling
- [ ] Test API endpoint error responses

## Test Data
**Sample Users:**
- Regular User: testuser/testpass123
- Admin User: Tournaowner/Bsvca2223

**Sample Games:**
- PUBG (requires in-game username)
- Call of Duty (requires in-game username)  
- PES (no in-game username required)

## Known Issues and Limitations
1. Monthly profile edit restriction is based on calendar months
2. Legacy registration still available for backward compatibility
3. Admin notifications to WhatsApp are manual process
4. Database migrations require manual reset for schema changes

## Success Criteria
- [ ] All authentication flows work correctly
- [ ] User registration and profile management functional
- [ ] Tournament registration integrates with user accounts
- [ ] Admin dashboard provides full management capabilities
- [ ] WhatsApp integration works for all registration types
- [ ] Database relationships maintain data integrity
- [ ] Security measures protect against common vulnerabilities
- [ ] User experience is intuitive and responsive

## Deployment Checklist
- [ ] Environment variables configured
- [ ] Database schema deployed
- [ ] Sample data populated
- [ ] SSL certificates configured (production)
- [ ] Domain name configured
- [ ] Backup procedures established
- [ ] Monitoring and logging configured
