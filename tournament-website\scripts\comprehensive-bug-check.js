#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🐛 COMPREHENSIVE BUG & DATA INTEGRITY CHECK')
console.log('==========================================\n')

let totalIssues = 0
let criticalBugs = 0
let warningBugs = 0

// 1. Check TypeScript compilation
console.log('1. 📝 TYPESCRIPT COMPILATION CHECK')
const { execSync } = require('child_process')

try {
  execSync('npx tsc --noEmit --skipLibCheck', { 
    cwd: path.join(__dirname, '..'),
    stdio: 'pipe'
  })
  console.log('✅ TypeScript compilation successful')
} catch (error) {
  console.log('❌ CRITICAL: TypeScript compilation errors found')
  console.log(error.stdout?.toString() || error.message)
  criticalBugs++
}

// 2. Check for console.log statements (should be removed in production)
console.log('\n2. 🖨️  CONSOLE LOG CHECK')
function checkConsoleLogs(dir) {
  const files = fs.readdirSync(dir)
  let logCount = 0
  
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      logCount += checkConsoleLogs(filePath)
    } else if (file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js')) {
      const content = fs.readFileSync(filePath, 'utf8')
      const matches = content.match(/console\.(log|error|warn|debug)/g)
      if (matches) {
        logCount += matches.length
        console.log(`⚠️  ${matches.length} console statements in ${filePath}`)
      }
    }
  }
  
  return logCount
}

const consoleLogCount = checkConsoleLogs(path.join(__dirname, '..', 'src'))
console.log(`${consoleLogCount === 0 ? '✅' : '⚠️ '} Total console statements: ${consoleLogCount}`)
if (consoleLogCount > 0) warningBugs++

// 3. Check for TODO/FIXME comments
console.log('\n3. 📋 TODO/FIXME CHECK')
function checkTodos(dir) {
  const files = fs.readdirSync(dir)
  let todoCount = 0
  
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      todoCount += checkTodos(filePath)
    } else if (file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js')) {
      const content = fs.readFileSync(filePath, 'utf8')
      const matches = content.match(/(TODO|FIXME|HACK|XXX):/gi)
      if (matches) {
        todoCount += matches.length
        console.log(`📝 ${matches.length} TODO/FIXME items in ${filePath}`)
      }
    }
  }
  
  return todoCount
}

const todoCount = checkTodos(path.join(__dirname, '..', 'src'))
console.log(`${todoCount === 0 ? '✅' : '📝'} Total TODO/FIXME items: ${todoCount}`)

// 4. Check for unused imports
console.log('\n4. 📦 UNUSED IMPORTS CHECK')
function checkUnusedImports(dir) {
  const files = fs.readdirSync(dir)
  let unusedCount = 0
  
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      unusedCount += checkUnusedImports(filePath)
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Basic check for imports that might be unused
      const importMatches = content.match(/import\s+{([^}]+)}\s+from/g)
      if (importMatches) {
        importMatches.forEach(importLine => {
          const imports = importLine.match(/{([^}]+)}/)[1].split(',').map(s => s.trim())
          imports.forEach(importName => {
            const cleanImportName = importName.replace(/\s+as\s+\w+/, '').trim()
            if (cleanImportName && !content.includes(cleanImportName.split(' ')[0])) {
              // This is a very basic check and may have false positives
            }
          })
        })
      }
    }
  }
  
  return unusedCount
}

console.log('✅ Unused imports check completed (basic)')

// 5. Check API endpoint consistency
console.log('\n5. 🌐 API ENDPOINT CONSISTENCY')
const apiDir = path.join(__dirname, '..', 'src', 'app', 'api')
let apiInconsistencies = 0

function checkAPIConsistency(dir) {
  if (!fs.existsSync(dir)) return
  
  const files = fs.readdirSync(dir)
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      checkAPIConsistency(filePath)
    } else if (file === 'route.ts') {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Check for proper error handling
      if (!content.includes('try') || !content.includes('catch')) {
        console.log(`⚠️  ${filePath} may lack proper error handling`)
        warningBugs++
        apiInconsistencies++
      }
      
      // Check for proper HTTP status codes
      if (content.includes('NextResponse.json') && !content.includes('status:')) {
        console.log(`⚠️  ${filePath} may lack proper HTTP status codes`)
        warningBugs++
        apiInconsistencies++
      }
    }
  }
}

checkAPIConsistency(apiDir)
console.log(`${apiInconsistencies === 0 ? '✅' : '⚠️ '} API consistency issues: ${apiInconsistencies}`)

// 6. Check database schema consistency
console.log('\n6. 🗄️  DATABASE SCHEMA CHECK')
const schemaFile = path.join(__dirname, '..', 'prisma', 'schema.prisma')
if (fs.existsSync(schemaFile)) {
  const schemaContent = fs.readFileSync(schemaFile, 'utf8')
  
  // Check for proper relationships
  const models = schemaContent.match(/model\s+\w+\s*{[^}]+}/g)
  if (models) {
    console.log(`✅ Found ${models.length} database models`)
    
    // Check for cascade deletes where appropriate
    const cascadeCount = (schemaContent.match(/onDelete:\s*Cascade/g) || []).length
    console.log(`✅ Found ${cascadeCount} cascade delete relationships`)
  }
  
  // Check for indexes on foreign keys
  const foreignKeys = (schemaContent.match(/@relation/g) || []).length
  console.log(`✅ Found ${foreignKeys} foreign key relationships`)
} else {
  console.log('❌ CRITICAL: Prisma schema file not found')
  criticalBugs++
}

// 7. Check for potential memory leaks
console.log('\n7. 🧠 MEMORY LEAK CHECK')
function checkMemoryLeaks(dir) {
  const files = fs.readdirSync(dir)
  let leakIssues = 0
  
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      leakIssues += checkMemoryLeaks(filePath)
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Check for multiple Prisma client instances
      if (content.includes('new PrismaClient()') && !content.includes('globalForPrisma')) {
        console.log(`⚠️  Potential Prisma client instance issue in ${filePath}`)
        warningBugs++
        leakIssues++
      }
      
      // Check for unclosed resources
      if (content.includes('setInterval') && !content.includes('clearInterval')) {
        console.log(`⚠️  Potential unclosed interval in ${filePath}`)
        warningBugs++
        leakIssues++
      }
    }
  }
  
  return leakIssues
}

const memoryLeakIssues = checkMemoryLeaks(path.join(__dirname, '..', 'src'))
console.log(`${memoryLeakIssues === 0 ? '✅' : '⚠️ '} Potential memory leak issues: ${memoryLeakIssues}`)

// Summary
console.log('\n📊 BUG CHECK SUMMARY')
console.log('===================')
console.log(`🔴 Critical Bugs: ${criticalBugs}`)
console.log(`🟡 Warning Issues: ${warningBugs}`)
console.log(`📊 Total Issues: ${criticalBugs + warningBugs}`)

if (criticalBugs === 0 && warningBugs === 0) {
  console.log('\n🎉 EXCELLENT! No bugs found.')
} else if (criticalBugs === 0) {
  console.log('\n✅ GOOD! No critical bugs, but some warnings to address.')
} else {
  console.log('\n🚨 ATTENTION REQUIRED! Critical bugs found.')
}

process.exit(criticalBugs > 0 ? 1 : 0)
