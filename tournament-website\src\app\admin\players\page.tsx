'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { logger } from '@/lib/logger'
import { AdminAuthGuard } from '@/components/AdminAuthGuard'
import { useAdminAuth } from '@/hooks/useAdminAuth'

interface Player {
  id: number
  username: string
  firstName: string
  lastName: string
  phoneNumber: string
  createdAt: string
  registrations: {
    paymentStatus: string
    id: number
    gameUsername: string
    registrationDate: string
    game: {
      name: string
    }
  }[]
}

function AdminPlayersContent() {
  const [players, setPlayers] = useState<Player[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGame, setSelectedGame] = useState<string>('')
  const [games, setGames] = useState<{id: number, name: string}[]>([])
  // Removed unused lastUpdated state
  // Removed unused auto-refresh state
  const router = useRouter()
  const { logout } = useAdminAuth()

  const fetchPlayers = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true)

      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/players', {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setPlayers(data)
        // Removed setLastUpdated call
      } else if (response.status === 403 || response.status === 401) {
        // Token expired or invalid, redirect to login
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error fetching players:', error)
    } finally {
      if (showLoading) setLoading(false)
    }
  }, [router])

  // Removed unused functions - toggleAutoRefresh and handleManualRefresh

  const handleLogout = () => {
    localStorage.removeItem('adminToken')
    router.push('/admin/login')
  }

  const fetchGames = useCallback(async () => {
    try {
      const response = await fetch('/api/games')
      if (response.ok) {
        const data = await response.json()
        setGames(data)
      }
    } catch (error) {
      logger.error('Error fetching games:', error)
    }
  }, [])

  useEffect(() => {
    // Check if admin is logged in
    const token = localStorage.getItem('adminToken')
    if (!token) {
      router.push('/admin/login')
      return
    }

    fetchPlayers()
    fetchGames()
  }, [router, fetchPlayers, fetchGames])

  const filteredPlayers = selectedGame
    ? players.filter(player => 
        player.registrations.some(reg => reg.game.name === selectedGame)
      )
    : players

  const sortedPlayers = filteredPlayers.sort((a, b) => {
    // First priority: Paid players at the top
    const aPaidCount = a.registrations.filter(reg => reg.paymentStatus === 'PAID').length
    const bPaidCount = b.registrations.filter(reg => reg.paymentStatus === 'PAID').length

    if (aPaidCount !== bPaidCount) {
      return bPaidCount - aPaidCount // More paid registrations first
    }

    // Second priority: Game-specific sorting if selected
    if (selectedGame) {
      const aGame = a.registrations.find(reg => reg.game.name === selectedGame)
      const bGame = b.registrations.find(reg => reg.game.name === selectedGame)

      // Prioritize paid status for selected game
      const aGamePaid = aGame?.paymentStatus === 'PAID'
      const bGamePaid = bGame?.paymentStatus === 'PAID'

      if (aGamePaid !== bGamePaid) {
        return bGamePaid ? 1 : -1 // Paid players first
      }

      return (aGame?.game.name || '').localeCompare(bGame?.game.name || '')
    }

    // Third priority: Alphabetical by first name
    return a.firstName.localeCompare(b.firstName)
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading players...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="text-3xl font-bold text-blue-600">
                eSports RXP Admin
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/admin/dashboard" className="text-gray-500 hover:text-gray-900">
                Dashboard
              </Link>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Total Players</h1>
          <p className="text-gray-600">All confirmed players with their registration details</p>
        </div>

        {/* Filter */}
        <div className="mb-6">
          <label htmlFor="gameFilter" className="block text-sm font-medium text-gray-700 mb-2">
            Filter by Game
          </label>
          <select
            id="gameFilter"
            value={selectedGame}
            onChange={(e) => setSelectedGame(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Games</option>
            {games.map(game => (
              <option key={game.id} value={game.name}>{game.name}</option>
            ))}
          </select>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Players</h3>
            <p className="text-3xl font-bold text-blue-600">{players.length}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Filtered Results</h3>
            <p className="text-3xl font-bold text-green-600">{filteredPlayers.length}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Registrations</h3>
            <p className="text-3xl font-bold text-purple-600">
              {players.reduce((sum, player) => sum + player.registrations.length, 0)}
            </p>
          </div>
        </div>

        {/* Players List */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Players {selectedGame && `- ${selectedGame}`}
            </h2>
          </div>
          
          {sortedPlayers.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No players found {selectedGame && `for ${selectedGame}`}.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Player Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Registered Games
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Join Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sortedPlayers.map((player) => (
                    <tr key={player.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {player.firstName} {player.lastName}
                          </div>
                          <div className="text-sm text-gray-500">@{player.username}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{player.phoneNumber}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          {player.registrations
                            .filter(reg => !selectedGame || reg.game.name === selectedGame)
                            .map((reg) => (
                            <div key={reg.id} className="flex items-center space-x-2">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {reg.game.name}
                              </span>
                              {reg.gameUsername && (
                                <span className="text-xs text-gray-500">
                                  ({reg.gameUsername})
                                </span>
                              )}
                            </div>
                          ))}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(player.createdAt).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default function AdminPlayersPage() {
  return (
    <AdminAuthGuard>
      <AdminPlayersContent />
    </AdminAuthGuard>
  )
}
