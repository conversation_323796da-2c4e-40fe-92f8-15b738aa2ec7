const fs = require('fs')
const path = require('path')

// Files to process (excluding node_modules, .next, etc.)
const srcDir = path.join(__dirname, '../src')

// Function to recursively find all TypeScript and JavaScript files
function findFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      // Skip certain directories
      if (!['node_modules', '.next', 'dist', 'build'].includes(file)) {
        findFiles(filePath, fileList)
      }
    } else if (file.match(/\.(ts|tsx|js|jsx)$/)) {
      fileList.push(filePath)
    }
  })
  
  return fileList
}

// Function to process a single file
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8')
  let modified = false
  let newContent = content
  
  // Check if file already imports logger
  const hasLoggerImport = content.includes("import { logger } from '@/lib/logger'")
  
  // Find console.log statements
  const consoleLogRegex = /console\.(log|error|warn|info|debug)\s*\([^)]*\)/g
  const matches = content.match(consoleLogRegex)
  
  if (matches && matches.length > 0) {
    console.log(`Processing ${filePath} - Found ${matches.length} console statements`)
    
    // Add logger import if not present
    if (!hasLoggerImport) {
      // Find the last import statement
      const importRegex = /^import.*from.*$/gm
      const imports = content.match(importRegex)
      if (imports) {
        const lastImport = imports[imports.length - 1]
        newContent = newContent.replace(lastImport, lastImport + "\nimport { logger } from '@/lib/logger'")
        modified = true
      }
    }
    
    // Replace console statements
    newContent = newContent.replace(/console\.error\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*([^)]+)\s*\)/g, 
      "logger.error('$1', $2)")
    
    newContent = newContent.replace(/console\.error\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g, 
      "logger.error('$1')")
    
    newContent = newContent.replace(/console\.log\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*([^)]+)\s*\)/g, 
      "logger.info('$1', $2)")
    
    newContent = newContent.replace(/console\.log\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g, 
      "logger.info('$1')")
    
    newContent = newContent.replace(/console\.warn\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*([^)]+)\s*\)/g, 
      "logger.warn('$1', $2)")
    
    newContent = newContent.replace(/console\.warn\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g, 
      "logger.warn('$1')")
    
    newContent = newContent.replace(/console\.info\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*([^)]+)\s*\)/g, 
      "logger.info('$1', $2)")
    
    newContent = newContent.replace(/console\.info\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g, 
      "logger.info('$1')")
    
    modified = true
  }
  
  // Write back if modified
  if (modified && newContent !== content) {
    fs.writeFileSync(filePath, newContent, 'utf8')
    console.log(`✅ Updated ${filePath}`)
    return true
  }
  
  return false
}

// Main execution
console.log('🧹 Removing console.log statements and replacing with logger...')

const files = findFiles(srcDir)
let processedCount = 0

files.forEach(file => {
  if (processFile(file)) {
    processedCount++
  }
})

console.log(`\n✅ Processed ${processedCount} files`)
console.log('🎉 Console.log cleanup complete!')

