const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "postgresql://postgres:Rodgers2004@localhost:5432/Gaming"
    }
  }
})

async function cleanAllTournamentData() {
  console.log('🧹 Starting comprehensive tournament data cleanup...')
  console.log('📋 This will preserve: Users, Games, Admin accounts')
  console.log('🗑️  This will remove: All tournament data, registrations, stats, schedules')

  try {
    // Step 1: Clear all tournament-related data
    console.log('\n1️⃣ Clearing weekly winners...')
    const deletedWinners = await prisma.weeklyWinner.deleteMany({})
    console.log(`✅ Deleted ${deletedWinners.count} weekly winners`)

    console.log('\n2️⃣ Clearing weekly tournaments...')
    const deletedTournaments = await prisma.weeklyTournament.deleteMany({})
    console.log(`✅ Deleted ${deletedTournaments.count} weekly tournaments`)

    console.log('\n3️⃣ Clearing tournament schedules...')
    const deletedSchedules = await prisma.tournamentSchedule.deleteMany({})
    console.log(`✅ Deleted ${deletedSchedules.count} tournament schedules`)

    console.log('\n4️⃣ Clearing player registrations...')
    const deletedRegistrations = await prisma.playerRegistration.deleteMany({})
    console.log(`✅ Deleted ${deletedRegistrations.count} player registrations`)

    console.log('\n5️⃣ Resetting player statistics...')
    const resetStats = await prisma.playerStats.updateMany({
      data: {
        tournamentsParticipated: 0,
        tournamentsWon: 0,
        totalWins: 0,
        totalLosses: 0,
        winPercentage: 0.00,
        lastUpdated: new Date()
      }
    })
    console.log(`✅ Reset ${resetStats.count} player statistics`)

    // Step 2: Clear any announcements (optional)
    console.log('\n6️⃣ Clearing announcements...')
    try {
      const deletedAnnouncements = await prisma.announcement.deleteMany({})
      console.log(`✅ Deleted ${deletedAnnouncements.count} announcements`)
    } catch (error) {
      console.log('ℹ️  No announcements table or already empty')
    }

    // Step 3: Verify what's preserved
    console.log('\n7️⃣ Verifying preserved data...')
    
    const userCount = await prisma.user.count()
    console.log(`✅ Preserved ${userCount} user accounts`)
    
    const gameCount = await prisma.game.count()
    console.log(`✅ Preserved ${gameCount} games`)
    
    const adminCount = await prisma.user.count({
      where: { role: 'ADMIN' }
    })
    console.log(`✅ Preserved ${adminCount} admin accounts`)

    // Step 4: Create fresh Week 1 tournaments
    console.log('\n8️⃣ Creating fresh Week 1 tournaments...')
    
    const games = await prisma.game.findMany()
    const week1Date = new Date('2025-07-07') // This Sunday (Week 1)
    
    for (const game of games) {
      // Create Week 1 tournament
      const tournament = await prisma.weeklyTournament.create({
        data: {
          gameId: game.id,
          weekNumber: 1,
          year: 2025,
          tournamentDate: week1Date,
          status: 'UPCOMING',
          totalParticipants: 0
        }
      })
      
      // Create corresponding schedule
      const schedule = await prisma.tournamentSchedule.create({
        data: {
          tournamentId: `T2025W01${String(game.id).padStart(2, '0')}`,
          gameId: game.id,
          scheduledDate: week1Date,
          scheduledTime: new Date('1970-01-01T14:00:00'), // 2:00 PM
          description: `${game.name} Tournament - Week 1`,
          status: 'SCHEDULED',
          maxParticipants: game.name === 'PES' ? 32 : 100,
          currentParticipants: 0,
          registrationDeadline: new Date('2025-07-06T23:59:59') // Day before
        }
      })
      
      console.log(`✅ Created Week 1 tournament and schedule for ${game.name}`)
    }

    // Step 5: Final verification
    console.log('\n9️⃣ Final verification...')
    
    const newTournamentCount = await prisma.weeklyTournament.count()
    const newScheduleCount = await prisma.tournamentSchedule.count()
    const newRegistrationCount = await prisma.playerRegistration.count()
    
    console.log(`📊 Current state:`)
    console.log(`   - Weekly tournaments: ${newTournamentCount}`)
    console.log(`   - Tournament schedules: ${newScheduleCount}`)
    console.log(`   - Player registrations: ${newRegistrationCount}`)
    console.log(`   - User accounts: ${userCount}`)
    console.log(`   - Games: ${gameCount}`)

    console.log('\n🎉 Tournament data cleanup completed successfully!')
    console.log('🏁 System is now ready for Week 1 tournaments')
    console.log('📅 Tournament season starts: July 1, 2025')
    console.log('🗓️  Week 1 tournaments scheduled: July 7, 2025')

  } catch (error) {
    console.error('❌ Error during cleanup:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the cleanup
if (require.main === module) {
  cleanAllTournamentData()
    .then(() => {
      console.log('\n✨ Cleanup script completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('\n💥 Cleanup script failed:', error)
      process.exit(1)
    })
}

module.exports = { cleanAllTournamentData }
