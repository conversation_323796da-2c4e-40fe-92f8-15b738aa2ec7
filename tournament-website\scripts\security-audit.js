#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🔒 COMPREHENSIVE SECURITY AUDIT')
console.log('================================\n')

let totalIssues = 0
let criticalIssues = 0
let warningIssues = 0

// 1. Check for hardcoded secrets
console.log('1. 🔍 HARDCODED SECRETS CHECK')
function checkHardcodedSecrets(dir) {
  const files = fs.readdirSync(dir)
  let issues = []
  
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      issues = issues.concat(checkHardcodedSecrets(filePath))
    } else if (file.endsWith('.ts') || file.endsWith('.js') || file.endsWith('.tsx')) {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Check for potential hardcoded secrets
      const secretPatterns = [
        /password\s*[:=]\s*["'][^"']+["']/gi,
        /secret\s*[:=]\s*["'][^"']+["']/gi,
        /token\s*[:=]\s*["'][^"']+["']/gi,
        /api[_-]?key\s*[:=]\s*["'][^"']+["']/gi,
        /database_url\s*[:=]\s*["'][^"']+["']/gi
      ]
      
      secretPatterns.forEach(pattern => {
        const matches = content.match(pattern)
        if (matches) {
          matches.forEach(match => {
            // Skip environment variable references
            if (!match.includes('process.env') && !match.includes('${')) {
              issues.push({
                file: filePath,
                issue: `Potential hardcoded secret: ${match}`,
                severity: 'CRITICAL'
              })
            }
          })
        }
      })
    }
  }
  
  return issues
}

const secretIssues = checkHardcodedSecrets(path.join(__dirname, '..', 'src'))
secretIssues.forEach(issue => {
  console.log(`❌ ${issue.severity}: ${issue.issue}`)
  console.log(`   File: ${issue.file}`)
  if (issue.severity === 'CRITICAL') criticalIssues++
  else warningIssues++
})

console.log(`${secretIssues.length === 0 ? '✅' : '❌'} Hardcoded secrets found: ${secretIssues.length}\n`)

// 2. Check authentication implementation
console.log('2. 🔐 AUTHENTICATION SECURITY')
const authFile = path.join(__dirname, '..', 'src', 'lib', 'auth.ts')
if (fs.existsSync(authFile)) {
  const authContent = fs.readFileSync(authFile, 'utf8')
  
  // Check bcrypt salt rounds
  const saltRoundsMatch = authContent.match(/saltRounds\s*=\s*(\d+)/)
  if (saltRoundsMatch) {
    const saltRounds = parseInt(saltRoundsMatch[1])
    if (saltRounds < 12) {
      console.log('❌ CRITICAL: bcrypt salt rounds too low (should be >= 12)')
      criticalIssues++
    } else {
      console.log('✅ bcrypt salt rounds adequate')
    }
  }
  
  // Check session token generation
  if (authContent.includes('Math.random()')) {
    console.log('⚠️  WARNING: Using Math.random() for session tokens (consider crypto.randomBytes)')
    warningIssues++
  } else {
    console.log('✅ Session token generation looks secure')
  }
  
  // Check session expiration
  if (authContent.includes('7 * 24 * 60 * 60 * 1000')) {
    console.log('✅ Session expiration set (7 days)')
  }
} else {
  console.log('❌ CRITICAL: auth.ts file not found')
  criticalIssues++
}

// 3. Check API endpoint security
console.log('\n3. 🌐 API ENDPOINT SECURITY')
const apiDir = path.join(__dirname, '..', 'src', 'app', 'api')
let apiIssues = 0

function checkAPIEndpoints(dir) {
  if (!fs.existsSync(dir)) return
  
  const files = fs.readdirSync(dir)
  for (const file of files) {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      checkAPIEndpoints(filePath)
    } else if (file === 'route.ts') {
      const content = fs.readFileSync(filePath, 'utf8')
      
      // Check for authentication
      const hasAuth = content.includes('validateSession') ||
                     content.includes('withAdminAuth') ||
                     content.includes('validateAdminSession') ||
                     content.includes('authentication') ||
                     filePath.includes('auth') ||
                     filePath.includes('login') ||
                     // Public endpoints that don't need auth
                     filePath.includes('announcements/route.ts') ||
                     filePath.includes('games/route.ts') ||
                     filePath.includes('stats/route.ts') ||
                     filePath.includes('schedules/route.ts') ||
                     filePath.includes('weekly-tournaments/route.ts')

      if (!hasAuth) {
        console.log(`⚠️  WARNING: ${filePath} may lack authentication`)
        warningIssues++
        apiIssues++
      }
      
      // Check for input validation
      if (content.includes('request.json()') && !content.includes('validation')) {
        // This is a basic check - might have false positives
      }
      
      // Check for SQL injection protection (Prisma should handle this)
      if (content.includes('prisma') && !content.includes('raw')) {
        // Good - using Prisma ORM
      }
    }
  }
}

checkAPIEndpoints(apiDir)
console.log(`${apiIssues === 0 ? '✅' : '⚠️ '} API endpoints checked: ${apiIssues} potential issues`)

// 4. Check environment variables
console.log('\n4. 🔧 ENVIRONMENT CONFIGURATION')
const envFile = path.join(__dirname, '..', '.env.local')
if (fs.existsSync(envFile)) {
  const envContent = fs.readFileSync(envFile, 'utf8')
  
  // Check for required variables
  const requiredVars = ['DATABASE_URL', 'NEXTAUTH_SECRET']
  requiredVars.forEach(varName => {
    if (envContent.includes(varName)) {
      console.log(`✅ ${varName} configured`)
    } else {
      console.log(`❌ CRITICAL: ${varName} missing`)
      criticalIssues++
    }
  })
  
  // Check for weak secrets
  if (envContent.includes('your-secret-key-here')) {
    console.log('❌ CRITICAL: Default NEXTAUTH_SECRET detected')
    criticalIssues++
  }
} else {
  console.log('❌ CRITICAL: .env.local file not found')
  criticalIssues++
}

// 5. Check for exposed sensitive files
console.log('\n5. 📁 SENSITIVE FILE EXPOSURE')
const sensitiveFiles = ['.env', '.env.local', '.env.production', 'database.db']
let exposedFiles = 0

sensitiveFiles.forEach(file => {
  const filePath = path.join(__dirname, '..', file)
  if (fs.existsSync(filePath)) {
    // Check if it's in .gitignore
    const gitignorePath = path.join(__dirname, '..', '.gitignore')
    if (fs.existsSync(gitignorePath)) {
      const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8')
      if (!gitignoreContent.includes(file) && !gitignoreContent.includes('.env*')) {
        console.log(`❌ CRITICAL: ${file} not in .gitignore`)
        criticalIssues++
        exposedFiles++
      } else {
        console.log(`✅ ${file} properly ignored`)
      }
    }
  }
})

// Summary
console.log('\n📊 SECURITY AUDIT SUMMARY')
console.log('========================')
console.log(`🔴 Critical Issues: ${criticalIssues}`)
console.log(`🟡 Warning Issues: ${warningIssues}`)
console.log(`📊 Total Issues: ${criticalIssues + warningIssues}`)

if (criticalIssues === 0 && warningIssues === 0) {
  console.log('\n🎉 EXCELLENT! No security issues found.')
} else if (criticalIssues === 0) {
  console.log('\n✅ GOOD! No critical issues, but some warnings to address.')
} else {
  console.log('\n🚨 ATTENTION REQUIRED! Critical security issues found.')
}

process.exit(criticalIssues > 0 ? 1 : 0)
