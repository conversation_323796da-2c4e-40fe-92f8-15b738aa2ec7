-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "pending_change_status" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateTable
CREATE TABLE "pending_profile_changes" (
    "id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "requested_by" INTEGER NOT NULL,
    "status" "pending_change_status" NOT NULL DEFAULT 'PENDING',
    "original_username" TEXT,
    "original_first_name" TEXT,
    "original_last_name" TEXT,
    "original_phone_number" TEXT,
    "original_email" TEXT,
    "original_date_of_birth" TIMESTAMP(3),
    "original_gender" TEXT,
    "original_address" TEXT,
    "new_username" TEXT,
    "new_first_name" TEXT,
    "new_last_name" TEXT,
    "new_phone_number" TEXT,
    "new_email" TEXT,
    "new_date_of_birth" TIMESTAMP(3),
    "new_gender" TEXT,
    "new_address" TEXT,
    "reviewed_by" INTEGER,
    "reviewed_at" TIMESTAMP(3),
    "review_notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "pending_profile_changes_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "pending_profile_changes" ADD CONSTRAINT "pending_profile_changes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pending_profile_changes" ADD CONSTRAINT "pending_profile_changes_requested_by_fkey" FOREIGN KEY ("requested_by") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "pending_profile_changes" ADD CONSTRAINT "pending_profile_changes_reviewed_by_fkey" FOREIGN KEY ("reviewed_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
