#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')

async function checkAuthStatus() {
  const prisma = new PrismaClient()
  
  try {
    console.log('🔍 Checking Authentication Status')
    console.log('=================================\n')

    // Check users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        createdAt: true
      }
    })

    console.log(`👥 Users in database: ${users.length}`)
    users.forEach(user => {
      console.log(`   - ${user.username} (${user.firstName} ${user.lastName}) - ${user.role} - ${user.isActive ? 'Active' : 'Inactive'}`)
    })

    // Check sessions
    const sessions = await prisma.userSession.findMany({
      include: {
        user: {
          select: {
            username: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    console.log(`\n🔑 Active sessions: ${sessions.length}`)
    sessions.forEach(session => {
      const isExpired = session.expiresAt < new Date()
      console.log(`   - ${session.user.username}: ${session.token.substring(0, 8)}... (${isExpired ? 'EXPIRED' : 'VALID'})`)
    })

    // Check admin user specifically
    const adminUser = await prisma.user.findUnique({
      where: { username: 'Tournaowner' },
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true,
        password: true
      }
    })

    console.log(`\n👑 Admin user status:`)
    if (adminUser) {
      console.log(`   ✅ Admin user exists: ${adminUser.username}`)
      console.log(`   - Role: ${adminUser.role}`)
      console.log(`   - Active: ${adminUser.isActive}`)
      console.log(`   - Has password: ${adminUser.password ? 'Yes' : 'No'}`)
    } else {
      console.log(`   ❌ Admin user 'Tournaowner' not found`)
    }

    console.log('\n💡 To fix authentication issues:')
    console.log('   1. Make sure you have created an account or logged in')
    console.log('   2. Check browser cookies for session token')
    console.log('   3. Try logging out and logging back in')

  } catch (error) {
    console.error('❌ Error checking auth status:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkAuthStatus()
