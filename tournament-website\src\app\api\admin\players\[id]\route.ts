import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const DELETE = withAdminAuth(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  try {
    const playerId = parseInt(params.id)

    if (!playerId) {
      return NextResponse.json(
        { error: 'Player ID is required' },
        { status: 400 }
      )
    }

    // Get player details before deletion for logging
    const player = await prisma.user.findUnique({
      where: { id: playerId },
      include: {
        registrations: {
          include: {
            game: { select: { name: true } },
            tournamentSchedule: { select: { tournamentId: true } }
          }
        }
      }
    })

    if (!player) {
      return NextResponse.json(
        { error: 'Player not found' },
        { status: 404 }
      )
    }

    if (player.role !== 'PLAYER') {
      return NextResponse.json(
        { error: 'Cannot delete non-player users' },
        { status: 400 }
      )
    }

    // Update tournament participant counts for tournaments the player was registered for
    for (const registration of player.registrations) {
      if (registration.tournamentScheduleId) {
        await prisma.tournamentSchedule.update({
          where: { id: registration.tournamentScheduleId },
          data: { currentParticipants: { decrement: 1 } }
        })
      }
    }

    // Delete the player (this will cascade delete registrations, stats, etc.)
    await prisma.user.delete({
      where: { id: playerId }
    })

    // Log the deletion
    logger.info(`Player deleted by admin:`, {
      playerId,
      playerName: `${player.firstName} ${player.lastName}`,
      username: player.username,
      phoneNumber: player.phoneNumber,
      registrationsCount: player.registrations.length,
      registrations: player.registrations.map(reg => ({
        game: reg.game.name,
        tournamentId: reg.tournamentSchedule?.tournamentId,
        paymentStatus: reg.paymentStatus
      }))
    })

    return NextResponse.json({
      success: true,
      message: `Player ${player.firstName} ${player.lastName} has been successfully removed`,
      deletedPlayer: {
        id: player.id,
        name: `${player.firstName} ${player.lastName}`,
        username: player.username,
        registrationsRemoved: player.registrations.length
      }
    })

  } catch (error) {
    logger.error('Error deleting player:', error)
    return NextResponse.json(
      { error: 'Failed to delete player' },
      { status: 500 }
    )
  }
})
