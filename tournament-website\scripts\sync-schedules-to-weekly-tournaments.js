const { PrismaClient } = require('@prisma/client')

// Tournament utility function (copied from tournament-utils.ts)
function getTournamentWeekForDate(date) {
  const year = date.getFullYear()
  const seasonStart = new Date(year, 0, 1) // January 1st of that year

  // Calculate days since season start
  const daysSinceStart = Math.floor((date.getTime() - seasonStart.getTime()) / (1000 * 60 * 60 * 24))

  // Calculate week number (1-based)
  const weekNumber = Math.floor(daysSinceStart / 7) + 1

  // Cap at 52 weeks maximum
  return Math.min(Math.max(weekNumber, 1), 52)
}

const prisma = new PrismaClient()

async function syncSchedulesToWeeklyTournaments() {
  console.log('🔄 Syncing tournament schedules to weekly tournaments...')

  try {
    // Get all tournament schedules
    const schedules = await prisma.tournamentSchedule.findMany({
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    console.log(`📅 Found ${schedules.length} tournament schedules`)

    let created = 0
    let updated = 0

    for (const schedule of schedules) {
      const weekNumber = getTournamentWeekForDate(schedule.scheduledDate)
      const year = schedule.scheduledDate.getFullYear()

      try {
        const result = await prisma.weeklyTournament.upsert({
          where: {
            gameId_weekNumber_year: {
              gameId: schedule.gameId,
              weekNumber: weekNumber,
              year: year
            }
          },
          update: {
            tournamentDate: schedule.scheduledDate,
            status: schedule.status === 'COMPLETED' ? 'COMPLETED' : 'UPCOMING'
          },
          create: {
            gameId: schedule.gameId,
            weekNumber: weekNumber,
            year: year,
            tournamentDate: schedule.scheduledDate,
            status: schedule.status === 'COMPLETED' ? 'COMPLETED' : 'UPCOMING',
            totalParticipants: 0
          }
        })

        if (result.createdAt.getTime() === result.createdAt.getTime()) {
          created++
        } else {
          updated++
        }

        console.log(`✅ ${schedule.game.name} - Week ${weekNumber}, ${year}`)
      } catch (error) {
        console.error(`❌ Error syncing ${schedule.game.name} - Week ${weekNumber}, ${year}:`, error.message)
      }
    }

    console.log(`\n🎉 Sync completed!`)
    console.log(`📊 Created: ${created} weekly tournaments`)
    console.log(`🔄 Updated: ${updated} weekly tournaments`)

  } catch (error) {
    console.error('❌ Error syncing schedules:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the sync
syncSchedulesToWeeklyTournaments()
