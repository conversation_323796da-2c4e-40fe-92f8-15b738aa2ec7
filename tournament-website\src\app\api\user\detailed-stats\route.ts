import { NextRequest, NextResponse } from 'next/server'
import { validateSession } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { getCurrentTournamentWeek } from '@/lib/tournament-utils'
import { logger } from '@/lib/logger'

export async function GET(request: NextRequest) {
  try {
    const sessionCookie = request.cookies.get('session')
    if (!sessionCookie) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    const session = await validateSession(sessionCookie.value)
    if (!session) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 })
    }

    const userId = session.userId
    const currentYear = new Date().getFullYear()
    const currentWeek = getCurrentTournamentWeek()

    // Get basic player stats
    const playerStats = await prisma.playerStats.findMany({
      where: { userId },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Get user registrations
    const registrations = await prisma.playerRegistration.findMany({
      where: { userId },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Get weekly wins
    const weeklyWins = await prisma.weeklyWinner.findMany({
      where: { userId },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ]
    })

    // Check current week tournament registrations
    const currentWeekTournaments = await prisma.weeklyTournament.findMany({
      where: {
        weekNumber: currentWeek,
        year: currentYear,
        status: {
          in: ['UPCOMING', 'IN_PROGRESS']
        }
      },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Check if user is registered for current week tournaments
    const currentWeekRegistrationStatus = await Promise.all(
      currentWeekTournaments.map(async (tournament) => {
        const isRegistered = registrations.some(reg => reg.gameId === tournament.gameId)
        return {
          gameId: tournament.gameId,
          gameName: tournament.game.name,
          isRegistered,
          tournamentDate: tournament.tournamentDate,
          weekNumber: tournament.weekNumber
        }
      })
    )

    // Get upcoming tournaments user is registered for
    const upcomingTournaments = await prisma.weeklyTournament.findMany({
      where: {
        status: {
          in: ['UPCOMING', 'IN_PROGRESS']
        },
        tournamentDate: {
          gte: new Date()
        },
        gameId: {
          in: registrations.map(reg => reg.gameId)
        }
      },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        tournamentDate: 'asc'
      },
      take: 5
    })

    // Get recent tournament history
    const recentTournaments = await prisma.weeklyTournament.findMany({
      where: {
        status: 'COMPLETED',
        gameId: {
          in: registrations.map(reg => reg.gameId)
        }
      },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        },
        winner: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ],
      take: 10
    })

    // Calculate overall statistics
    const totalTournamentsParticipated = playerStats.reduce((sum, stat) => sum + stat.tournamentsParticipated, 0)
    const totalTournamentsWon = playerStats.reduce((sum, stat) => sum + stat.tournamentsWon, 0)
    const totalWins = playerStats.reduce((sum, stat) => sum + stat.totalWins, 0)
    const totalLosses = playerStats.reduce((sum, stat) => sum + stat.totalLosses, 0)
    const overallWinRate = totalWins + totalLosses > 0 ? (totalWins / (totalWins + totalLosses)) * 100 : 0

    return NextResponse.json({
      playerStats,
      registrations,
      weeklyWins,
      currentWeekRegistrationStatus,
      upcomingTournaments,
      recentTournaments,
      overallStats: {
        totalTournamentsParticipated,
        totalTournamentsWon,
        totalWins,
        totalLosses,
        overallWinRate: Math.round(overallWinRate * 100) / 100,
        gamesRegistered: registrations.length,
        currentWeek,
        currentYear
      }
    })

  } catch (error) {
    logger.error('Error fetching detailed stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
