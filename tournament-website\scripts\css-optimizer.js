#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🎨 CSS OPTIMIZATION ANALYZER')
console.log('============================\n')

// Check if build exists
const buildDir = path.join(__dirname, '..', '.next')
if (!fs.existsSync(buildDir)) {
  console.log('❌ No build found. Run "npm run build" first.')
  process.exit(1)
}

// Analyze CSS files
function analyzeCSSFiles() {
  console.log('📊 CSS BUNDLE ANALYSIS')
  console.log('----------------------')
  
  const staticDir = path.join(buildDir, 'static', 'css')
  
  if (!fs.existsSync(staticDir)) {
    console.log('⚠️  No CSS files found in build')
    return
  }
  
  const cssFiles = fs.readdirSync(staticDir).filter(file => file.endsWith('.css'))
  let totalSize = 0
  
  cssFiles.forEach(file => {
    const filePath = path.join(staticDir, file)
    const stats = fs.statSync(filePath)
    const sizeKB = (stats.size / 1024).toFixed(2)
    totalSize += stats.size
    
    console.log(`📄 ${file}: ${sizeKB} KB`)
    
    // Analyze CSS content
    const content = fs.readFileSync(filePath, 'utf8')
    const rules = content.split('}').length - 1
    const selectors = content.match(/[^{}]+{/g)?.length || 0
    
    console.log(`   Rules: ${rules}, Selectors: ${selectors}`)
  })
  
  console.log(`\n📦 Total CSS Size: ${(totalSize / 1024).toFixed(2)} KB`)
  
  // Size recommendations
  if (totalSize > 100 * 1024) { // 100KB
    console.log('⚠️  CSS bundle is large (>100KB). Consider optimization.')
  } else if (totalSize > 50 * 1024) { // 50KB
    console.log('💡 CSS bundle is moderate (>50KB). Room for optimization.')
  } else {
    console.log('✅ CSS bundle size is optimal (<50KB).')
  }
}

// Check for unused CSS
function checkUnusedCSS() {
  console.log('\n🔍 UNUSED CSS ANALYSIS')
  console.log('----------------------')
  
  try {
    // Check if PurgeCSS is configured
    const postcssConfig = path.join(__dirname, '..', 'postcss.config.mjs')
    if (fs.existsSync(postcssConfig)) {
      const config = fs.readFileSync(postcssConfig, 'utf8')
      if (config.includes('purgecss')) {
        console.log('✅ PurgeCSS is configured')
      } else {
        console.log('⚠️  PurgeCSS not found in PostCSS config')
      }
    }
    
    // Check Tailwind purging
    const tailwindConfig = path.join(__dirname, '..', 'tailwind.config.ts')
    if (fs.existsSync(tailwindConfig)) {
      const config = fs.readFileSync(tailwindConfig, 'utf8')
      if (config.includes('content:')) {
        console.log('✅ Tailwind content purging is configured')
      } else {
        console.log('⚠️  Tailwind content purging not properly configured')
      }
    }
    
  } catch (error) {
    console.log('❌ Error checking CSS purging configuration:', error.message)
  }
}

// Performance recommendations
function performanceRecommendations() {
  console.log('\n🚀 PERFORMANCE RECOMMENDATIONS')
  console.log('==============================')
  
  const recommendations = []
  
  // Check for CSS-in-JS
  const srcDir = path.join(__dirname, '..', 'src')
  let cssInJSFound = false
  
  function checkCSSInJS(dir) {
    const files = fs.readdirSync(dir)
    
    for (const file of files) {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory() && !file.startsWith('.')) {
        checkCSSInJS(filePath)
      } else if (file.endsWith('.tsx') || file.endsWith('.jsx')) {
        const content = fs.readFileSync(filePath, 'utf8')
        if (content.includes('styled-components') || content.includes('emotion') || content.includes('style={{')) {
          cssInJSFound = true
        }
      }
    }
  }
  
  checkCSSInJS(srcDir)
  
  if (cssInJSFound) {
    recommendations.push('Consider moving inline styles to CSS classes for better performance')
  }
  
  // Check for critical CSS
  const hasInlineCSS = fs.existsSync(path.join(__dirname, '..', 'src', 'app', 'globals.css'))
  if (hasInlineCSS) {
    recommendations.push('✅ Global CSS file found - good for critical CSS')
  }
  
  // Check for font optimization
  const layoutFile = path.join(__dirname, '..', 'src', 'app', 'layout.tsx')
  if (fs.existsSync(layoutFile)) {
    const content = fs.readFileSync(layoutFile, 'utf8')
    if (content.includes('next/font')) {
      recommendations.push('✅ Next.js font optimization is being used')
    } else {
      recommendations.push('Consider using Next.js font optimization for better performance')
    }
  }
  
  // Output recommendations
  if (recommendations.length === 0) {
    console.log('✅ No specific recommendations - CSS setup looks good!')
  } else {
    recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`)
    })
  }
}

// CSS compression analysis
function compressionAnalysis() {
  console.log('\n📦 COMPRESSION ANALYSIS')
  console.log('----------------------')
  
  const staticDir = path.join(buildDir, 'static', 'css')
  
  if (!fs.existsSync(staticDir)) {
    console.log('⚠️  No CSS files found for compression analysis')
    return
  }
  
  const cssFiles = fs.readdirSync(staticDir).filter(file => file.endsWith('.css'))
  
  cssFiles.forEach(file => {
    const filePath = path.join(staticDir, file)
    const content = fs.readFileSync(filePath, 'utf8')
    
    // Check for minification indicators
    const isMinified = !content.includes('\n  ') && content.length > 1000
    const hasComments = content.includes('/*')
    const hasWhitespace = content.includes('  ')
    
    console.log(`📄 ${file}:`)
    console.log(`   Minified: ${isMinified ? '✅' : '❌'}`)
    console.log(`   Comments removed: ${!hasComments ? '✅' : '❌'}`)
    console.log(`   Whitespace optimized: ${!hasWhitespace ? '✅' : '❌'}`)
  })
}

// Main execution
async function main() {
  try {
    analyzeCSSFiles()
    checkUnusedCSS()
    compressionAnalysis()
    performanceRecommendations()
    
    console.log('\n🎯 OPTIMIZATION SUMMARY')
    console.log('======================')
    console.log('✅ CSS analysis complete!')
    console.log('💡 Review recommendations above for further optimizations.')
    console.log('\n🔧 To optimize further:')
    console.log('   1. Run "npm run build" to apply PostCSS optimizations')
    console.log('   2. Check bundle size with "npm run analyze" (if available)')
    console.log('   3. Monitor Core Web Vitals in production')
    
  } catch (error) {
    console.error('❌ Error during CSS analysis:', error.message)
    process.exit(1)
  }
}

main()
