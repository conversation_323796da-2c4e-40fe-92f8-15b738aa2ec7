import { getCurrentTournamentWeek, getTournamentWeekForDate } from '@/lib/tournament-utils'

describe('Tournament Utils', () => {
  describe('getCurrentTournamentWeek', () => {
    it('should return week 1 for January 1st', () => {
      // Mock Date to return January 1st
      const mockDate = new Date('2025-01-01')
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any)

      const week = getCurrentTournamentWeek()
      expect(week).toBe(1)

      jest.restoreAllMocks()
    })

    it('should return week 2 for January 8th', () => {
      const mockDate = new Date('2025-01-08')
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any)

      const week = getCurrentTournamentWeek()
      expect(week).toBe(2)

      jest.restoreAllMocks()
    })

    it('should return week 52 for December 31st', () => {
      const mockDate = new Date('2025-12-31')
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any)

      const week = getCurrentTournamentWeek()
      expect(week).toBe(52)

      jest.restoreAllMocks()
    })

    it('should cap at 52 weeks maximum', () => {
      // Mock a date that would calculate to more than 52 weeks
      const mockDate = new Date('2025-12-31')
      mockDate.setDate(mockDate.getDate() + 100) // Add extra days
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any)

      const week = getCurrentTournamentWeek()
      expect(week).toBeLessThanOrEqual(52)

      jest.restoreAllMocks()
    })
  })

  describe('getTournamentWeekForDate', () => {
    it('should return week 1 for January 1st of any year', () => {
      const date = new Date('2025-01-01')
      const week = getTournamentWeekForDate(date)
      expect(week).toBe(1)
    })

    it('should return week 2 for January 8th', () => {
      const date = new Date('2025-01-08')
      const week = getTournamentWeekForDate(date)
      expect(week).toBe(2)
    })

    it('should handle different years correctly', () => {
      const date2024 = new Date('2024-01-01')
      const date2025 = new Date('2025-01-01')
      
      expect(getTournamentWeekForDate(date2024)).toBe(1)
      expect(getTournamentWeekForDate(date2025)).toBe(1)
    })

    it('should return minimum week 1', () => {
      // Test with a date before January 1st (edge case)
      const date = new Date('2024-12-31')
      const week = getTournamentWeekForDate(date)
      expect(week).toBeGreaterThanOrEqual(1)
    })

    it('should cap at 52 weeks maximum', () => {
      const date = new Date('2025-12-31')
      const week = getTournamentWeekForDate(date)
      expect(week).toBeLessThanOrEqual(52)
    })

    it('should handle leap years correctly', () => {
      // 2024 is a leap year
      const leapYearDate = new Date('2024-02-29')
      const week = getTournamentWeekForDate(leapYearDate)
      expect(week).toBeGreaterThan(8) // Should be around week 9
      expect(week).toBeLessThan(11)   // But less than week 11
    })

    it('should be consistent for same dates', () => {
      const date1 = new Date('2025-06-15')
      const date2 = new Date('2025-06-15')
      
      expect(getTournamentWeekForDate(date1)).toBe(getTournamentWeekForDate(date2))
    })

    it('should increment weeks correctly', () => {
      const date1 = new Date('2025-01-01') // Week 1
      const date2 = new Date('2025-01-08') // Week 2
      const date3 = new Date('2025-01-15') // Week 3
      
      expect(getTournamentWeekForDate(date2)).toBe(getTournamentWeekForDate(date1) + 1)
      expect(getTournamentWeekForDate(date3)).toBe(getTournamentWeekForDate(date2) + 1)
    })
  })
})
