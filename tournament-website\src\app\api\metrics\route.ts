import { NextResponse } from 'next/server'
import { getPerformanceMetrics } from '@/lib/performance-middleware'
import { performanceMonitor } from '@/lib/performance-monitor'
import { withAdminAuth } from '@/lib/admin-middleware'

export const GET = withAdminAuth(async () => {
  try {
    const metrics = getPerformanceMetrics()
    const fullReport = performanceMonitor.generateReport()
    
    return NextResponse.json({
      ...metrics,
      fullReport,
      serverMetrics: performanceMonitor.getServerMetrics(),
      databaseMetrics: performanceMonitor.getDatabaseMetrics()
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch metrics' },
      { status: 500 }
    )
  }
})
