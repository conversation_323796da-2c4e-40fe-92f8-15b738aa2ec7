'use client'

import { logger } from '@/lib/logger'
import { AdminAuthGuard } from '@/components/AdminAuthGuard'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Player {
  id: number
  username: string
  firstName: string
  lastName: string
  phoneNumber: string
  email?: string
  registrations: {
    id: number
    gameUsername: string
    registrationDate: string
    paymentStatus: 'PAID' | 'UNPAID' | 'PARTIAL' | 'REFUNDED'
    paymentDate?: string
    paymentAmount?: number
    paymentNotes?: string
    game: {
      name: string
    }
    tournamentSchedule?: {
      tournamentId: string
    }
  }[]
}

function PlayerManagementContent() {
  const [players, setPlayers] = useState<Player[]>([])
  const [filteredPlayers, setFilteredPlayers] = useState<Player[]>([])
  const [loading, setLoading] = useState(true)
  const [paymentFilter, setPaymentFilter] = useState<'all' | 'paid' | 'unpaid'>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  // Removed unused showContactModal state
  const [showRemoveModal, setShowRemoveModal] = useState(false)
  const router = useRouter()
  const { logout } = useAdminAuth()

  const fetchPlayers = useCallback(async () => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/players-with-payments', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setPlayers(data)
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error fetching players:', error)
    } finally {
      setLoading(false)
    }
  }, [router])

  const filterPlayers = useCallback(() => {
    let filtered = players

    // Filter by payment status
    if (paymentFilter !== 'all') {
      filtered = filtered.filter(player =>
        player.registrations.some(reg =>
          paymentFilter === 'paid' ? reg.paymentStatus === 'PAID' : reg.paymentStatus !== 'PAID'
        )
      )
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(player =>
        player.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        player.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        player.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        player.phoneNumber.includes(searchTerm)
      )
    }

    // Sort filtered players: paid players first
    const sortedFiltered = filtered.sort((a, b) => {
      // Count paid registrations for each player
      const aPaidCount = a.registrations.filter(reg => reg.paymentStatus === 'PAID').length
      const bPaidCount = b.registrations.filter(reg => reg.paymentStatus === 'PAID').length

      // Paid players first
      if (aPaidCount !== bPaidCount) {
        return bPaidCount - aPaidCount
      }

      // Then by registration date (newest first)
      const aLatestReg = Math.max(...a.registrations.map(reg => new Date(reg.registrationDate).getTime()))
      const bLatestReg = Math.max(...b.registrations.map(reg => new Date(reg.registrationDate).getTime()))

      if (aLatestReg !== bLatestReg) {
        return bLatestReg - aLatestReg
      }

      // Finally by name
      return a.firstName.localeCompare(b.firstName)
    })

    setFilteredPlayers(sortedFiltered)
  }, [players, paymentFilter, searchTerm])

  useEffect(() => {
    fetchPlayers()
  }, [fetchPlayers])

  useEffect(() => {
    filterPlayers()
  }, [players, paymentFilter, searchTerm, filterPlayers])

  const updatePaymentStatus = async (registrationId: number, status: string, amount?: number, notes?: string) => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch(`/api/admin/payment-status/${registrationId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          paymentStatus: status,
          paymentAmount: amount,
          paymentNotes: notes,
          paymentDate: status === 'PAID' ? new Date().toISOString() : null
        })
      })

      if (response.ok) {
        fetchPlayers() // Refresh data
        setShowPaymentModal(false)
        alert('✅ Payment status updated successfully!')
      } else {
        alert('❌ Failed to update payment status')
      }
    } catch (error) {
      logger.error('Error updating payment status:', error)
      alert('❌ Error updating payment status')
    }
  }

  const removePlayer = async (playerId: number) => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch(`/api/admin/players/${playerId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        fetchPlayers() // Refresh data
        setShowRemoveModal(false)
        setSelectedPlayer(null)
        alert('✅ Player removed successfully!')
      } else {
        alert('❌ Failed to remove player')
      }
    } catch (error) {
      logger.error('Error removing player:', error)
      alert('❌ Error removing player')
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'text-green-600'
      case 'UNPAID': return 'text-orange-600'
      case 'PARTIAL': return 'text-yellow-600'
      case 'REFUNDED': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getPaymentStatusDot = (status: string) => {
    switch (status) {
      case 'PAID': return '🟢'
      case 'UNPAID': return '🟠'
      case 'PARTIAL': return '🟡'
      case 'REFUNDED': return '🔴'
      default: return '⚪'
    }
  }

  const contactPlayer = (player: Player) => {
    const message = `Hello ${player.firstName}, this is regarding your tournament registration. Please contact us for more details.`
    const whatsappUrl = `https://wa.me/${player.phoneNumber.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading player management...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="text-3xl font-bold text-blue-600">Admin Panel</Link>
              <span className="ml-4 px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded-full">
                Player Management
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/admin/dashboard" className="text-gray-500 hover:text-gray-900">Dashboard</Link>
              <Link href="/admin/schedules" className="text-gray-500 hover:text-gray-900">Schedules</Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Player Management</h1>
          <p className="text-lg text-gray-600">
            Manage players, track payments, and contact participants.
          </p>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Payment Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Payment Status</label>
              <select
                value={paymentFilter}
                onChange={(e) => setPaymentFilter(e.target.value as 'all' | 'paid' | 'unpaid')}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Players</option>
                <option value="paid">Paid Players 🟢</option>
                <option value="unpaid">Unpaid Players 🟠</option>
              </select>
            </div>

            {/* Search */}
            <div>
              <label htmlFor="player-search" className="block text-sm font-medium text-gray-700 mb-2">Search Players</label>
              <input
                type="text"
                id="player-search"
                name="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Name, username, or phone..."
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Stats */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Quick Stats</label>
              <div className="text-sm text-gray-600">
                <p>Total: {players.length} players</p>
                <p>Showing: {filteredPlayers.length} players</p>
              </div>
            </div>
          </div>
        </div>

        {/* Players List */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Players ({filteredPlayers.length})
            </h2>
          </div>

          {filteredPlayers.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">👥</div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">No Players Found</h3>
              <p className="text-gray-600">
                {paymentFilter !== 'all' 
                  ? `No ${paymentFilter} players match your criteria.`
                  : 'No players match your search criteria.'
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Player</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registrations</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPlayers.map((player) => (
                    <tr key={player.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {player.firstName} {player.lastName}
                          </div>
                          <div className="text-sm text-gray-500">@{player.username}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{player.phoneNumber}</div>
                        {player.email && (
                          <div className="text-sm text-gray-500">{player.email}</div>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          {player.registrations.map((reg) => (
                            <div key={reg.id} className="text-sm">
                              <span className="font-medium">{reg.game.name}</span>
                              {reg.tournamentSchedule && (
                                <span className="text-gray-500 ml-2">({reg.tournamentSchedule.tournamentId})</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          {player.registrations.map((reg) => (
                            <div key={reg.id} className="flex items-center text-sm">
                              <span className="mr-2">{getPaymentStatusDot(reg.paymentStatus)}</span>
                              <span className={getPaymentStatusColor(reg.paymentStatus)}>
                                {reg.paymentStatus}
                              </span>
                              {reg.paymentAmount && (
                                <span className="text-gray-500 ml-2">
                                  (K{reg.paymentAmount})
                                </span>
                              )}
                            </div>
                          ))}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedPlayer(player)
                              setShowPaymentModal(true)
                            }}
                            className="text-green-600 hover:text-green-900"
                            title="Update Payment"
                          >
                            💰
                          </button>
                          <button
                            onClick={() => contactPlayer(player)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Contact Player"
                          >
                            📞
                          </button>
                          <button
                            onClick={() => {
                              setSelectedPlayer(player)
                              setShowRemoveModal(true)
                            }}
                            className="text-red-600 hover:text-red-900"
                            title="Remove Player"
                          >
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </main>

      {/* Payment Modal */}
      {showPaymentModal && selectedPlayer && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Update Payment Status - {selectedPlayer.firstName} {selectedPlayer.lastName}
            </h3>
            
            <div className="space-y-4">
              {selectedPlayer.registrations.map((reg) => (
                <div key={reg.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{reg.game.name}</span>
                    <span className={`${getPaymentStatusColor(reg.paymentStatus)} font-medium`}>
                      {getPaymentStatusDot(reg.paymentStatus)} {reg.paymentStatus}
                    </span>
                  </div>
                  
                  <div className="space-y-2">
                    <select
                      onChange={(e) => {
                        if (e.target.value === 'PAID') {
                          const amount = prompt('Enter payment amount (K):')
                          const notes = prompt('Payment notes (optional):')
                          if (amount) {
                            updatePaymentStatus(reg.id, 'PAID', parseFloat(amount), notes || '')
                          }
                        } else {
                          updatePaymentStatus(reg.id, e.target.value)
                        }
                      }}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                      defaultValue=""
                    >
                      <option value="">Select Status</option>
                      <option value="PAID">Mark as Paid</option>
                      <option value="UNPAID">Mark as Unpaid</option>
                      <option value="PARTIAL">Mark as Partial</option>
                      <option value="REFUNDED">Mark as Refunded</option>
                    </select>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                onClick={() => setShowPaymentModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Remove Player Modal */}
      {showRemoveModal && selectedPlayer && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Remove Player
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to remove <strong>{selectedPlayer.firstName} {selectedPlayer.lastName}</strong>? 
              This will delete their account and all registrations permanently.
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowRemoveModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={() => removePlayer(selectedPlayer.id)}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700"
              >
                Remove Player
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default function PlayerManagementPage() {
  return (
    <AdminAuthGuard>
      <PlayerManagementContent />
    </AdminAuthGuard>
  )
}