import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'
import { getCurrentTournamentWeek } from '@/lib/tournament-utils'

export async function GET() {
  try {
    const currentWeek = getCurrentTournamentWeek()
    const currentYear = new Date().getFullYear()

    // Get games with current week registration counts only
    const games = await prisma.game.findMany({
      orderBy: {
        name: 'asc'
      }
    })

    // For each game, count only current active registrations
    const gamesWithCounts = await Promise.all(
      games.map(async (game) => {
        // Count player registrations for scheduled tournaments only
        const scheduledTournamentRegistrations = await prisma.playerRegistration.count({
          where: {
            gameId: game.id,
            tournamentSchedule: {
              status: 'SCHEDULED' // Only count registrations for scheduled (not completed) tournaments
            }
          }
        })

        // Also count general player registrations not tied to specific tournaments
        const generalRegistrations = await prisma.playerRegistration.count({
          where: {
            gameId: game.id,
            tournamentScheduleId: null // General registrations
          }
        })

        return {
          ...game,
          _count: {
            registrations: scheduledTournamentRegistrations + generalRegistrations
          }
        }
      })
    )

    return NextResponse.json(gamesWithCounts)
  } catch (error) {
    logger.error('Error fetching games:', error)
    return NextResponse.json(
      { error: 'Failed to fetch games' },
      { status: 500 }
    )
  }
}
