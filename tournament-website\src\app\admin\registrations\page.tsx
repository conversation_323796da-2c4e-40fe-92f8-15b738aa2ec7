'use client'

import { getTournamentWeekForDate } from '@/lib/tournament-utils'
import { AdminAuthGuard } from '@/components/AdminAuthGuard'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { logger } from '@/lib/logger'
import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Registration {
  tournamentSchedule: any
  id: number
  gameUsername: string | null
  registrationDate: string
  user: {
    id: number
    username: string
    firstName: string
    lastName: string
    phoneNumber: string
  }
  game: {
    id: number
    name: string
  }
}

function AdminRegistrationsContent() {
  const [registrations, setRegistrations] = useState<Registration[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGame, setSelectedGame] = useState<string>('')
  const [selectedWeek, setSelectedWeek] = useState<string>('')
  const [games, setGames] = useState<{id: number, name: string}[]>([])
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(true)
  const router = useRouter()
  const { logout } = useAdminAuth()

  const fetchRegistrations = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true)

      const token = localStorage.getItem('adminToken')
      const response = await fetch('/api/admin/registrations', {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setRegistrations(data)
        setLastUpdated(new Date())
      } else if (response.status === 403 || response.status === 401) {
        // Token expired or invalid, redirect to login
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error fetching registrations:', error)
    } finally {
      if (showLoading) setLoading(false)
    }
  }

  const fetchGames = async () => {
    try {
      const response = await fetch('/api/games')
      if (response.ok) {
        const data = await response.json()
        setGames(data)
      }
    } catch (error) {
      logger.error('Error fetching games:', error)
    }
  }

  useEffect(() => {
    // Check if admin is logged in
    const token = localStorage.getItem('adminToken')
    if (!token) {
      router.push('/admin/login')
      return
    }

    fetchRegistrations()
    fetchGames()
  }, [router])

  // Auto-refresh functionality
  useEffect(() => {
    if (!isAutoRefreshEnabled) return

    const interval = setInterval(() => {
      fetchRegistrations(false) // Don't show loading on auto-refresh
    }, 3000) // Refresh every 3 seconds for registrations

    return () => clearInterval(interval)
  }, [isAutoRefreshEnabled])

  const toggleAutoRefresh = () => {
    setIsAutoRefreshEnabled(!isAutoRefreshEnabled)
  }

  const handleManualRefresh = () => {
    fetchRegistrations(true)
  }

  const handleLogout = () => {
    logout()
  }

  const filteredRegistrations = registrations.filter(reg => {
    // Filter by game if selected
    if (selectedGame && reg.game.name !== selectedGame) {
      return false
    }

    // Filter by week if selected
    if (selectedWeek) {
      if (selectedWeek === 'no-tournament') {
        // Show registrations not linked to any tournament
        return !reg.tournamentSchedule
      } else {
        // Show registrations for specific week
        const weekNumber = parseInt(selectedWeek)
        if (reg.tournamentSchedule) {
          const tournamentWeek = getTournamentWeekForDate(new Date(reg.tournamentSchedule.scheduledDate))
          return tournamentWeek === weekNumber
        }
        return false
      }
    }

    return true
  })

  const sortedRegistrations = filteredRegistrations.sort((a, b) => {
    // Sort by game first, then by registration date (newest first)
    if (a.game.name !== b.game.name) {
      return a.game.name.localeCompare(b.game.name)
    }
    return new Date(b.registrationDate).getTime() - new Date(a.registrationDate).getTime()
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading registrations...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="text-3xl font-bold text-blue-600">
                eSports RXP Admin
              </Link>
              {/* Real-time status indicator */}
              <div className="ml-4 flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isAutoRefreshEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
                <span className="text-xs text-gray-600">
                  {isAutoRefreshEnabled ? 'Live' : 'Paused'}
                </span>
                {lastUpdated && (
                  <span className="text-xs text-gray-500">
                    Updated: {lastUpdated.toLocaleTimeString()}
                  </span>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {/* Auto-refresh controls */}
              <button
                onClick={toggleAutoRefresh}
                className={`px-3 py-1 rounded-md text-sm font-medium ${
                  isAutoRefreshEnabled
                    ? 'bg-green-100 text-green-800 hover:bg-green-200'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
              >
                {isAutoRefreshEnabled ? 'Pause' : 'Enable'} Auto-Refresh
              </button>
              <button
                onClick={handleManualRefresh}
                className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 text-sm font-medium"
              >
                Refresh Now
              </button>
              <Link href="/admin/dashboard" className="text-gray-500 hover:text-gray-900">
                Dashboard
              </Link>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Tournament Registrations</h1>
          <p className="text-gray-600">All registration requests sent with user details and registered games</p>
        </div>

        {/* Filters */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="gameFilter" className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Game
            </label>
            <select
              id="gameFilter"
              name="gameFilter"
              value={selectedGame}
              onChange={(e) => setSelectedGame(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Games</option>
              {games.map(game => (
                <option key={game.id} value={game.name}>{game.name}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="weekFilter" className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Tournament Week
            </label>
            <select
              id="weekFilter"
              name="weekFilter"
              value={selectedWeek}
              onChange={(e) => setSelectedWeek(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Registrations</option>
              <option value="no-tournament">General Game Registration (No Tournament)</option>
              <option value="1">Week 1 Tournament</option>
              <option value="2">Week 2 Tournament</option>
              <option value="3">Week 3 Tournament</option>
              <option value="4">Week 4 Tournament</option>
              <option value="5">Week 5 Tournament</option>
              <option value="6">Week 6 Tournament</option>
              <option value="7">Week 7 Tournament</option>
              <option value="8">Week 8 Tournament</option>
            </select>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Registrations</h3>
            <p className="text-3xl font-bold text-blue-600">{registrations.length}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Filtered Results</h3>
            <p className="text-3xl font-bold text-green-600">{filteredRegistrations.length}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Unique Players</h3>
            <p className="text-3xl font-bold text-purple-600">
              {new Set(registrations.map(reg => reg.user.id)).size}
            </p>
          </div>
        </div>

        {/* Registrations List */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Registrations {selectedGame && `- ${selectedGame}`}
            </h2>
          </div>
          
          {sortedRegistrations.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              No registrations found {selectedGame && `for ${selectedGame}`}.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Player Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Game / Tournament
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Game Username
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Registration Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sortedRegistrations.map((registration) => (
                    <tr key={registration.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {registration.user.firstName} {registration.user.lastName}
                          </div>
                          <div className="text-sm text-gray-500">@{registration.user.username}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{registration.user.phoneNumber}</div>
                        <a 
                          href={`https://wa.me/${registration.user.phoneNumber.replace(/\D/g, '')}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-green-600 hover:text-green-800"
                        >
                          WhatsApp
                        </a>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {registration.game.name}
                          </span>
                          {registration.tournamentSchedule ? (
                            <div className="mt-1">
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                Week {getTournamentWeekForDate(new Date(registration.tournamentSchedule.scheduledDate))} Tournament
                              </span>
                              <div className="text-xs text-gray-500 mt-1">
                                {new Date(registration.tournamentSchedule.scheduledDate).toLocaleDateString()}
                              </div>
                            </div>
                          ) : (
                            <div className="mt-1">
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
                                General Registration
                              </span>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {registration.gameUsername || (
                            <span className="text-gray-400 italic">Not required</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {new Date(registration.registrationDate).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(registration.registrationDate).toLocaleTimeString()}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default function AdminRegistrationsPage() {
  return (
    <AdminAuthGuard>
      <AdminRegistrationsContent />
    </AdminAuthGuard>
  )
}