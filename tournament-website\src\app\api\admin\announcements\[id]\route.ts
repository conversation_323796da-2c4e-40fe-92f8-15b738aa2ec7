import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const PATCH = withAdminAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const body = await request.json()
    const { id } = await params
    const announcementId = parseInt(id)

    if (!announcementId) {
      return NextResponse.json(
        { error: 'Invalid announcement ID' },
        { status: 400 }
      )
    }

    const updatedAnnouncement = await prisma.announcement.update({
      where: { id: announcementId },
      data: body
    })

    return NextResponse.json(updatedAnnouncement)
  } catch (error) {
    logger.error('Error updating announcement:', error)
    return NextResponse.json(
      { error: 'Failed to update announcement' },
      { status: 500 }
    )
  }
})
