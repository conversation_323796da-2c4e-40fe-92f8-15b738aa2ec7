import { NextRequest, NextResponse } from 'next/server'
import { deleteSession } from '@/lib/auth'
import { cookies } from 'next/headers'
import { logger } from '@/lib/logger'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const sessionToken = cookieStore.get('session')?.value

    if (sessionToken) {
      // Delete session from database
      await deleteSession(sessionToken)
    }

    // Clear cookie
    cookieStore.delete('session')

    return NextResponse.json({
      message: 'Logout successful'
    })

  } catch (error) {
    logger.error('Logout error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
