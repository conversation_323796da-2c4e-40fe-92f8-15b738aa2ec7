// Load environment variables
require('dotenv').config({ path: '.env.local' })

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  console.log('🧹 Clearing all sample data and resetting tournament system...')

  try {
    // Delete in order to respect foreign key constraints
    console.log('Deleting user sessions...')
    await prisma.userSession.deleteMany({})

    console.log('Deleting weekly winners...')
    await prisma.weeklyWinner.deleteMany({})

    console.log('Deleting weekly tournaments...')
    await prisma.weeklyTournament.deleteMany({})

    console.log('Deleting tournament schedules...')
    await prisma.tournamentSchedule.deleteMany({})

    console.log('Deleting player stats...')
    await prisma.playerStats.deleteMany({})

    console.log('Deleting player registrations...')
    await prisma.playerRegistration.deleteMany({})

    console.log('Deleting announcements...')
    await prisma.announcement.deleteMany({})

    // Delete all users except the admin user 'Tournaowner'
    console.log('Deleting sample users (keeping admin)...')
    const deletedUsers = await prisma.user.deleteMany({
      where: {
        username: {
          not: 'Tournaowner'
        }
      }
    })
    console.log(`Deleted ${deletedUsers.count} sample users`)

    // Verify admin user exists, create if not
    const adminUser = await prisma.user.findUnique({
      where: { username: 'Tournaowner' }
    })

    if (!adminUser) {
      console.log('Creating admin user...')
      const bcrypt = require('bcryptjs')
      const hashedPassword = await bcrypt.hash('Bsvca2223', 12)

      await prisma.user.create({
        data: {
          username: 'Tournaowner',
          password: hashedPassword,
          firstName: 'Tournament',
          lastName: 'Owner',
          phoneNumber: '+265983132770',
          role: 'ADMIN'
        }
      })
      console.log('✅ Admin user created')
    } else {
      console.log('✅ Admin user already exists')
    }

    // Verify games exist (these should be preserved)
    const games = await prisma.game.findMany()
    console.log(`✅ Games preserved: ${games.map(g => g.name).join(', ')}`)

    console.log('\n🎉 Sample data cleared successfully!')
    console.log('Database is now clean and ready for fresh tournament season.')
    console.log('📅 Tournament system will start from Week 1 when new tournaments are created.')
    
  } catch (error) {
    console.error('❌ Error clearing sample data:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Script failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
