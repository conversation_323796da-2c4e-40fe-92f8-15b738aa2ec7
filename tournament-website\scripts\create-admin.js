require('dotenv').config()
const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAdmin() {
  try {
    console.log('Creating admin user...')
    
    // Hash the password
    const hashedPassword = await bcrypt.hash('Bsvca2223', 10)
    
    // Create admin user (without password field since it's not in schema)
    const admin = await prisma.user.create({
      data: {
        username: '<PERSON><PERSON>owner',
        firstName: 'Tournament',
        lastName: 'Owner',
        phoneNumber: '+265983132770',
        role: 'ADMIN'
      }
    })
    
    console.log('Admin user created successfully:', admin.username)
    console.log('Login with username: <PERSON><PERSON>owner, password: Bsvca2223')
    
  } catch (error) {
    if (error.code === 'P2002') {
      console.log('Admin user already exists - updating password...')
      
      // Update existing admin user
      await prisma.user.updateMany({
        where: { username: '<PERSON><PERSON>owner' },
        data: {
          phoneNumber: '+265983132770',
          role: 'ADMIN'
        }
      })
      console.log('Admin user updated successfully')
    } else {
      console.error('Error creating admin:', error)
    }
  } finally {
    await prisma.$disconnect()
  }
}

createAdmin()
