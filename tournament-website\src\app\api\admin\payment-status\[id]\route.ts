import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'
import { Decimal } from '@prisma/client/runtime/library'

export const PATCH = withAdminAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params
    const registrationId = parseInt(id)
    const body = await request.json()
    const { paymentStatus, paymentAmount, paymentNotes, paymentDate } = body

    if (!paymentStatus) {
      return NextResponse.json(
        { error: 'Payment status is required' },
        { status: 400 }
      )
    }

    // Validate payment status
    const validStatuses = ['PAID', 'UNPAID', 'PARTIAL', 'REFUNDED']
    if (!validStatuses.includes(paymentStatus)) {
      return NextResponse.json(
        { error: 'Invalid payment status' },
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: any = {
      paymentStatus
    }

    if (paymentAmount !== undefined && paymentAmount !== null) {
      updateData.paymentAmount = new Decimal(paymentAmount)
    }

    if (paymentNotes !== undefined) {
      updateData.paymentNotes = paymentNotes
    }

    if (paymentDate) {
      updateData.paymentDate = new Date(paymentDate)
    } else if (paymentStatus === 'PAID' && !paymentDate) {
      updateData.paymentDate = new Date()
    } else if (paymentStatus === 'UNPAID') {
      updateData.paymentDate = null
    }

    const updatedRegistration = await prisma.playerRegistration.update({
      where: { id: registrationId },
      data: updateData,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            phoneNumber: true
          }
        },
        game: {
          select: {
            name: true
          }
        },
        tournamentSchedule: {
          select: {
            tournamentId: true
          }
        }
      }
    })

    // Log the payment status change
    logger.info(`Payment status updated for registration ${registrationId}:`, {
      registrationId,
      paymentStatus,
      paymentAmount,
      user: `${updatedRegistration.user.firstName} ${updatedRegistration.user.lastName}`,
      game: updatedRegistration.game.name
    })

    return NextResponse.json({
      success: true,
      registration: {
        id: updatedRegistration.id,
        paymentStatus: updatedRegistration.paymentStatus,
        paymentDate: updatedRegistration.paymentDate?.toISOString(),
        paymentAmount: updatedRegistration.paymentAmount ? Number(updatedRegistration.paymentAmount) : null,
        paymentNotes: updatedRegistration.paymentNotes,
        user: updatedRegistration.user,
        game: updatedRegistration.game,
        tournamentSchedule: updatedRegistration.tournamentSchedule
      }
    })

  } catch (error) {
    logger.error('Error updating payment status:', error)
    return NextResponse.json(
      { error: 'Failed to update payment status' },
      { status: 500 }
    )
  }
})
