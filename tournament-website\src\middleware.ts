import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

/**
 * Validate admin token
 * @param token - The admin token to validate
 * @returns boolean - True if valid admin token
 */
function validateAdminToken(token: string): boolean {
  try {
    // Decode the token (format: base64(username:timestamp))
    const decoded = Buffer.from(token, 'base64').toString('utf-8')
    const [username, timestamp] = decoded.split(':')

    // Check if it's the admin username
    if (username === process.env.ADMIN_USERNAME || username === 'Tournaowner') {
      // Check if token is not too old (24 hours)
      const tokenTime = parseInt(timestamp)
      const now = Date.now()
      const maxAge = 24 * 60 * 60 * 1000 // 24 hours

      return (now - tokenTime) < maxAge
    }

    return false
  } catch (error) {
    return false
  }
}

/**
 * Check if user is authenticated as admin
 * @param request - The NextRequest object
 * @returns boolean - True if authenticated as admin
 */
function isAdminAuthenticated(request: NextRequest): boolean {
  // Check Authorization header (for API calls)
  const authHeader = request.headers.get('Authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7)
    if (validateAdminToken(token)) {
      return true
    }
  }

  // Check adminToken cookie (set by localStorage)
  const adminToken = request.cookies.get('adminToken')
  if (adminToken && validateAdminToken(adminToken.value)) {
    return true
  }

  // Check for admin token in custom header (for client-side requests)
  const customToken = request.headers.get('X-Admin-Token')
  if (customToken && validateAdminToken(customToken)) {
    return true
  }

  return false
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Protect all admin routes except login
  if (pathname.startsWith('/admin') && pathname !== '/admin/login') {
    const isAuthenticated = isAdminAuthenticated(request)
    
    if (!isAuthenticated) {
      // For API routes, return 401
      if (pathname.startsWith('/api/admin')) {
        return NextResponse.json(
          { error: 'Admin authentication required' },
          { status: 401 }
        )
      }
      
      // For admin pages, redirect to login
      const loginUrl = new URL('/admin/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }
  }

  // If accessing admin login while already authenticated, redirect to dashboard
  if (pathname === '/admin/login') {
    const isAuthenticated = isAdminAuthenticated(request)
    if (isAuthenticated) {
      return NextResponse.redirect(new URL('/admin/dashboard', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all admin routes:
     * - /admin/dashboard
     * - /admin/players
     * - /admin/registrations
     * - /admin/schedules
     * - /admin/announcements
     * - /admin/leaderboard
     * - /admin/stats
     * - /admin/monitoring
     * - /admin/player-management
     * - /admin/completed
     * - /admin/upcoming
     * - /api/admin/*
     */
    '/admin/:path*',
    '/api/admin/:path*'
  ]
}
