version: '3.8'

services:
  # Main application
  mzuni-tournaments:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mzuni-tournaments-app
    restart: unless-stopped
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - PORT=3002
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
    networks:
      - mzuni-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mzuni-tournaments-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: Gaming
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-Rodgers2004}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - mzuni-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d Gaming"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: mzuni-tournaments-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mzuni-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: mzuni-tournaments-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - mzuni-tournaments
    networks:
      - mzuni-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  mzuni-network:
    driver: bridge
