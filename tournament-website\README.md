# Mzuni Tournaments

A tournament registration and statistics website for PUBG, Call of Duty, and PES gaming tournaments.

## Features

- **Player Registration**: Students can register for weekend tournaments
- **Statistics & Leaderboards**: View player stats, win rates, and rankings
- **Tournament Schedules**: Check upcoming weekend tournament times
- **Winner Tracking**: Historical record of tournament winners
- **Multi-Game Support**: Separate tracking for PUBG, Call of Duty, and PES

## Tech Stack

- **Frontend**: Next.js 15 with React and TypeScript
- **Styling**: Tailwind CSS
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js (ready for future implementation)

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up environment variables**:
   Create a `.env.local` file with:
   ```
   DATABASE_URL="postgresql://postgres:Rodgers2004@localhost:5432/Gaming"
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key-here"
   NODE_ENV="development"
   ```

3. **Set up the database**:
   ```bash
   npx prisma db push
   ```

4. **Add sample data** (optional):
   ```bash
   node scripts/add-sample-data.js
   ```

5. **Start the development server**:
   ```bash
   npm run dev
   ```

6. **Open the website**:
   Visit [http://localhost:3000](http://localhost:3000)

## Pages

- **Homepage** (`/`) - Overview of games and recent results
- **Registration** (`/register`) - Player registration form
- **Stats** (`/stats`) - Leaderboards and player statistics
- **Schedule** (`/schedule`) - Upcoming tournament schedules

## Database Schema

The application uses a simplified schema focused on registration and statistics:
- `users` - Player information
- `games` - Available games (PUBG, Call of Duty, PES)
- `player_registrations` - Tournament registrations
- `player_stats` - Player statistics and win records
- `weekly_tournaments` - Tournament results
- `tournament_schedules` - Upcoming tournament schedules
- `weekly_winners` - Tournament winner records

## Note

This system is designed for registration and information display only. Actual tournament matches are played externally.
