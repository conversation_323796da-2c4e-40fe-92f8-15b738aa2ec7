const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkData() {
  try {
    const users = await prisma.user.count();
    const games = await prisma.game.count();
    const registrations = await prisma.playerRegistration.count();
    const schedules = await prisma.tournamentSchedule.count();
    const tournaments = await prisma.weeklyTournament.count();
    const announcements = await prisma.announcement.count();
    
    console.log('=== DATABASE STATUS ===');
    console.log('Users:', users);
    console.log('Games:', games);
    console.log('Player Registrations:', registrations);
    console.log('Tournament Schedules:', schedules);
    console.log('Weekly Tournaments:', tournaments);
    console.log('Announcements:', announcements);
    
    // Check admin user
    const admin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });
    console.log('Admin user exists:', admin ? 'YES' : 'NO');
    if (admin) {
      console.log('Admin username:', admin.username);
    }
    
    // Check games
    const gamesList = await prisma.game.findMany();
    console.log('Games available:', gamesList.map(g => g.name).join(', '));
    
    // Check recent schedules
    const recentSchedules = await prisma.tournamentSchedule.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      include: { game: true }
    });
    console.log('\nRecent Schedules:');
    recentSchedules.forEach(schedule => {
      console.log(`- ${schedule.tournamentId}: ${schedule.game.name} on ${schedule.scheduledDate}`);
    });
    
  } catch (error) {
    console.error('Database check error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkData();
