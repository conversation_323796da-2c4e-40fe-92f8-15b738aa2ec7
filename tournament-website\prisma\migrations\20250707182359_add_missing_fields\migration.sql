/*
  Warnings:

  - A unique constraint covering the columns `[tournament_id]` on the table `tournament_schedules` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `tournament_id` to the `tournament_schedules` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `scheduled_time` on the `tournament_schedules` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('PAID', 'UNPAID', 'PARTIAL', 'REFUNDED');

-- AlterTable
ALTER TABLE "player_registrations" ADD COLUMN     "payment_amount" DECIMAL(10,2),
ADD COLUMN     "payment_date" TIMESTAMP(3),
ADD COLUMN     "payment_notes" TEXT,
ADD COLUMN     "payment_status" "PaymentStatus" NOT NULL DEFAULT 'UNPAID',
ADD COLUMN     "tournament_schedule_id" INTEGER;

-- AlterTable
ALTER TABLE "tournament_schedules" ADD COLUMN     "current_participants" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "max_participants" INTEGER,
ADD COLUMN     "registration_deadline" TIMESTAMP(3),
ADD COLUMN     "tournament_id" TEXT NOT NULL,
DROP COLUMN "scheduled_time",
ADD COLUMN     "scheduled_time" VARCHAR(8) NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "tournament_schedules_tournament_id_key" ON "tournament_schedules"("tournament_id");

-- AddForeignKey
ALTER TABLE "player_registrations" ADD CONSTRAINT "player_registrations_tournament_schedule_id_fkey" FOREIGN KEY ("tournament_schedule_id") REFERENCES "tournament_schedules"("id") ON DELETE SET NULL ON UPDATE CASCADE;
