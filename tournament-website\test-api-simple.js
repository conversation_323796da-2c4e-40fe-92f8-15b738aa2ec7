const https = require('http');

// Create admin token
const adminToken = Buffer.from(`Tournaowner:${Date.now()}`).toString('base64');

console.log('Testing admin schedules API...');
console.log('Admin token:', adminToken);

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/admin/schedules',
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  }
};

const req = https.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', data);
    try {
      const parsed = JSON.parse(data);
      console.log('Parsed response:', parsed);
    } catch (e) {
      console.log('Could not parse JSON response');
    }
  });
});

req.on('error', (e) => {
  console.error(`Request error: ${e.message}`);
});

req.end();
