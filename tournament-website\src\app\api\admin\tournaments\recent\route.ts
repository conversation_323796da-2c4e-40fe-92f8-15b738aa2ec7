import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const recentTournaments = await prisma.weeklyTournament.findMany({
      orderBy: {
        tournamentDate: 'desc'
      },
      take: 20, // Get last 20 tournaments
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        },
        winner: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    return NextResponse.json(recentTournaments)
  } catch (error) {
    logger.error('Error fetching recent tournaments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch recent tournaments' },
      { status: 500 }
    )
  }
})
