const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  console.log('Adding sample players and registrations...')

  // Get games
  const games = await prisma.game.findMany()
  console.log('Found games:', games.map(g => g.name))

  // Sample players
  const players = [
    { username: 'player1', firstName: '<PERSON>', lastName: '<PERSON><PERSON>', phoneNumber: '+265991234567' },
    { username: 'player2', firstName: '<PERSON>', lastName: '<PERSON><PERSON>', phoneNumber: '+265992345678' },
    { username: 'player3', firstName: '<PERSON>', lastName: '<PERSON><PERSON><PERSON>', phoneNumber: '+265993456789' },
    { username: 'player4', firstName: '<PERSON>', lastName: 'Te<PERSON>', phoneNumber: '+265994567890' },
    { username: 'player5', firstName: '<PERSON>', lastName: '<PERSON><PERSON><PERSON>', phoneNumber: '+265995678901' }
  ]

  // Create players and registrations
  for (const playerData of players) {
    // Create user
    const user = await prisma.user.upsert({
      where: { username: playerData.username },
      update: {},
      create: playerData
    })

    console.log(`✅ Created user: ${user.username}`)

    // Register for random games
    for (const game of games) {
      if (Math.random() > 0.4) { // 60% chance to register for each game
        const gameUsername = game.name === 'PES' ? playerData.username : `${playerData.username}_${game.name.toLowerCase()}`

        // Create registration
        await prisma.playerRegistration.upsert({
          where: {
            userId_gameId: {
              userId: user.id,
              gameId: game.id
            }
          },
          update: {},
          create: {
            userId: user.id,
            gameId: game.id,
            gameUsername: gameUsername
          }
        })

        // Create stats
        const totalWins = Math.floor(Math.random() * 20) + 5
        const totalLosses = Math.floor(Math.random() * 15) + 2
        const totalGames = totalWins + totalLosses
        const winPercentage = totalGames > 0 ? (totalWins / totalGames) * 100 : 0

        await prisma.playerStats.upsert({
          where: {
            userId_gameId: {
              userId: user.id,
              gameId: game.id
            }
          },
          update: {},
          create: {
            userId: user.id,
            gameId: game.id,
            tournamentsParticipated: Math.floor(Math.random() * 10) + 1,
            tournamentsWon: Math.floor(Math.random() * 3),
            totalWins: totalWins,
            totalLosses: totalLosses,
            winPercentage: Math.round(winPercentage * 100) / 100
          }
        })

        console.log(`  📝 Registered ${user.username} for ${game.name}`)
      }
    }
  }

  // Win percentages are already calculated above

  console.log('✅ Sample players, registrations, and stats added successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error adding sample data:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
