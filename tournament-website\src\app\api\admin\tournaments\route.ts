import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')

    let whereClause = {}

    if (status === 'upcoming') {
      whereClause = { status: { in: ['UPCOMING', 'IN_PROGRESS'] } }
    } else if (status === 'completed') {
      whereClause = { status: 'COMPLETED' }
    }

    const tournaments = await prisma.weeklyTournament.findMany({
      where: whereClause,
      include: {
        game: true,
        winner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true
          }
        }
      },
      orderBy: {
        tournamentDate: 'asc'
      }
    })

    return NextResponse.json(tournaments)
  } catch (error) {
    logger.error('Error fetching tournaments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tournaments' },
      { status: 500 }
    )
  }
})
