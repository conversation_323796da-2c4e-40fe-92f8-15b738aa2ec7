'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface User {
  userId: number
  username: string
  firstName: string
  lastName: string
  phoneNumber: string
  email?: string
  dateOfBirth?: string
  gender?: string
  address?: string
  emergencyContact?: string
  profileLastEdited?: string
  role: string
}

interface PlayerStats {
  id: number
  gameId: number
  tournamentsParticipated: number
  tournamentsWon: number
  totalWins: number
  totalLosses: number
  winPercentage: number
  game: {
    id: number
    name: string
  }
}

interface DetailedStats {
  playerStats: PlayerStats[]
  registrations: Array<{
    id: number
    gameId: number
    gameUsername: string
    game: {
      id: number
      name: string
    }
  }>
  weeklyWins: Array<{
    id: number
    weekNumber: number
    year: number
    game: {
      id: number
      name: string
    }
  }>
  currentWeekRegistrationStatus: Array<{
    gameId: number
    gameName: string
    isRegistered: boolean
    tournamentDate: string
    weekNumber: number
  }>
  upcomingTournaments: Array<{
    id: number
    weekNumber: number
    year: number
    tournamentDate: string
    game: {
      id: number
      name: string
    }
  }>
  recentTournaments: Array<{
    id: number
    weekNumber: number
    year: number
    tournamentDate: string
    game: {
      id: number
      name: string
    }
    winner: {
      id: number
      username: string
      firstName: string
      lastName: string
    } | null
  }>
  overallStats: {
    totalTournamentsParticipated: number
    totalTournamentsWon: number
    totalWins: number
    totalLosses: number
    overallWinRate: number
    gamesRegistered: number
    currentWeek: number
    currentYear: number
  }
}

export default function ProfilePage() {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [stats, setStats] = useState<PlayerStats[]>([])
  const [detailedStats, setDetailedStats] = useState<DetailedStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deletePassword, setDeletePassword] = useState('')
  const [deleting, setDeleting] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')


  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
        await fetchStats(data.user.userId)
        await fetchDetailedStats()
      } else {
        router.push('/login')
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      router.push('/login')
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async (userId: number) => {
    try {
      const response = await fetch(`/api/user/stats?userId=${userId}`)
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const fetchDetailedStats = async () => {
    try {
      const response = await fetch('/api/user/detailed-stats')
      if (response.ok) {
        const data = await response.json()
        setDetailedStats(data)
      }
    } catch (error) {
      console.error('Error fetching detailed stats:', error)
    }
  }

  const handleDeleteAccount = async () => {
    if (!deletePassword.trim()) {
      setError('Password is required to delete account')
      return
    }

    setDeleting(true)
    setError('')

    try {
      const response = await fetch('/api/user/profile', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ password: deletePassword })
      })

      if (response.ok) {
        setMessage('Account deleted successfully. Redirecting...')
        setTimeout(() => {
          router.push('/')
        }, 2000)
      } else {
        const data = await response.json()
        setError(data.error || 'Failed to delete account')
      }
    } catch (error) {
      console.error('Error deleting account:', error)
      setError('An error occurred while deleting account')
    } finally {
      setDeleting(false)
    }
  }





  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      router.push('/')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/" className="text-3xl font-bold text-blue-600">Mzuni Tournaments</Link>
            </div>
            <nav className="flex space-x-8">
              <Link href="/" className="text-gray-500 hover:text-gray-900">Home</Link>
              <Link href="/register" className="text-gray-500 hover:text-gray-900">Register</Link>
              <Link href="/stats" className="text-gray-500 hover:text-gray-900">Stats</Link>
              <Link href="/schedule" className="text-gray-500 hover:text-gray-900">Schedule</Link>
              <span className="text-blue-600 font-medium">Profile</span>
              <button
                onClick={handleLogout}
                className="text-red-600 hover:text-red-700 font-medium"
              >
                Logout
              </button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Profile Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-white">
                  {user.firstName} {user.lastName}
                </h1>
                <p className="text-blue-100">@{user.username}</p>
              </div>
              <div className="flex items-center space-x-4">
                <Link
                  href="/tournament-register"
                  className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 font-medium"
                >
                  Register for Tournament
                </Link>
                <div className="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                  <p className="text-white text-sm">Player Since</p>
                  <p className="text-white font-semibold">2024</p>
                </div>
              </div>
            </div>
          </div>

          {message && (
            <div className="bg-green-50 border border-green-400 text-green-700 px-6 py-3">
              {message}
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-400 text-red-700 px-6 py-3">
              {error}
            </div>
          )}

          <div className="p-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Personal Information */}
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">Personal Information</h2>
                  <button
                    onClick={() => setShowDeleteModal(true)}
                    className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 font-medium"
                  >
                    Delete Account
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Username</p>
                      <p className="font-medium">{user.username}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Phone Number</p>
                      <p className="font-medium">{user.phoneNumber}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">First Name</p>
                      <p className="font-medium">{user.firstName}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Last Name</p>
                      <p className="font-medium">{user.lastName}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{user.email || 'Not provided'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Date of Birth</p>
                      <p className="font-medium">
                        {user.dateOfBirth ? new Date(user.dateOfBirth).toLocaleDateString() : 'Not provided'}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Gender</p>
                      <p className="font-medium">{user.gender || 'Not specified'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Address</p>
                      <p className="font-medium">{user.address || 'Not provided'}</p>
                    </div>
                  </div>
                </div>

                {/* Delete Account Modal */}
                {showDeleteModal && (
                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 w-full max-w-md">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Account</h3>
                      <p className="text-gray-600 mb-4">
                        This action cannot be undone. Please enter your password to confirm account deletion.
                      </p>
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Password
                        </label>
                        <input
                          type="password"
                          value={deletePassword}
                          onChange={(e) => setDeletePassword(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                          placeholder="Enter your password"
                        />
                      </div>
                      <div className="flex space-x-4">
                        <button
                          onClick={handleDeleteAccount}
                          disabled={deleting}
                          className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:opacity-50"
                        >
                          {deleting ? 'Deleting...' : 'Delete Account'}
                        </button>
                        <button
                          onClick={() => {
                            setShowDeleteModal(false)
                            setDeletePassword('')
                            setError('')
                          }}
                          className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
                        >
                          Cancel
                        </button>
                      </div>
                      {error && (
                        <p className="mt-4 text-red-600 text-sm">{error}</p>
                      )}
                    </div>
                  </div>
                )}

              {/* Enhanced Gaming Stats */}
              <div>
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Gaming Statistics & Activities</h2>

                {detailedStats ? (
                  <div className="space-y-6">
                    {/* Overall Stats Summary */}
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
                      <h3 className="font-semibold text-lg text-gray-900 mb-4">Overall Performance</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-blue-600">{detailedStats.overallStats.totalTournamentsParticipated}</p>
                          <p className="text-sm text-gray-600">Total Tournaments</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-green-600">{detailedStats.overallStats.totalTournamentsWon}</p>
                          <p className="text-sm text-gray-600">Tournaments Won</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-purple-600">{detailedStats.overallStats.overallWinRate}%</p>
                          <p className="text-sm text-gray-600">Win Rate</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-orange-600">{detailedStats.overallStats.gamesRegistered}</p>
                          <p className="text-sm text-gray-600">Games Registered</p>
                        </div>
                      </div>
                    </div>

                    {/* Current Week Registration Status */}
                    <div className="bg-yellow-50 rounded-lg p-6">
                      <h3 className="font-semibold text-lg text-gray-900 mb-4">
                        Week {detailedStats.overallStats.currentWeek} Tournament Status
                      </h3>
                      {detailedStats.currentWeekRegistrationStatus.length > 0 ? (
                        <div className="space-y-3">
                          {detailedStats.currentWeekRegistrationStatus.map((status) => (
                            <div key={status.gameId} className="flex items-center justify-between bg-white rounded-lg p-3">
                              <div>
                                <p className="font-medium text-gray-900">{status.gameName}</p>
                                <p className="text-sm text-gray-600">
                                  Tournament: {new Date(status.tournamentDate).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex items-center">
                                {status.isRegistered ? (
                                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    ✓ Registered
                                  </span>
                                ) : (
                                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                    ✗ Not Registered
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-600">No tournaments scheduled for this week.</p>
                      )}
                    </div>

                    {/* Game-specific Stats */}
                    {detailedStats.playerStats.length > 0 && (
                      <div>
                        <h3 className="font-semibold text-lg text-gray-900 mb-4">Game Statistics</h3>
                        <div className="space-y-4">
                          {detailedStats.playerStats.map((stat) => (
                            <div key={stat.id} className="bg-gray-50 rounded-lg p-4">
                              <h4 className="font-semibold text-lg text-gray-900 mb-3">{stat.game.name}</h4>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div>
                                  <p className="text-sm text-gray-500">Tournaments Played</p>
                                  <p className="text-2xl font-bold text-blue-600">{stat.tournamentsParticipated}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Tournaments Won</p>
                                  <p className="text-2xl font-bold text-green-600">{stat.tournamentsWon}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Total Wins</p>
                                  <p className="text-2xl font-bold text-purple-600">{stat.totalWins}</p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Win Rate</p>
                                  <p className="text-2xl font-bold text-orange-600">{stat.winPercentage.toFixed(1)}%</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-400 mb-4">
                      <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <p className="text-gray-500">Loading detailed statistics...</p>
                  </div>
                )}
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>
  )
}
