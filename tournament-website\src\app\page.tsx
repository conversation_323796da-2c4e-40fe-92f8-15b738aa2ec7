'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { logger } from '@/lib/logger'

interface Game {
  id: number
  name: string
  description: string
  _count: {
    registrations: number
  }
}

interface WeeklyTournament {
  id: number
  gameId: number
  weekNumber: number
  year: number
  tournamentDate: string
  status: string
  totalParticipants: number
  game: {
    name: string
  }
  winner: {
    username: string
    firstName: string
    lastName: string
  } | null
}

interface Announcement {
  id: number
  title: string
  content: string
  createdAt: string
}

interface User {
  id: number
  username: string
  firstName: string
  lastName: string
  role: string
}

export default function Home() {
  const [games, setGames] = useState<Game[]>([])
  const [recentTournaments, setRecentTournaments] = useState<WeeklyTournament[]>([])
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<User | null>(null)
  const [authLoading, setAuthLoading] = useState(true)

  useEffect(() => {
    fetchData()
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      }
    } catch (error) {
      logger.error('Auth check failed:', error)
    } finally {
      setAuthLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      setUser(null)
    } catch (error) {
      logger.error('Logout error:', error)
    }
  }

  const fetchData = async () => {
    try {
      const currentYear = new Date().getFullYear()
      const [gamesResponse, tournamentsResponse, announcementsResponse] = await Promise.all([
        fetch('/api/games'),
        fetch(`/api/weekly-tournaments?year=${currentYear}`),
        fetch('/api/announcements')
      ])

      if (gamesResponse.ok) {
        const gamesData = await gamesResponse.json()
        setGames(gamesData)
      }

      if (tournamentsResponse.ok) {
        const tournamentsData = await tournamentsResponse.json()
        // Sort by tournament date descending and show all recent tournaments
        const sortedTournaments = tournamentsData
          .sort((a: any, b: any) => new Date(b.tournamentDate).getTime() - new Date(a.tournamentDate).getTime())
        setRecentTournaments(sortedTournaments)
      }

      if (announcementsResponse.ok) {
        const announcementsData = await announcementsResponse.json()
        setAnnouncements(announcementsData)
      }
    } catch (error) {
      logger.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'UPCOMING': return 'bg-blue-100 text-blue-800'
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800'
      case 'COMPLETED': return 'bg-green-100 text-green-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-blue-600">eSports RXP</h1>
            </div>
            <nav className="flex space-x-6 items-center">
              <Link href="/" className="text-blue-600 font-medium p-2 hover:bg-blue-50 rounded-lg transition-colors" title="Home">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </Link>
              {user ? (
                <Link href="/schedule" className="text-gray-500 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors" title="Schedule">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                  </svg>
  
                </Link>
              ) : (
                <>
                  <Link href="/signup" className="text-gray-500 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors" title="Sign Up">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>

                  </Link>
                  <Link href="/login" className="text-gray-500 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors" title="Login">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>

                  </Link>
                </>
              )}
              <Link href="/leaderboard" className="text-gray-500 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors" title="Leaderboard">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 0 1 3.138-3.138z" />
                </svg>

              </Link>

              <Link href="/schedule" className="text-gray-500 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors" title="Schedule">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>

              </Link>
              <Link href="/how-it-works" className="text-gray-500 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors" title="How It Works">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>

              </Link>
              <a
                href="https://wa.me/265983132770"
                target="_blank"
                rel="noopener noreferrer"
                className="text-green-600 hover:text-green-700 p-2 hover:bg-green-50 rounded-lg transition-colors"
                title="Contact via WhatsApp"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>

              </a>

              {!authLoading && (
                <>
                  {user ? (
                    <div className="flex items-center space-x-4">
                      <Link
                        href="/profile"
                        className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 font-medium bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded-lg transition-colors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span>{user.firstName} {user.lastName}</span>
                      </Link>
                      {user.role === 'ADMIN' && (
                        <Link
                          href="/admin"
                          className="bg-red-600 text-white px-3 py-1 rounded-md text-sm hover:bg-red-700"
                        >
                          Admin
                        </Link>
                      )}
                      <button
                        onClick={handleLogout}
                        className="text-red-600 hover:text-red-700 p-2 hover:bg-red-50 rounded-lg transition-colors"
                        title="Logout"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>

                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-4">
                      <Link
                        href="/login"
                        className="text-gray-700 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors"
                        title="Login"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                        </svg>
                      </Link>
                      <Link
                        href="/signup"
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                        title="Sign Up"
                      >
                        Sign Up
                      </Link>
                    </div>
                  )}
                </>
              )}
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold sm:text-4xl">
              eSports RXP
            </h2>
            <p className="mt-4 text-lg max-w-3xl mx-auto">
              {user ? (
                <>Register for Call of Duty, PES and PUBG tournaments. Compete every weekend and track your stats!</>
              ) : (
                <>Join eSports RXP Tournaments! Create your account to register for PUBG, Call of Duty, and PES tournaments.</>
              )}
            </p>
            <div className="mt-6 space-x-4">
              {user ? (
                <Link
                  href="/schedule"
                  className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                >
                  View Tournament Schedule
                </Link>
              ) : (
                <>
                  
                </>
              )}
              {user && (
                <Link
                  href="/leaderboard"
                  className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
                >
                  View Leaderboard
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Announcements Section */}
      {announcements.length > 0 && (
        <div className="bg-yellow-50 border-b border-yellow-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="text-center mb-4">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">📢 Latest Announcements</h3>
            </div>
            <div className="space-y-4">
              {announcements.map((announcement) => (
                <div key={announcement.id} className="bg-white rounded-lg shadow-sm p-4 border-l-4 border-yellow-400">
                  <h4 className="font-semibold text-gray-900 mb-2">{announcement.title}</h4>
                  <p className="text-gray-600 text-sm mb-2">{announcement.content}</p>
                  <p className="text-xs text-gray-500">
                    {new Date(announcement.createdAt).toLocaleDateString()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Games Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Available Games</h3>
          <p className="text-gray-600">
            {user ? (
              "Choose your game and register for weekend tournaments"
            ) : (
              "Create an account to register for weekend tournaments"
            )}
          </p>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading games...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {games.map((game) => (
              <div key={game.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div className="p-8 text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-bold text-blue-600">{game.name.charAt(0)}</span>
                  </div>
                  <h4 className="text-2xl font-bold text-gray-900 mb-2">{game.name}</h4>
                  <p className="text-gray-600 mb-4">{game.description}</p>
                  <div className="text-3xl font-bold text-blue-600 mb-4">
                    {game._count.registrations}
                  </div>
                  <p className="text-sm text-gray-500 mb-6">Registered Players</p>
                  {user ? (
                    <Link
                      href="/schedule"
                      className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block"
                    >
                      View {game.name} Tournaments
                    </Link>
                  ) : (
                    <div className="space-y-2">
                      <Link
                        href="/signup"
                        className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block"
                      >
                        Create Account to Register
                      </Link>
                      <Link
                        href="/login"
                        className="w-full bg-gray-600 text-white py-2 px-6 rounded-lg font-medium hover:bg-gray-700 transition-colors inline-block text-sm"
                      >
                        Already have an account? Login
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Recent Tournaments Section */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900">Recent Tournament Results</h3>
          </div>

          {recentTournaments.length === 0 ? (
            <div className="text-center py-12 bg-white rounded-lg">
              <p className="text-gray-600">No tournament results yet.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentTournaments.map((tournament) => (
                <div key={tournament.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <h4 className="text-lg font-semibold text-gray-900">
                        {tournament.game.name} - Week {tournament.weekNumber}
                      </h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(tournament.status)}`}>
                        {tournament.status}
                      </span>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Date:</span>
                        <span className="font-medium">{formatDate(tournament.tournamentDate)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Participants:</span>
                        <span className="font-medium">{tournament.totalParticipants}</span>
                      </div>
                      {tournament.winner && (
                        <div className="flex justify-between">
                          <span className="text-gray-500">Winner:</span>
                          <span className="font-medium text-green-600">
                            {tournament.winner.username}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
