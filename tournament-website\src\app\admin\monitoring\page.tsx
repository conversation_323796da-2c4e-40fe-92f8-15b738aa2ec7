'use client'

import { logger } from '@/lib/logger'
import { AdminAuthGuard } from '@/components/AdminAuthGuard'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface HealthData {
  status: string
  timestamp: string
  uptime: number
  memory: {
    used: number
    total: number
    unit: string
  }
  requests: {
    total: number
    errors: number
    errorRate: string
  }
  database: {
    connected: boolean
    avgResponseTime: string
    errorRate: string
  }
  services: {
    database: string
    api: string
    monitoring: string
  }
}

interface MetricsData {
  timestamp: string
  period: string
  api: {
    totalRequests: number
    averageResponseTime: string
    slowRequests: number
  }
  database: {
    totalQueries: number
    averageQueryTime: string
    slowQueries: number
  }
  memory: {
    current: string
    peak: string
  }
  fullReport: string
}

function MonitoringContent() {
  const [healthData, setHealthData] = useState<HealthData | null>(null)
  const [metricsData, setMetricsData] = useState<MetricsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { logout } = useAdminAuth()

  useEffect(() => {
    fetchData()
    const interval = setInterval(fetchData, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const fetchData = async () => {
    try {
      const token = localStorage.getItem('adminToken')
      const headers = {
        'Authorization': `Bearer ${token}`
      }

      const [healthResponse, metricsResponse] = await Promise.all([
        fetch('/api/health', { headers }),
        fetch('/api/metrics', { headers })
      ])

      if (healthResponse.ok) {
        const health = await healthResponse.json()
        setHealthData(health)
      } else if (healthResponse.status === 403 || healthResponse.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
        return
      }

      if (metricsResponse.ok) {
        const metrics = await metricsResponse.json()
        setMetricsData(metrics)
      } else if (metricsResponse.status === 403 || metricsResponse.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
        return
      }

      setError(null)
    } catch (err) {
      logger.error('Failed to fetch monitoring data:', err)
      setError('Failed to load monitoring data')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'unhealthy':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-yellow-600 bg-yellow-100'
    }
  }

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading monitoring data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">System Monitoring</h1>
          <p className="mt-2 text-gray-600">Real-time performance and health monitoring</p>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Health Status */}
        {healthData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">System Status</h3>
              <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(healthData.status)}`}>
                {healthData.status.toUpperCase()}
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Uptime: {formatUptime(healthData.uptime)}
              </p>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Memory Usage</h3>
              <div className="text-2xl font-bold text-blue-600">
                {healthData.memory.used} {healthData.memory.unit}
              </div>
              <p className="text-sm text-gray-500">
                of {healthData.memory.total} {healthData.memory.unit}
              </p>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Requests</h3>
              <div className="text-2xl font-bold text-green-600">
                {healthData.requests.total}
              </div>
              <p className="text-sm text-gray-500">
                Error rate: {healthData.requests.errorRate}
              </p>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Database</h3>
              <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(healthData.database.connected ? 'healthy' : 'unhealthy')}`}>
                {healthData.database.connected ? 'CONNECTED' : 'DISCONNECTED'}
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Avg: {healthData.database.avgResponseTime}
              </p>
            </div>
          </div>
        )}

        {/* Services Status */}
        {healthData && (
          <div className="bg-white rounded-lg shadow mb-8">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Services Status</h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(healthData.services).map(([service, status]) => (
                  <div key={service} className="flex items-center justify-between p-4 border rounded-lg">
                    <span className="font-medium capitalize">{service}</span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status)}`}>
                      {status.toUpperCase()}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Performance Metrics */}
        {metricsData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">API Performance</h3>
                <p className="text-sm text-gray-500">Last {metricsData.period}</p>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex justify-between">
                  <span>Total Requests:</span>
                  <span className="font-medium">{metricsData.api.totalRequests}</span>
                </div>
                <div className="flex justify-between">
                  <span>Average Response Time:</span>
                  <span className="font-medium">{metricsData.api.averageResponseTime}</span>
                </div>
                <div className="flex justify-between">
                  <span>Slow Requests (&gt;1s):</span>
                  <span className={`font-medium ${metricsData.api.slowRequests > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {metricsData.api.slowRequests}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Database Performance</h3>
                <p className="text-sm text-gray-500">Last {metricsData.period}</p>
              </div>
              <div className="p-6 space-y-4">
                <div className="flex justify-between">
                  <span>Total Queries:</span>
                  <span className="font-medium">{metricsData.database.totalQueries}</span>
                </div>
                <div className="flex justify-between">
                  <span>Average Query Time:</span>
                  <span className="font-medium">{metricsData.database.averageQueryTime}</span>
                </div>
                <div className="flex justify-between">
                  <span>Slow Queries (&gt;500ms):</span>
                  <span className={`font-medium ${metricsData.database.slowQueries > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {metricsData.database.slowQueries}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Performance Report */}
        {metricsData && (
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Detailed Performance Report</h3>
            </div>
            <div className="p-6">
              <pre className="text-sm bg-gray-50 p-4 rounded-lg overflow-x-auto whitespace-pre-wrap">
                {metricsData.fullReport}
              </pre>
            </div>
          </div>
        )}

        <div className="mt-8 text-center">
          <button
            onClick={fetchData}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Refresh Data
          </button>
        </div>
      </div>
    </div>
  )
}

export default function MonitoringPage() {
  return (
    <AdminAuthGuard>
      <MonitoringContent />
    </AdminAuthGuard>
  )
}