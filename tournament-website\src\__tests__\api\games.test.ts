/**
 * @jest-environment node
 */

import { GET } from '@/app/api/games/route'
import { NextRequest } from 'next/server'

// Mock Prisma
jest.mock('@/lib/db', () => ({
  prisma: {
    game: {
      findMany: jest.fn(),
    },
  },
}))

// Mock logger
jest.mock('@/lib/logger', () => ({
  logger: {
    error: jest.fn(),
  },
}))

describe('/api/games', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should return games with registration counts', async () => {
    const mockGames = [
      {
        id: 1,
        name: 'PUBG',
        description: 'PlayerUnknown\'s Battlegrounds',
        createdAt: new Date(),
        _count: { registrations: 5 }
      },
      {
        id: 2,
        name: 'Call of Duty',
        description: 'Call of Duty Mobile',
        createdAt: new Date(),
        _count: { registrations: 3 }
      }
    ]

    const { prisma } = require('@/lib/db')
    prisma.game.findMany.mockResolvedValue(mockGames)

    const response = await GET()
    const data = await response.json()

    expect(response.status).toBe(200)
    expect(data).toEqual(mockGames)
    expect(prisma.game.findMany).toHaveBeenCalledWith({
      include: {
        _count: {
          select: {
            registrations: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })
  })

  it('should handle database errors', async () => {
    const { prisma } = require('@/lib/db')
    const { logger } = require('@/lib/logger')
    
    prisma.game.findMany.mockRejectedValue(new Error('Database error'))

    const response = await GET()
    const data = await response.json()

    expect(response.status).toBe(500)
    expect(data).toEqual({ error: 'Failed to fetch games' })
    expect(logger.error).toHaveBeenCalledWith('Error fetching games:', expect.any(Error))
  })
})
