const https = require('http')

async function testAPI() {
  console.log('🌐 TESTING API ENDPOINTS')
  console.log('=' .repeat(40))

  const endpoints = [
    { path: '/api/games', method: 'GET', name: 'Games API' },
    { path: '/api/stats', method: 'GET', name: 'Stats API' },
    { path: '/api/weekly-tournaments', method: 'GET', name: 'Weekly Tournaments API' },
    { path: '/api/admin/players', method: 'GET', name: 'Admin Players API' },
    { path: '/api/admin/registrations', method: 'GET', name: 'Admin Registrations API' },
    { path: '/api/admin/stats', method: 'GET', name: 'Admin Stats API' },
    { path: '/api/admin/tournaments', method: 'GET', name: 'Admin Tournaments API' },
    { path: '/api/admin/schedules', method: 'GET', name: 'Admin Schedules API' },
    { path: '/api/admin/announcements', method: 'GET', name: 'Admin Announcements API' }
  ]

  for (const endpoint of endpoints) {
    try {
      const result = await makeRequest(endpoint.path, endpoint.method)
      console.log(`✅ ${endpoint.name}: ${result.status} - ${result.data ? 'Data received' : 'No data'}`)
      
      if (result.data && Array.isArray(result.data)) {
        console.log(`   📊 Records: ${result.data.length}`)
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: ${error.message}`)
    }
  }

  console.log('\n🎯 API TESTING COMPLETED')
}

function makeRequest(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    }

    const req = https.request(options, (res) => {
      let data = ''
      
      res.on('data', (chunk) => {
        data += chunk
      })
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data)
          resolve({
            status: res.statusCode,
            data: jsonData
          })
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data
          })
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    req.setTimeout(5000, () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })

    req.end()
  })
}

testAPI().catch(console.error)
