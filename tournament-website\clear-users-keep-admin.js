#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')

async function clearUsersKeepAdmin() {
  const prisma = new PrismaClient()
  
  try {
    console.log('👑 CLEARING USERS (KEEPING ADMIN ONLY)')
    console.log('=====================================\n')

    // Get current user count
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true
      }
    })

    console.log(`👥 Current users in database: ${allUsers.length}`)
    allUsers.forEach(user => {
      const status = user.role === 'ADMIN' ? '👑 ADMIN (WILL KEEP)' : '🗑️ WILL DELETE'
      console.log(`   - ${user.username} (${user.firstName} ${user.lastName}) - ${status}`)
    })

    // Find admin user
    const adminUser = await prisma.user.findUnique({
      where: { username: 'Tournaowner' },
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true
      }
    })

    if (!adminUser) {
      console.log('\n❌ ERROR: Admin user "Tournaowner" not found!')
      console.log('   Cannot proceed without admin user.')
      return
    }

    console.log(`\n👑 Admin user found: ${adminUser.username} (ID: ${adminUser.id})`)

    // Get non-admin users
    const nonAdminUsers = await prisma.user.findMany({
      where: {
        NOT: {
          username: 'Tournaowner'
        }
      },
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true
      }
    })

    console.log(`\n🗑️  Users to be deleted: ${nonAdminUsers.length}`)
    nonAdminUsers.forEach(user => {
      console.log(`   - ${user.username} (${user.firstName} ${user.lastName})`)
    })

    if (nonAdminUsers.length === 0) {
      console.log('\n✅ No non-admin users to delete. Only admin exists.')
      return
    }

    console.log('\n🧹 Starting cleanup process...')

    // Clear data related to non-admin users
    console.log('\n1. Clearing user sessions...')
    const deletedSessions = await prisma.userSession.deleteMany({
      where: {
        userId: {
          in: nonAdminUsers.map(u => u.id)
        }
      }
    })
    console.log(`   ✅ Cleared ${deletedSessions.count} user sessions`)

    console.log('\n2. Clearing player registrations...')
    const deletedRegistrations = await prisma.playerRegistration.deleteMany({
      where: {
        userId: {
          in: nonAdminUsers.map(u => u.id)
        }
      }
    })
    console.log(`   ✅ Cleared ${deletedRegistrations.count} player registrations`)

    console.log('\n3. Clearing player statistics...')
    const deletedStats = await prisma.playerStats.deleteMany({
      where: {
        userId: {
          in: nonAdminUsers.map(u => u.id)
        }
      }
    })
    console.log(`   ✅ Cleared ${deletedStats.count} player statistics`)

    console.log('\n4. Clearing weekly winners...')
    const deletedWinners = await prisma.weeklyWinner.deleteMany({
      where: {
        userId: {
          in: nonAdminUsers.map(u => u.id)
        }
      }
    })
    console.log(`   ✅ Cleared ${deletedWinners.count} weekly winners`)

    console.log('\n5. Clearing weekly tournament winners...')
    const updatedTournaments = await prisma.weeklyTournament.updateMany({
      where: {
        winnerId: {
          in: nonAdminUsers.map(u => u.id)
        }
      },
      data: {
        winnerId: null
      }
    })
    console.log(`   ✅ Cleared ${updatedTournaments.count} tournament winner references`)

    console.log('\n6. Deleting non-admin users...')
    const deletedUsers = await prisma.user.deleteMany({
      where: {
        NOT: {
          username: 'Tournaowner'
        }
      }
    })
    console.log(`   ✅ Deleted ${deletedUsers.count} non-admin users`)

    // Verify final state
    const finalUsers = await prisma.user.findMany({
      select: {
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true
      }
    })

    console.log('\n✅ CLEANUP COMPLETE!')
    console.log(`   Final user count: ${finalUsers.length}`)
    
    console.log('\n👑 Remaining users:')
    finalUsers.forEach(user => {
      console.log(`   - ${user.username} (${user.firstName} ${user.lastName}) - ${user.role}`)
    })

    // Check games and other data
    const gameCount = await prisma.game.count()
    const scheduleCount = await prisma.tournamentSchedule.count()
    
    console.log('\n📊 System Status:')
    console.log(`   Users: ${finalUsers.length} (Admin only)`)
    console.log(`   Games: ${gameCount} (Preserved)`)
    console.log(`   Schedules: ${scheduleCount} (Preserved)`)

    console.log('\n🔑 Admin Login Credentials:')
    console.log('   Username: Tournaowner')
    console.log('   Password: Bsvca2223')
    console.log('   URL: http://localhost:3000/admin/login')

    console.log('\n🌐 Website Status:')
    console.log('   ✅ Ready for new user registrations')
    console.log('   ✅ Admin can manage tournaments')
    console.log('   ✅ Registration counts reset to 0')
    console.log('   ✅ Clean slate for new players')

  } catch (error) {
    console.error('❌ Error clearing users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

clearUsersKeepAdmin()
