-- Mzuni Tournaments Database Schema
-- Created for PUBG, Call of Duty, and PES tournament registration and stats

-- Users table for player registration and profiles
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    phone_number VARCHAR(20),
    student_id VARCHAR(20),
    role VARCHAR(20) DEFAULT 'player' CHECK (role IN ('admin', 'player')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Games table - fixed for PUB<PERSON>, Call of Duty, and PES
CREATE TABLE IF NOT EXISTS games (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Player registrations for each game
CREATE TABLE IF NOT EXISTS player_registrations (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    game_id INTEGER REFERENCES games(id) ON DELETE CASCADE,
    game_username VARCHAR(100) NOT NULL, -- Their in-game username
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(user_id, game_id)
);

-- Weekly tournaments/matches
CREATE TABLE IF NOT EXISTS weekly_tournaments (
    id SERIAL PRIMARY KEY,
    game_id INTEGER REFERENCES games(id) ON DELETE CASCADE,
    week_number INTEGER NOT NULL,
    year INTEGER NOT NULL,
    tournament_date DATE NOT NULL, -- Weekend date
    status VARCHAR(20) DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'in_progress', 'completed', 'cancelled')),
    winner_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    total_participants INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(game_id, week_number, year)
);

-- Player statistics for each game
CREATE TABLE IF NOT EXISTS player_stats (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    game_id INTEGER REFERENCES games(id) ON DELETE CASCADE,
    tournaments_participated INTEGER DEFAULT 0,
    tournaments_won INTEGER DEFAULT 0,
    total_wins INTEGER DEFAULT 0,
    total_losses INTEGER DEFAULT 0,
    win_percentage DECIMAL(5,2) DEFAULT 0.00,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, game_id)
);

-- Tournament schedules
CREATE TABLE IF NOT EXISTS tournament_schedules (
    id SERIAL PRIMARY KEY,
    game_id INTEGER REFERENCES games(id) ON DELETE CASCADE,
    scheduled_date DATE NOT NULL,
    scheduled_time TIME NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Weekly winners tracking
CREATE TABLE IF NOT EXISTS weekly_winners (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    game_id INTEGER REFERENCES games(id) ON DELETE CASCADE,
    week_number INTEGER NOT NULL,
    year INTEGER NOT NULL,
    tournament_id INTEGER REFERENCES weekly_tournaments(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(game_id, week_number, year)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_player_registrations_user_id ON player_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_player_registrations_game_id ON player_registrations(game_id);
CREATE INDEX IF NOT EXISTS idx_weekly_tournaments_game_id ON weekly_tournaments(game_id);
CREATE INDEX IF NOT EXISTS idx_weekly_tournaments_date ON weekly_tournaments(tournament_date);
CREATE INDEX IF NOT EXISTS idx_player_stats_user_id ON player_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_player_stats_game_id ON player_stats(game_id);
CREATE INDEX IF NOT EXISTS idx_weekly_winners_game_id ON weekly_winners(game_id);
CREATE INDEX IF NOT EXISTS idx_weekly_winners_week_year ON weekly_winners(week_number, year);

-- Insert the three games for Mzuni Tournaments
INSERT INTO games (name, description) VALUES
('PUBG', 'PlayerUnknown''s Battlegrounds - Battle Royale Game'),
('Call of Duty', 'First-person shooter military game'),
('PES', 'Pro Evolution Soccer - Football simulation game')
ON CONFLICT DO NOTHING;

-- Create a default admin user
INSERT INTO users (username, email, first_name, last_name, role) VALUES
('admin', '<EMAIL>', 'Admin', 'User', 'admin')
ON CONFLICT DO NOTHING;
