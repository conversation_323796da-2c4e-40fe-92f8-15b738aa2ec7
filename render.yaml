services:
  # PostgreSQL Database
  - type: pserv
    name: mzuni-tournaments-db
    env: node
    plan: free
    region: oregon
    databases:
      - name: Gaming
        user: postgres
    envVars:
      - key: POSTGRES_DB
        value: Gaming
      - key: POSTGRES_USER
        value: postgres

  # Next.js Web Application
  - type: web
    name: mzuni-tournaments-web
    env: node
    region: oregon
    plan: free
    buildCommand: cd tournament-website && npm run render:build
    startCommand: cd tournament-website && npm run render:start
    envVars:
      - key: NODE_ENV
        value: production
      - key: DATABASE_URL
        fromDatabase:
          name: mzuni-tournaments-db
          property: connectionString
      - key: NEXTAUTH_URL
        fromService:
          type: web
          name: mzuni-tournaments-web
          property: host
      - key: NEXTAUTH_SECRET
        generateValue: true
      - key: ADMIN_USERNAME
        value: Tournaowner
      - key: ADMIN_PASSWORD
        sync: false
      - key: WHATSAPP_NUMBER
        value: "+265983132770"
    healthCheckPath: /api/healthz
    domains:
      - mzuni-tournaments.onrender.com
    autoDeploy: true
    branch: main
    rootDir: tournament-website
    
    # Build settings
    buildFilter:
      paths:
        - tournament-website/**
      ignoredPaths:
        - tournament-website/node_modules/**
        - tournament-website/.next/**
        - tournament-website/logs/**
        - tournament-website/*.md
        - tournament-website/test-*.js
        - tournament-website/*.bat
        - tournament-website/*.ps1

    # Runtime settings
    disk:
      name: mzuni-tournaments-disk
      mountPath: /opt/render/project/src/tournament-website/logs
      sizeGB: 1

# Environment Groups (for shared environment variables)
envVarGroups:
  - name: tournament-shared-config
    envVars:
      - key: TOURNAMENT_SEASON_START
        value: "2025-01-12"
      - key: MAX_PLAYERS_PES
        value: "32"
      - key: MAX_PLAYERS_PUBG
        value: "50"
      - key: MAX_PLAYERS_COD
        value: "50"
      - key: PRIZE_DISTRIBUTION_TOP4
        value: "45,25,15,5"
      - key: PRIZE_DISTRIBUTION_TOP10
        value: "28,20,14,10,8,6,5,4,3,2"
