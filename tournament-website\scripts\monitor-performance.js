#!/usr/bin/env node

const http = require('http')

console.log('🔍 MZUNI TOURNAMENTS - PERFORMANCE MONITOR')
console.log('==========================================\n')

const BASE_URL = 'http://localhost:3000'

async function checkHealth() {
  return new Promise((resolve) => {
    const req = http.get(`${BASE_URL}/api/health`, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        try {
          const health = JSON.parse(data)
          resolve({ success: true, data: health, status: res.statusCode })
        } catch (error) {
          resolve({ success: false, error: 'Invalid JSON response', status: res.statusCode })
        }
      })
    })
    
    req.on('error', (error) => {
      resolve({ success: false, error: error.message, status: 0 })
    })
    
    req.setTimeout(5000, () => {
      req.destroy()
      resolve({ success: false, error: 'Request timeout', status: 0 })
    })
  })
}

async function testEndpoints() {
  const endpoints = [
    '/api/games',
    '/api/announcements',
    '/api/weekly-tournaments?year=2025'
  ]
  
  const results = []
  
  for (const endpoint of endpoints) {
    const startTime = Date.now()
    
    try {
      const result = await new Promise((resolve) => {
        const req = http.get(`${BASE_URL}${endpoint}`, (res) => {
          let data = ''
          res.on('data', chunk => data += chunk)
          res.on('end', () => {
            const duration = Date.now() - startTime
            resolve({
              endpoint,
              status: res.statusCode,
              duration,
              success: res.statusCode < 400
            })
          })
        })
        
        req.on('error', (error) => {
          const duration = Date.now() - startTime
          resolve({
            endpoint,
            status: 0,
            duration,
            success: false,
            error: error.message
          })
        })
        
        req.setTimeout(10000, () => {
          req.destroy()
          const duration = Date.now() - startTime
          resolve({
            endpoint,
            status: 0,
            duration,
            success: false,
            error: 'Timeout'
          })
        })
      })
      
      results.push(result)
    } catch (error) {
      results.push({
        endpoint,
        status: 0,
        duration: Date.now() - startTime,
        success: false,
        error: error.message
      })
    }
  }
  
  return results
}

async function runMonitoring() {
  console.log(`🏥 Health Check - ${new Date().toISOString()}`)
  console.log('─'.repeat(50))
  
  const health = await checkHealth()
  
  if (health.success) {
    const data = health.data
    console.log(`✅ System Status: ${data.status.toUpperCase()}`)
    console.log(`⏱️  Uptime: ${Math.floor(data.uptime / 60)}m ${data.uptime % 60}s`)
    console.log(`💾 Memory: ${data.memory.used}/${data.memory.total} ${data.memory.unit}`)
    console.log(`📊 Requests: ${data.requests.total} (${data.requests.errorRate} error rate)`)
    console.log(`🗄️  Database: ${data.database.connected ? 'Connected' : 'Disconnected'} (${data.database.avgResponseTime})`)
    
    // Check for alerts
    const memoryUsage = (data.memory.used / data.memory.total) * 100
    if (memoryUsage > 80) {
      console.log(`⚠️  HIGH MEMORY USAGE: ${memoryUsage.toFixed(1)}%`)
    }
    
    const errorRate = parseFloat(data.requests.errorRate)
    if (errorRate > 5) {
      console.log(`⚠️  HIGH ERROR RATE: ${data.requests.errorRate}`)
    }
    
  } else {
    console.log(`❌ Health check failed: ${health.error}`)
  }
  
  console.log('\n🌐 API Endpoint Tests')
  console.log('─'.repeat(50))
  
  const endpointResults = await testEndpoints()
  
  for (const result of endpointResults) {
    const status = result.success ? '✅' : '❌'
    const duration = result.duration < 1000 ? `${result.duration}ms` : `${(result.duration/1000).toFixed(1)}s`
    
    console.log(`${status} ${result.endpoint} - ${result.status} (${duration})`)
    
    if (!result.success && result.error) {
      console.log(`   Error: ${result.error}`)
    }
    
    if (result.duration > 2000) {
      console.log(`   ⚠️  Slow response: ${duration}`)
    }
  }
  
  // Summary
  const successfulEndpoints = endpointResults.filter(r => r.success).length
  const totalEndpoints = endpointResults.length
  const avgResponseTime = endpointResults.reduce((sum, r) => sum + r.duration, 0) / totalEndpoints
  
  console.log('\n📈 Summary')
  console.log('─'.repeat(50))
  console.log(`Endpoint Success Rate: ${successfulEndpoints}/${totalEndpoints} (${((successfulEndpoints/totalEndpoints)*100).toFixed(1)}%)`)
  console.log(`Average Response Time: ${avgResponseTime.toFixed(0)}ms`)
  
  if (successfulEndpoints === totalEndpoints && health.success) {
    console.log('🎉 All systems operational!')
  } else {
    console.log('⚠️  Some issues detected - check logs above')
  }
}

// Run monitoring
runMonitoring().catch(error => {
  console.error('❌ Monitoring failed:', error.message)
  process.exit(1)
})
