const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createWeek1Tournament() {
  console.log('🏆 Creating Week 1 Tournament to test fresh start...\n')

  try {
    // Get games
    const games = await prisma.game.findMany()
    console.log(`Found ${games.length} games: ${games.map(g => g.name).join(', ')}\n`)

    // Create Week 1 tournaments for all games
    const currentYear = new Date().getFullYear()
    const week1Date = new Date(currentYear, 0, 7) // January 7th (first Saturday of the year)

    for (const game of games) {
      // Check if Week 1 tournament already exists
      const existingTournament = await prisma.weeklyTournament.findFirst({
        where: {
          gameId: game.id,
          weekNumber: 1,
          year: currentYear
        }
      })

      if (existingTournament) {
        console.log(`⚠️  Week 1 tournament for ${game.name} already exists`)
        continue
      }

      // Create Week 1 tournament
      const tournament = await prisma.weeklyTournament.create({
        data: {
          gameId: game.id,
          weekNumber: 1,
          year: currentYear,
          tournamentDate: week1Date,
          status: 'UPCOMING',
          totalParticipants: 0
        },
        include: {
          game: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      console.log(`✅ Created Week 1 tournament for ${game.name}`)
      console.log(`   Tournament ID: ${tournament.id}`)
      console.log(`   Date: ${tournament.tournamentDate.toLocaleDateString()}`)
      console.log(`   Status: ${tournament.status}`)
    }

    // Verify the tournaments were created
    console.log('\n📊 Verifying Week 1 tournaments...')
    const week1Tournaments = await prisma.weeklyTournament.findMany({
      where: {
        weekNumber: 1,
        year: currentYear
      },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    console.log(`\n✅ Week 1 Tournaments Created:`)
    week1Tournaments.forEach(tournament => {
      console.log(`   ${tournament.game.name}: Week ${tournament.weekNumber}, ${tournament.year}`)
    })

    // Test admin counting with tournaments
    console.log('\n📈 Testing admin counting with tournaments...')
    const totalPlayers = await prisma.user.count({
      where: { role: 'PLAYER' }
    })
    
    const totalRegistrations = await prisma.playerRegistration.count()
    
    const upcomingTournaments = await prisma.weeklyTournament.count({
      where: {
        status: 'UPCOMING'
      }
    })

    console.log(`\n📊 Admin Dashboard Stats:`)
    console.log(`   Total Players: ${totalPlayers}`)
    console.log(`   Total Registrations: ${totalRegistrations}`)
    console.log(`   Upcoming Tournaments: ${upcomingTournaments}`)

    console.log('\n🎉 Week 1 tournaments created successfully!')
    console.log('Tournament system is now ready to start from Week 1.')

  } catch (error) {
    console.error('❌ Error creating Week 1 tournaments:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
createWeek1Tournament()
