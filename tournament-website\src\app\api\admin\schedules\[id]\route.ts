import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const PATCH = withAdminAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const body = await request.json()
    const { id } = await params
    const scheduleId = parseInt(id)

    if (!scheduleId) {
      return NextResponse.json(
        { error: 'Invalid schedule ID' },
        { status: 400 }
      )
    }

    // Get the current schedule to check if status is changing to COMPLETED
    const currentSchedule = await prisma.tournamentSchedule.findUnique({
      where: { id: scheduleId },
      include: { game: true }
    })

    if (!currentSchedule) {
      return NextResponse.json(
        { error: 'Schedule not found' },
        { status: 404 }
      )
    }

    const updatedSchedule = await prisma.tournamentSchedule.update({
      where: { id: scheduleId },
      data: body,
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // If status is being changed to COMPLETED, reset registration counts for new week
    if (body.status === 'COMPLETED' && currentSchedule.status !== 'COMPLETED') {
      try {
        // Clear player registrations for this specific tournament schedule
        await prisma.playerRegistration.deleteMany({
          where: {
            tournamentScheduleId: scheduleId
          }
        })

        // Also clear any general game registrations for this game to start fresh
        await prisma.playerRegistration.deleteMany({
          where: {
            gameId: currentSchedule.gameId,
            tournamentScheduleId: null // Clear general registrations not tied to specific tournaments
          }
        })

        logger.info(`✅ Reset registration counts for ${currentSchedule.game.name} after tournament completion`)
      } catch (resetError) {
        logger.error('Error resetting registration counts:', resetError)
        // Don't fail the status update if reset fails
      }
    }

    return NextResponse.json(updatedSchedule)
  } catch (error) {
    logger.error('Error updating schedule:', error)
    return NextResponse.json(
      { error: 'Failed to update schedule' },
      { status: 500 }
    )
  }
})
