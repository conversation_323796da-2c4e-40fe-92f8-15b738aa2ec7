#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client')

console.log('🗄️  COMPREHENSIVE DATA INTEGRITY CHECK')
console.log('=====================================\n')

const prisma = new PrismaClient()

async function checkDataIntegrity() {
  let totalIssues = 0
  let criticalIssues = 0
  let warningIssues = 0

  try {
    // 1. Check database connection
    console.log('1. 🔌 DATABASE CONNECTION CHECK')
    try {
      await prisma.$connect()
      console.log('✅ Database connection successful')
    } catch (error) {
      console.log('❌ CRITICAL: Database connection failed')
      console.log(error.message)
      criticalIssues++
      return
    }

    // 2. Check all tables exist and have data
    console.log('\n2. 📊 TABLE EXISTENCE & DATA CHECK')
    
    const tables = [
      { name: 'User', model: prisma.user },
      { name: 'Game', model: prisma.game },
      { name: 'WeeklyTournament', model: prisma.weeklyTournament },
      { name: 'PlayerRegistration', model: prisma.playerRegistration },
      { name: 'PlayerStats', model: prisma.playerStats },
      { name: 'WeeklyWinner', model: prisma.weeklyWinner },
      { name: 'Announcement', model: prisma.announcement },
      { name: 'TournamentSchedule', model: prisma.tournamentSchedule },
      { name: 'UserSession', model: prisma.userSession }
    ]

    for (const table of tables) {
      try {
        const count = await table.model.count()
        console.log(`✅ ${table.name}: ${count} records`)
        
        if (table.name === 'Game' && count === 0) {
          console.log('⚠️  WARNING: No games found - this may affect functionality')
          warningIssues++
        }
      } catch (error) {
        console.log(`❌ CRITICAL: ${table.name} table error - ${error.message}`)
        criticalIssues++
      }
    }

    // 3. Check data relationships integrity
    console.log('\n3. 🔗 RELATIONSHIP INTEGRITY CHECK')
    
    // Check registration relationships by trying to include related data
    try {
      const registrationsWithRelations = await prisma.playerRegistration.findMany({
        include: {
          user: true,
          game: true
        }
      })

      const orphanedRegistrations = registrationsWithRelations.filter(reg => !reg.user || !reg.game)

      if (orphanedRegistrations.length > 0) {
        console.log(`⚠️  WARNING: ${orphanedRegistrations.length} orphaned registrations found`)
        warningIssues++
      } else {
        console.log('✅ Registration relationships intact')
      }
    } catch (error) {
      console.log(`❌ CRITICAL: Registration relationship check failed - ${error.message}`)
      criticalIssues++
    }

    // Check player stats relationships
    try {
      const statsWithRelations = await prisma.playerStats.findMany({
        include: {
          user: true,
          game: true
        }
      })

      const orphanedStats = statsWithRelations.filter(stat => !stat.user || !stat.game)

      if (orphanedStats.length > 0) {
        console.log(`⚠️  WARNING: ${orphanedStats.length} orphaned player stats found`)
        warningIssues++
      } else {
        console.log('✅ Player stats relationships intact')
      }
    } catch (error) {
      console.log(`❌ CRITICAL: Player stats relationship check failed - ${error.message}`)
      criticalIssues++
    }

    // Check weekly winner relationships
    try {
      const winnersWithRelations = await prisma.weeklyWinner.findMany({
        include: {
          user: true,
          game: true,
          tournament: true
        }
      })

      const orphanedWinners = winnersWithRelations.filter(winner => !winner.user || !winner.game || !winner.tournament)

      if (orphanedWinners.length > 0) {
        console.log(`⚠️  WARNING: ${orphanedWinners.length} orphaned weekly winners found`)
        warningIssues++
      } else {
        console.log('✅ Weekly winner relationships intact')
      }
    } catch (error) {
      console.log(`❌ CRITICAL: Weekly winner relationship check failed - ${error.message}`)
      criticalIssues++
    }

    // 4. Check data consistency
    console.log('\n4. 🔍 DATA CONSISTENCY CHECK')
    
    // Check user data completeness
    try {
      const usersWithMissingData = await prisma.user.findMany({
        where: {
          OR: [
            { username: '' },
            { firstName: '' },
            { lastName: '' },
            { phoneNumber: '' }
          ]
        }
      })
      
      if (usersWithMissingData.length > 0) {
        console.log(`⚠️  WARNING: ${usersWithMissingData.length} users with incomplete required data`)
        warningIssues++
      } else {
        console.log('✅ User data completeness check passed')
      }
    } catch (error) {
      console.log(`❌ CRITICAL: User data consistency check failed - ${error.message}`)
      criticalIssues++
    }

    // Check duplicate usernames
    try {
      const duplicateUsernames = await prisma.user.groupBy({
        by: ['username'],
        having: {
          username: {
            _count: {
              gt: 1
            }
          }
        }
      })
      
      if (duplicateUsernames.length > 0) {
        console.log(`❌ CRITICAL: ${duplicateUsernames.length} duplicate usernames found`)
        criticalIssues++
      } else {
        console.log('✅ Username uniqueness check passed')
      }
    } catch (error) {
      console.log(`❌ CRITICAL: Username uniqueness check failed - ${error.message}`)
      criticalIssues++
    }

    // 5. Check active sessions
    console.log('\n5. 🔐 SESSION INTEGRITY CHECK')

    try {
      const totalSessions = await prisma.userSession.count()
      const expiredSessions = await prisma.userSession.count({
        where: {
          expiresAt: {
            lt: new Date()
          }
        }
      })
      
      console.log(`✅ Total sessions: ${totalSessions}`)
      console.log(`✅ Expired sessions: ${expiredSessions}`)
      
      if (expiredSessions > 100) {
        console.log('⚠️  WARNING: Many expired sessions - consider cleanup')
        warningIssues++
      }
    } catch (error) {
      console.log(`❌ CRITICAL: Session check failed - ${error.message}`)
      criticalIssues++
    }

    // 6. Check recent activity
    console.log('\n6. 📈 RECENT ACTIVITY CHECK')
    
    try {
      const recentRegistrations = await prisma.playerRegistration.count({
        where: {
          registrationDate: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
          }
        }
      })
      
      const recentUsers = await prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
          }
        }
      })
      
      console.log(`✅ Recent registrations (7 days): ${recentRegistrations}`)
      console.log(`✅ Recent users (7 days): ${recentUsers}`)
    } catch (error) {
      console.log(`❌ CRITICAL: Recent activity check failed - ${error.message}`)
      criticalIssues++
    }

    // Summary
    console.log('\n📊 DATA INTEGRITY SUMMARY')
    console.log('=========================')
    console.log(`🔴 Critical Issues: ${criticalIssues}`)
    console.log(`🟡 Warning Issues: ${warningIssues}`)
    console.log(`📊 Total Issues: ${criticalIssues + warningIssues}`)

    if (criticalIssues === 0 && warningIssues === 0) {
      console.log('\n🎉 EXCELLENT! All data integrity checks passed.')
    } else if (criticalIssues === 0) {
      console.log('\n✅ GOOD! No critical data issues, but some warnings to review.')
    } else {
      console.log('\n🚨 ATTENTION REQUIRED! Critical data integrity issues found.')
    }

  } catch (error) {
    console.log(`❌ CRITICAL: Data integrity check failed - ${error.message}`)
    criticalIssues++
  } finally {
    await prisma.$disconnect()
  }

  process.exit(criticalIssues > 0 ? 1 : 0)
}

checkDataIntegrity().catch(console.error)
