import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const PATCH = withAdminAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params
    const tournamentId = parseInt(id)
    const body = await request.json()

    // Convert isCompleted to status if needed
    if ('isCompleted' in body) {
      body.status = body.isCompleted ? 'COMPLETED' : 'UPCOMING'
      delete body.isCompleted
    }

    const updatedTournament = await prisma.weeklyTournament.update({
      where: { id: tournamentId },
      data: body,
      include: {
        game: true,
        winner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true
          }
        }
      }
    })

    return NextResponse.json(updatedTournament)
  } catch (error) {
    logger.error('Error updating tournament:', error)
    return NextResponse.json(
      { error: 'Failed to update tournament' },
      { status: 500 }
    )
  }
})
