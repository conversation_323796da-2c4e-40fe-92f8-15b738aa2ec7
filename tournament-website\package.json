{"name": "tournament-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "pm2:start-prod": "pm2 start ecosystem.config.js --env production", "pm2:start-dev": "pm2 start ecosystem.config.js --env development", "pm2:stop": "pm2 stop mzuni-tournaments", "pm2:restart": "pm2 restart mzuni-tournaments", "pm2:reload": "pm2 reload mzuni-tournaments", "pm2:delete": "pm2 delete mzuni-tournaments", "pm2:status": "pm2 status mzuni-tournaments", "pm2:logs": "pm2 logs mzuni-tournaments", "pm2:monitor": "pm2 monit", "deploy:build": "npm ci --production && npm run build", "deploy:prod": "npm run deploy:build && npm run pm2:start-prod", "deploy:staging": "npm run deploy:build && pm2 start ecosystem.config.js --env staging", "health": "curl -f http://localhost:3002/api/health || echo 'Health check failed'", "setup:admin": "node create-admin.js", "setup:db": "npx prisma generate && npx prisma db push", "setup:production": "npm run setup:db && npm run setup:admin && npm run deploy:prod", "analyze:css": "node scripts/css-optimizer.js", "optimize:css": "npm run build && npm run analyze:css", "clean:build": "rimraf .next", "prebuild": "npm run clean:build", "render:build": "npm ci && npx prisma generate && npm run build", "render:start": "npx prisma db push && npm run setup:admin && npm start", "postinstall": "npx prisma generate", "db:migrate": "npx prisma migrate deploy", "db:reset": "npx prisma migrate reset --force", "db:seed": "npx prisma db seed", "build:full": "npm run clean:build && npm ci && npm run setup:db && npm run build", "verify:build": "node -e \"console.log('Build verification:', require('fs').existsSync('.next') ? '✅ Success' : '❌ Failed')\"", "security:audit": "npm audit --audit-level moderate", "deps:update": "npm update && npm audit fix"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.10.1", "@types/bcryptjs": "^2.4.6", "@types/pg": "^8.15.4", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "dotenv": "^17.0.0", "next": "15.3.4", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "pg": "^8.16.3", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@fullhuman/postcss-purgecss": "^7.0.2", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cssnano": "^7.0.7", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.4", "rimraf": "^6.0.1", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}