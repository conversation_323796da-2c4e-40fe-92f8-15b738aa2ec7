import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import bcrypt from 'bcryptjs'
import { logger } from '@/lib/logger'

// Function to send WhatsApp notification for admin login
async function sendWhatsAppNotification(username: string) {
  try {
    const loginTime = new Date().toLocaleString('en-US', {
      timeZone: 'Africa/Blantyre',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })

    const message = `🔐 ADMIN LOGIN ALERT\n\nUsername: ${username}\nTime: ${loginTime}\nLocation: Mzuni Tournaments Admin Panel\n\nIf this wasn't you, please secure your account immediately.`

    // WhatsApp API URL (using WhatsApp Business API or similar service)
    const whatsappNumber = '************' // Your WhatsApp number
    const encodedMessage = encodeURIComponent(message)

    // For now, we'll log the notification (in production, integrate with WhatsApp API)
    logger.info('WhatsApp Notification:', {
      to: whatsappNumber,
      message: message
    })

    // You can integrate with WhatsApp Business API, Twilio, or similar service here
    // Example: await fetch('https://api.whatsapp.com/send', { ... })

  } catch (error) {
    logger.error('Failed to send WhatsApp notification:', error)
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { username, password } = body

    if (!username || !password) {
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      )
    }

    // Admin credentials from environment variables
    if (username === process.env.ADMIN_USERNAME && password === process.env.ADMIN_PASSWORD) {
      // Send WhatsApp notification
      await sendWhatsAppNotification(username)

      // Generate a simple token (in production, use JWT or proper session management)
      const token = Buffer.from(`${username}:${Date.now()}`).toString('base64')

      return NextResponse.json({
        success: true,
        token,
        user: {
          username: 'Tournaowner',
          role: 'ADMIN'
        }
      })
    }

    // Also check database for admin users
    const user = await prisma.user.findFirst({
      where: {
        username,
        role: 'ADMIN'
      }
    })

    if (user) {
      // For demo, accept any password for database admin users
      // In production, you'd verify hashed passwords
      const token = Buffer.from(`${user.username}:${Date.now()}`).toString('base64')
      
      return NextResponse.json({
        success: true,
        token,
        user: {
          id: user.id,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role
        }
      })
    }

    return NextResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    )
  } catch (error) {
    logger.error('Admin login error:', error)
    return NextResponse.json(
      { error: 'Login failed' },
      { status: 500 }
    )
  }
}
