# Server Management Guide

## Option 1: Keep Server Running with PM2 (Recommended)

PM2 is a process manager that keeps your server running even after you close the terminal.

### Install PM2 globally:
```bash
npm install -g pm2
```

### Start the server with PM2:
```bash
pm2 start ecosystem.config.js
```

### Useful PM2 commands:
```bash
pm2 list                    # Show running processes
pm2 logs mzuni-tournaments  # View logs
pm2 restart mzuni-tournaments # Restart the app
pm2 stop mzuni-tournaments  # Stop the app
pm2 delete mzuni-tournaments # Remove from PM2
pm2 startup                 # Enable PM2 to start on system boot
pm2 save                    # Save current PM2 processes
```

## Option 2: Simple Development Server
```bash
npm run dev
```
This runs on http://localhost:3002 but stops when you close the terminal.

## Option 3: Production Build
```bash
npm run build
npm start
```

## Current Status
- Server runs on port 3002 (since 3000 is occupied)
- Database: PostgreSQL on localhost:5432/Gaming
- Admin credentials: admin/admin123
- WhatsApp number: +265983132770

## Features Working:
✅ Registration form with conditional PES username
✅ Single button that transforms to WhatsApp after registration
✅ Admin management system (stats, schedules, announcements)
✅ Public announcements display
✅ All navigation updated with Admin links
✅ Malawi phone number validation (+265)
