import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'
import { TournamentNotifications } from '@/lib/notifications'

export const POST = withAdminAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const { gameId, winnerId, position, weekNumber, year, tournamentDate } = body

    // Validate required fields
    if (!gameId || !winnerId || !position || !weekNumber || !year) {
      return NextResponse.json(
        { error: 'Game ID, winner ID, position, week number, and year are required' },
        { status: 400 }
      )
    }

    // Validate data types and ranges
    const parsedGameId = parseInt(gameId)
    const parsedWinnerId = parseInt(winnerId)
    const parsedPosition = parseInt(position)
    const parsedWeekNumber = parseInt(weekNumber)
    const parsedYear = parseInt(year)

    if (isNaN(parsedGameId) || isNaN(parsedWinnerId) || isNaN(parsedPosition) ||
        isNaN(parsedWeekNumber) || isNaN(parsedYear)) {
      return NextResponse.json(
        { error: 'Invalid data format. All IDs and numbers must be valid integers.' },
        { status: 400 }
      )
    }

    if (parsedPosition < 1 || parsedPosition > 10) {
      return NextResponse.json(
        { error: 'Position must be between 1 and 10' },
        { status: 400 }
      )
    }

    if (parsedWeekNumber < 1 || parsedWeekNumber > 52) {
      return NextResponse.json(
        { error: 'Week number must be between 1 and 52' },
        { status: 400 }
      )
    }

    // Verify game and user exist
    const [game, user] = await Promise.all([
      prisma.game.findUnique({ where: { id: parsedGameId } }),
      prisma.user.findUnique({ where: { id: parsedWinnerId } })
    ])

    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      )
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if tournament already exists for this week/game/year
    const existingTournament = await prisma.weeklyTournament.findFirst({
      where: {
        gameId: parsedGameId,
        weekNumber: parsedWeekNumber,
        year: parsedYear
      }
    })

    let tournament

    if (existingTournament) {
      // Update existing tournament
      tournament = await prisma.weeklyTournament.update({
        where: { id: existingTournament.id },
        data: {
          winnerId: parsedPosition === 1 ? parsedWinnerId : existingTournament.winnerId,
          status: 'COMPLETED',
          totalParticipants: Math.max(existingTournament.totalParticipants || 0, parsedPosition)
        },
        include: {
          game: {
            select: {
              id: true,
              name: true
            }
          },
          winner: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true
            }
          }
        }
      })
    } else {
      // Create new tournament
      tournament = await prisma.weeklyTournament.create({
        data: {
          gameId: parsedGameId,
          weekNumber: parsedWeekNumber,
          year: parsedYear,
          tournamentDate: new Date(tournamentDate || new Date()),
          winnerId: parsedPosition === 1 ? parsedWinnerId : null,
          status: 'COMPLETED',
          totalParticipants: parsedPosition
        },
        include: {
          game: {
            select: {
              id: true,
              name: true
            }
          },
          winner: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true
            }
          }
        }
      })
    }

    // If position is 1 (winner), create/update weekly winner record
    if (parsedPosition === 1) {
      await prisma.weeklyWinner.upsert({
        where: {
          gameId_weekNumber_year: {
            gameId: parsedGameId,
            weekNumber: parsedWeekNumber,
            year: parsedYear
          }
        },
        update: {
          tournamentId: tournament.id
        },
        create: {
          userId: parsedWinnerId,
          gameId: parsedGameId,
          weekNumber: parsedWeekNumber,
          year: parsedYear,
          tournamentId: tournament.id
        }
      })
    }

    // Update player stats based on position
    const currentStats = await prisma.playerStats.findUnique({
      where: {
        userId_gameId: {
          userId: parsedWinnerId,
          gameId: parsedGameId
        }
      }
    })

    if (currentStats) {
      // Update existing stats
      const updateData: any = {
        tournamentsParticipated: { increment: 1 },
        lastUpdated: new Date()
      }

      if (parsedPosition === 1) {
        // Winner gets tournament win and total win
        updateData.tournamentsWon = { increment: 1 }
        updateData.totalWins = { increment: 1 }
      }
      // Note: For tournaments, positions 2+ are not losses, just different placements
      // Only increment losses for actual match losses, not tournament positions

      await prisma.playerStats.update({
        where: {
          userId_gameId: {
            userId: parsedWinnerId,
            gameId: parsedGameId
          }
        },
        data: updateData
      })

      // Recalculate win percentage
      const updatedStats = await prisma.playerStats.findUnique({
        where: {
          userId_gameId: {
            userId: parsedWinnerId,
            gameId: parsedGameId
          }
        }
      })

      if (updatedStats) {
        const totalGames = updatedStats.totalWins + updatedStats.totalLosses
        const winPercentage = totalGames > 0 ? (updatedStats.totalWins / totalGames) * 100 : 0

        await prisma.playerStats.update({
          where: { id: updatedStats.id },
          data: { winPercentage }
        })
      }
    } else {
      // Create new stats
      const newStatsData: any = {
        userId: parsedWinnerId,
        gameId: parsedGameId,
        tournamentsParticipated: 1,
        tournamentsWon: parsedPosition === 1 ? 1 : 0,
        totalWins: parsedPosition === 1 ? 1 : 0,
        totalLosses: 0, // Don't count tournament positions as losses
        winPercentage: parsedPosition === 1 ? 100 : 0
      }

      await prisma.playerStats.create({
        data: newStatsData
      })
    }

    // Send real-time notification for leaderboard update
    if (parsedPosition === 1) {
      TournamentNotifications.leaderboardUpdated(
        `${user.firstName} ${user.lastName}`,
        game.name
      )
    }

    return NextResponse.json({
      success: true,
      tournament,
      message: `Successfully updated leaderboard! ${parsedPosition === 1 ? 'Winner' : `${parsedPosition}${parsedPosition === 2 ? 'nd' : parsedPosition === 3 ? 'rd' : 'th'} place`} recorded for ${user.firstName} ${user.lastName} in ${game.name}.`
    })
  } catch (error) {
    logger.error('Error updating leaderboard:', error)
    return NextResponse.json(
      { error: 'Failed to update leaderboard' },
      { status: 500 }
    )
  }
})
