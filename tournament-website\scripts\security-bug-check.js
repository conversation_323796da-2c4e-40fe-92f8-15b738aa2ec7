require('dotenv').config()
const { PrismaClient } = require('@prisma/client')
const fs = require('fs')
const path = require('path')

const prisma = new PrismaClient()

async function runSecurityAndBugCheck() {
  console.log('🛡️  SECURITY & BUG CHECK')
  console.log('=' .repeat(50))

  try {
    // 1. Check for sensitive data exposure
    console.log('\n🔍 1. SENSITIVE DATA EXPOSURE CHECK')
    
    // Check .env file exists and has proper permissions
    const envPath = path.join(__dirname, '..', '.env')
    if (fs.existsSync(envPath)) {
      console.log('✅ .env file exists')
      const envContent = fs.readFileSync(envPath, 'utf8')
      if (envContent.includes('DATABASE_URL')) {
        console.log('✅ Database URL configured')
      }
      if (envContent.includes('NEXTAUTH_SECRET')) {
        console.log('✅ NextAuth secret configured')
      }
    } else {
      console.log('❌ .env file missing')
    }

    // 2. Database security checks
    console.log('\n🔒 2. DATABASE SECURITY CHECK')
    
    // Check for admin users
    const adminUsers = await prisma.user.findMany({
      where: { role: 'ADMIN' }
    })
    console.log(`${adminUsers.length > 0 ? '✅' : '⚠️'} Admin users: ${adminUsers.length}`)
    
    // Check for users with weak data
    const users = await prisma.user.findMany()
    let weakUsers = 0
    for (const user of users) {
      if (!user.phoneNumber || user.phoneNumber.length < 10) {
        weakUsers++
      }
    }
    console.log(`${weakUsers === 0 ? '✅' : '⚠️'} Users with weak phone validation: ${weakUsers}`)

    // 3. Input validation checks
    console.log('\n📝 3. INPUT VALIDATION CHECK')
    
    // Check for potential SQL injection patterns in data
    let suspiciousRecords = 0
    const suspiciousPatterns = ["'", '"', ';', '--', '/*', '*/', 'DROP', 'DELETE', 'UPDATE', 'INSERT']
    
    for (const user of users) {
      const textFields = [user.username, user.firstName, user.lastName, user.phoneNumber]
      for (const field of textFields) {
        if (field) {
          for (const pattern of suspiciousPatterns) {
            if (field.toUpperCase().includes(pattern.toUpperCase())) {
              suspiciousRecords++
              break
            }
          }
        }
      }
    }
    console.log(`${suspiciousRecords === 0 ? '✅' : '❌'} Suspicious data patterns: ${suspiciousRecords}`)

    // 4. Business logic validation
    console.log('\n🎯 4. BUSINESS LOGIC VALIDATION')
    
    // Check tournament data consistency
    const tournaments = await prisma.weeklyTournament.findMany({
      include: { winner: true, weeklyWinners: true }
    })
    
    let inconsistentTournaments = 0
    for (const tournament of tournaments) {
      // Check if tournament has winner but no weekly winner record
      if (tournament.winnerId && tournament.weeklyWinners.length === 0) {
        inconsistentTournaments++
      }
      
      // Check if tournament is completed but has no winner
      if (tournament.status === 'COMPLETED' && !tournament.winnerId) {
        inconsistentTournaments++
      }
    }
    console.log(`${inconsistentTournaments === 0 ? '✅' : '❌'} Tournament logic inconsistencies: ${inconsistentTournaments}`)

    // Check stats calculations
    const stats = await prisma.playerStats.findMany()
    let wrongCalculations = 0
    
    for (const stat of stats) {
      const totalGames = stat.totalWins + stat.totalLosses
      const expectedPercentage = totalGames > 0 ? (stat.totalWins / totalGames) * 100 : 0
      const actualPercentage = parseFloat(stat.winPercentage.toString())
      
      if (Math.abs(expectedPercentage - actualPercentage) > 0.1) {
        wrongCalculations++
      }
      
      // Check for impossible values
      if (stat.totalWins < 0 || stat.totalLosses < 0 || stat.tournamentsWon < 0) {
        wrongCalculations++
      }
      
      // Check if tournaments won exceeds total wins
      if (stat.tournamentsWon > stat.totalWins) {
        wrongCalculations++
      }
    }
    console.log(`${wrongCalculations === 0 ? '✅' : '❌'} Stats calculation errors: ${wrongCalculations}`)

    // 5. Performance and resource checks
    console.log('\n⚡ 5. PERFORMANCE & RESOURCE CHECK')
    
    // Check for large data sets that might cause performance issues
    const largeDataSets = []
    if (users.length > 10000) largeDataSets.push(`Users: ${users.length}`)
    if (tournaments.length > 1000) largeDataSets.push(`Tournaments: ${tournaments.length}`)
    if (stats.length > 50000) largeDataSets.push(`Stats: ${stats.length}`)
    
    console.log(`${largeDataSets.length === 0 ? '✅' : '⚠️'} Large datasets: ${largeDataSets.length === 0 ? 'None' : largeDataSets.join(', ')}`)

    // 6. Zero-day vulnerability patterns
    console.log('\n🚨 6. ZERO-DAY VULNERABILITY PATTERNS')
    
    // Check for common vulnerability patterns
    let vulnerabilityCount = 0
    
    // Check for users with admin privileges but no proper validation
    const adminWithoutValidation = adminUsers.filter(admin => !admin.phoneNumber)
    if (adminWithoutValidation.length > 0) {
      vulnerabilityCount++
      console.log(`⚠️  Admin users without phone validation: ${adminWithoutValidation.length}`)
    }
    
    // Check for tournaments with future dates that are too far
    const farFutureTournaments = tournaments.filter(t => {
      const tournamentDate = new Date(t.tournamentDate)
      const oneYearFromNow = new Date()
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1)
      return tournamentDate > oneYearFromNow
    })
    
    if (farFutureTournaments.length > 0) {
      vulnerabilityCount++
      console.log(`⚠️  Tournaments scheduled too far in future: ${farFutureTournaments.length}`)
    }
    
    console.log(`${vulnerabilityCount === 0 ? '✅' : '❌'} Zero-day vulnerability patterns: ${vulnerabilityCount}`)

    // 7. File system security
    console.log('\n📁 7. FILE SYSTEM SECURITY')
    
    // Check for sensitive files in public directory
    const publicDir = path.join(__dirname, '..', 'public')
    const sensitiveFiles = ['.env', 'config.json', 'database.db', '.git']
    let exposedFiles = 0
    
    if (fs.existsSync(publicDir)) {
      const publicFiles = fs.readdirSync(publicDir)
      for (const file of publicFiles) {
        if (sensitiveFiles.some(sensitive => file.includes(sensitive))) {
          exposedFiles++
        }
      }
    }
    console.log(`${exposedFiles === 0 ? '✅' : '❌'} Exposed sensitive files: ${exposedFiles}`)

    // 8. API endpoint security
    console.log('\n🌐 8. API ENDPOINT SECURITY')
    
    // Check for API files that might have security issues
    const apiDir = path.join(__dirname, '..', 'src', 'app', 'api')
    let apiSecurityIssues = 0
    
    function checkAPIFiles(dir) {
      if (!fs.existsSync(dir)) return
      
      const files = fs.readdirSync(dir)
      for (const file of files) {
        const filePath = path.join(dir, file)
        const stat = fs.statSync(filePath)
        
        if (stat.isDirectory()) {
          checkAPIFiles(filePath)
        } else if (file.endsWith('.ts') || file.endsWith('.js')) {
          const content = fs.readFileSync(filePath, 'utf8')
          
          // Check for potential security issues
          if (content.includes('eval(') || content.includes('Function(')) {
            apiSecurityIssues++
          }
          
          // Check for hardcoded secrets
          if (content.includes('password') && content.includes('=') && content.includes('"')) {
            // This is a basic check - might have false positives
          }
        }
      }
    }
    
    checkAPIFiles(apiDir)
    console.log(`${apiSecurityIssues === 0 ? '✅' : '❌'} API security issues: ${apiSecurityIssues}`)

    // Final summary
    console.log('\n🎯 FINAL SECURITY & BUG SUMMARY')
    const totalIssues = inconsistentTournaments + wrongCalculations + vulnerabilityCount + exposedFiles + apiSecurityIssues + suspiciousRecords
    
    console.log(`   Total Critical Issues: ${totalIssues}`)
    console.log(`   Security Status: ${totalIssues === 0 ? '🟢 SECURE' : totalIssues < 3 ? '🟡 MINOR ISSUES' : '🔴 NEEDS ATTENTION'}`)
    console.log(`   Database Records: ${users.length} users, ${tournaments.length} tournaments, ${stats.length} stats`)
    console.log(`   Admin Users: ${adminUsers.length}`)
    console.log(`   Data Integrity: ${inconsistentTournaments === 0 && wrongCalculations === 0 ? '✅ GOOD' : '⚠️ ISSUES FOUND'}`)

  } catch (error) {
    console.error('❌ CRITICAL ERROR during security check:', error)
  } finally {
    await prisma.$disconnect()
    console.log('\n🔌 Security check completed')
    console.log('=' .repeat(50))
  }
}

runSecurityAndBugCheck().catch(console.error)
