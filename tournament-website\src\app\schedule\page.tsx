'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { logger } from '@/lib/logger'

interface Schedule {
  maxParticipants: string
  currentParticipants: number
  registrationDeadline: any
  id: number
  game: {
    id: number
    name: string
  }
  scheduledDate: string
  scheduledTime: string
  description: string | null
  status: string
  weekNumber?: number
  year?: number
}

export default function SchedulePage() {
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGame, setSelectedGame] = useState<string>('')
  const [games, setGames] = useState<any[]>([])

  useEffect(() => {
    fetchGames()
    fetchSchedules()
  }, [])

  const fetchGames = async () => {
    try {
      const response = await fetch('/api/games')
      if (response.ok) {
        const data = await response.json()
        setGames(data)
      }
    } catch (error) {
      logger.error('Error fetching games:', error)
    }
  }

  const fetchSchedules = async () => {
    try {
      setLoading(true)
      const url = selectedGame ? `/api/schedules?gameId=${selectedGame}` : '/api/schedules'
      const response = await fetch(url)
      
      if (response.ok) {
        const data = await response.json()
        // Filter to show only scheduled tournaments
        const scheduledTournaments = data.filter((schedule: Schedule) => schedule.status === 'SCHEDULED')
        setSchedules(scheduledTournaments)
      } else {
        logger.error('Failed to fetch schedules')
      }
    } catch (error) {
      logger.error('Error fetching schedules:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchSchedules()
  }, [selectedGame])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (timeString: string) => {
    // Handle both time string formats (HH:MM or full DateTime)
    let timeToFormat: Date

    if (timeString.includes('T') || timeString.includes('Z')) {
      // It's a full DateTime string
      timeToFormat = new Date(timeString)
    } else {
      // It's a time string like "14:30"
      const [hours, minutes] = timeString.split(':')
      timeToFormat = new Date()
      timeToFormat.setHours(parseInt(hours), parseInt(minutes))
    }

    return timeToFormat.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const isUpcoming = (dateString: string, timeString: string) => {
    try {
      // Simple approach: just check if the date is today or in the future
      const scheduleDate = new Date(dateString)
      const today = new Date()

      // Set time to start of day for comparison
      const scheduleDateOnly = new Date(scheduleDate.getFullYear(), scheduleDate.getMonth(), scheduleDate.getDate())
      const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate())

      // If it's today or in the future, consider it upcoming
      return scheduleDateOnly >= todayOnly
    } catch (error) {
      logger.error('Error parsing schedule date:', error)
      // If there's an error, show the schedule anyway (better to show than hide)
      return true
    }
  }

  const upcomingSchedules = schedules.filter(schedule => 
    isUpcoming(schedule.scheduledDate, schedule.scheduledTime)
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-3xl font-bold text-blue-600">
                eSports RXP
              </Link>
              <span className="ml-4 px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                Tournament Schedule
              </span>
            </div>
            <nav className="flex items-center space-x-6">
              <Link href="/" className="text-gray-500 hover:text-gray-900 flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span>Home</span>
              </Link>
              <Link href="/leaderboard" className="text-gray-500 hover:text-gray-900 flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                <span>Leaderboard</span>
              </Link>

              <Link href="/schedule" className="text-blue-600 font-medium flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>Schedule</span>
              </Link>
              <Link href="/how-it-works" className="text-gray-500 hover:text-gray-900 flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>How It Works</span>
              </Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Title */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Tournament Schedule</h1>
          <p className="text-xl text-gray-600">Stay updated with upcoming tournament dates and times</p>
        </div>

        {/* Game Filter */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="mb-4 sm:mb-0">
              <label htmlFor="game-filter" className="block text-sm font-medium text-gray-700 mb-2">
                Filter by Game
              </label>
              <select
                id="game-filter"
                value={selectedGame}
                onChange={(e) => setSelectedGame(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Games</option>
                {games.map((game) => (
                  <option key={game.id} value={game.id}>
                    {game.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="text-sm text-gray-500">
              {upcomingSchedules.length} upcoming tournament{upcomingSchedules.length !== 1 ? 's' : ''}
            </div>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Loading schedules...</p>
          </div>
        ) : upcomingSchedules.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Upcoming Tournaments</h3>
            <p className="text-gray-600 mb-4">
              {selectedGame ? 'No upcoming tournaments for the selected game.' : 'No tournaments are currently scheduled.'}
            </p>
            <p className="text-sm text-gray-500">Check back later for new tournament announcements!</p>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {upcomingSchedules.map((schedule) => (
              <div key={schedule.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                        {schedule.game.name}
                      </span>
                      {schedule.weekNumber && (
                        <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                          Week {schedule.weekNumber}
                        </span>
                      )}
                    </div>
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                      {schedule.status}
                    </span>
                  </div>
                  
                  <div className="mb-4">
                    <div className="flex items-center text-gray-600 mb-2">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <span className="text-sm font-medium">{formatDate(schedule.scheduledDate)}</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-sm font-medium">{formatTime(schedule.scheduledTime)}</span>
                    </div>
                  </div>

                  {schedule.description && (
                    <div className="mb-4">
                      <p className="text-gray-700 text-sm">{schedule.description}</p>
                    </div>
                  )}

                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-xs text-gray-500">
                        <span>Max: {schedule.maxParticipants || 'Unlimited'}</span>
                        <span className="mx-2">•</span>
                        <span>Registered: {schedule.currentParticipants || 0}</span>
                      </div>
                      {schedule.registrationDeadline && (
                        <div className="text-xs text-orange-600">
                          Deadline: {new Date(schedule.registrationDeadline).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                    <Link
                      href={`/tournament-register?scheduleId=${schedule.id}&game=${schedule.game.name}&week=${schedule.weekNumber}`}
                      className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-center block text-sm font-medium"
                    >
                      Register for Week {schedule.weekNumber} - {schedule.game.name}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Contact Information */}
        <div className="mt-12 bg-blue-50 rounded-lg p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Need Help or Have Questions?</h3>
            <p className="text-blue-700 mb-4">Contact us for tournament information and registration assistance</p>
            <a
              href="https://wa.me/265983132770"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488z"/>
              </svg>
              WhatsApp: +265 98 313 2770
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
