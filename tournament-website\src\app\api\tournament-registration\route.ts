import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'
import { TournamentNotifications } from '@/lib/notifications'
import { validateSession } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    // Validate session first
    const sessionCookie = request.cookies.get('session')
    if (!sessionCookie) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    const session = await validateSession(sessionCookie.value)
    if (!session) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 })
    }

    const body = await request.json()
    const { tournamentScheduleId, gameUsername } = body
    const userId = session.userId // Get userId from session

    // Debug logging
    logger.info('Tournament registration request:', { tournamentScheduleId, userId, gameUsername })

    if (!tournamentScheduleId) {
      logger.error('Missing required fields:', { tournamentScheduleId })
      return NextResponse.json(
        { error: 'Tournament schedule ID is required' },
        { status: 400 }
      )
    }

    // Get tournament schedule details
    const tournament = await prisma.tournamentSchedule.findUnique({
      where: { id: parseInt(tournamentScheduleId) },
      include: {
        game: { select: { id: true, name: true } }
      }
    })

    if (!tournament) {
      return NextResponse.json(
        { error: 'Tournament not found' },
        { status: 404 }
      )
    }

    // Check if tournament is available for registration
    if (tournament.status !== 'SCHEDULED') {
      return NextResponse.json(
        { error: 'Tournament is not available for registration' },
        { status: 400 }
      )
    }

    // Check registration deadline
    if (tournament.registrationDeadline && new Date(tournament.registrationDeadline) < new Date()) {
      return NextResponse.json(
        { error: 'Registration deadline has passed' },
        { status: 400 }
      )
    }

    // Get game-specific max participants per phase
    const getGameMaxParticipants = (gameName: string) => {
      switch (gameName?.toLowerCase()) {
        case 'pes': return 32
        case 'pubg': return 100
        case 'call of duty': return 100
        default: return 50
      }
    }

    const gameMaxPerPhase = getGameMaxParticipants(tournament.game.name)

    // Calculate current phase and participants in current phase
    const currentPhase = Math.floor(tournament.currentParticipants / gameMaxPerPhase) + 1
    const participantsInCurrentPhase = tournament.currentParticipants % gameMaxPerPhase

    // Check if current phase is full (this will automatically create next phase)
    // We don't block registration, just inform about phase placement

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: parseInt(userId) }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user already registered for this specific tournament
    const existingTournamentRegistration = await prisma.playerRegistration.findFirst({
      where: {
        userId: parseInt(userId),
        tournamentScheduleId: parseInt(tournamentScheduleId),
        isActive: true
      }
    })

    if (existingTournamentRegistration) {
      return NextResponse.json(
        { error: 'You are already registered for this tournament' },
        { status: 400 }
      )
    }

    // Check if user has a general registration for this game
    const existingGameRegistration = await prisma.playerRegistration.findFirst({
      where: {
        userId: parseInt(userId),
        gameId: tournament.gameId,
        tournamentScheduleId: null, // General registration
        isActive: true
      }
    })

    if (existingGameRegistration) {
      // Update existing general registration to link to this tournament
      const updatedRegistration = await prisma.playerRegistration.update({
        where: { id: existingGameRegistration.id },
        data: {
          tournamentScheduleId: parseInt(tournamentScheduleId),
          registrationDate: new Date(), // Update registration date
          gameUsername: gameUsername || existingGameRegistration.gameUsername // Update game username if provided
        },
        include: {
          user: { select: { id: true, username: true, firstName: true, lastName: true } },
          game: { select: { id: true, name: true } },
          tournamentSchedule: { select: { tournamentId: true } }
        }
      })

      // Update participant count
      await prisma.tournamentSchedule.update({
        where: { id: parseInt(tournamentScheduleId) },
        data: { currentParticipants: { increment: 1 } }
      })

      // Calculate phase info for response
      const newPhase = Math.floor((tournament.currentParticipants + 1) / gameMaxPerPhase) + 1
      const newParticipantsInPhase = ((tournament.currentParticipants + 1) % gameMaxPerPhase) || gameMaxPerPhase

      // Send notification
      TournamentNotifications.tournamentRegistration(
        `${user.firstName} ${user.lastName}`,
        tournament.game.name,
        tournament.tournamentId
      )

      return NextResponse.json({
        success: true,
        registration: updatedRegistration,
        phase: newPhase,
        participantsInPhase: newParticipantsInPhase,
        maxPerPhase: gameMaxPerPhase,
        message: `Successfully registered for ${tournament.game.name} Tournament ${tournament.tournamentId} - Phase ${newPhase}! You are participant ${newParticipantsInPhase}/${gameMaxPerPhase} in this phase.`
      })
    }

    // Create new registration for this tournament
    const registration = await prisma.playerRegistration.create({
      data: {
        userId: parseInt(userId),
        gameId: tournament.gameId,
        tournamentScheduleId: parseInt(tournamentScheduleId),
        gameUsername: gameUsername || user.username // Use provided gameUsername or default to username
      },
      include: {
        user: { select: { id: true, username: true, firstName: true, lastName: true } },
        game: { select: { id: true, name: true } },
        tournamentSchedule: { select: { tournamentId: true } }
      }
    })

    // Update participant count
    await prisma.tournamentSchedule.update({
      where: { id: parseInt(tournamentScheduleId) },
      data: { currentParticipants: { increment: 1 } }
    })

    // Create or update player stats
    await prisma.playerStats.upsert({
      where: {
        userId_gameId: {
          userId: parseInt(userId),
          gameId: tournament.gameId
        }
      },
      update: {},
      create: {
        userId: parseInt(userId),
        gameId: tournament.gameId
      }
    })

    // Calculate phase info for response
    const newPhase = Math.floor((tournament.currentParticipants + 1) / gameMaxPerPhase) + 1
    const newParticipantsInPhase = ((tournament.currentParticipants + 1) % gameMaxPerPhase) || gameMaxPerPhase

    // Send notification
    TournamentNotifications.tournamentRegistration(
      `${user.firstName} ${user.lastName}`,
      tournament.game.name,
      tournament.tournamentId
    )

    return NextResponse.json({
      success: true,
      registration,
      phase: newPhase,
      participantsInPhase: newParticipantsInPhase,
      maxPerPhase: gameMaxPerPhase,
      message: `Successfully registered for ${tournament.game.name} Tournament ${tournament.tournamentId} - Phase ${newPhase}! You are participant ${newParticipantsInPhase}/${gameMaxPerPhase} in this phase.`
    })

  } catch (error) {
    logger.error('Error in tournament registration:', error)
    return NextResponse.json(
      { error: 'Failed to register for tournament' },
      { status: 500 }
    )
  }
}
