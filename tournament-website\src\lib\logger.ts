/**
 * Production-ready logging utility
 * Replaces console.log statements with proper logging
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error'

interface LogEntry {
  level: LogLevel
  message: string
  timestamp: string
  context?: any
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development'

  private formatMessage(level: LogLevel, message: string, context?: any): LogEntry {
    return {
      level,
      message,
      timestamp: new Date().toISOString(),
      context
    }
  }

  private log(level: LogLevel, message: string, context?: any) {
    const logEntry = this.formatMessage(level, message, context)
    
    if (this.isDevelopment) {
      // In development, use console for immediate feedback
      const logMethod = console[level] || console.log
      if (context) {
        logMethod(`[${level.toUpperCase()}] ${message}`, context)
      } else {
        logMethod(`[${level.toUpperCase()}] ${message}`)
      }
    } else {
      // In production, you could send to external logging service
      // For now, we'll use console but with structured format
      console.log(JSON.stringify(logEntry))
    }
  }

  debug(message: string, context?: any) {
    if (this.isDevelopment) {
      this.log('debug', message, context)
    }
  }

  info(message: string, context?: any) {
    this.log('info', message, context)
  }

  warn(message: string, context?: any) {
    this.log('warn', message, context)
  }

  error(message: string, context?: any) {
    this.log('error', message, context)
  }

  // Convenience method for API errors
  apiError(endpoint: string, error: any, context?: any) {
    this.error(`API Error in ${endpoint}`, {
      error: error.message || error,
      stack: error.stack,
      ...context
    })
  }

  // Convenience method for authentication events
  authEvent(event: string, userId?: number, context?: any) {
    this.info(`Auth: ${event}`, {
      userId,
      ...context
    })
  }

  // Convenience method for database operations
  dbOperation(operation: string, table: string, context?: any) {
    this.debug(`DB: ${operation} on ${table}`, context)
  }
}

// Export singleton instance
export const logger = new Logger()

// Export for backward compatibility during migration
export default logger
