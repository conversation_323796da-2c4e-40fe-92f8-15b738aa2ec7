import { PrismaClient } from '@prisma/client'
import * as dotenv from 'dotenv'
import { getCurrentTournamentWeek } from '../src/lib/tournament-utils'
import { hashPassword } from '../src/lib/auth'

// Load environment variables
dotenv.config({ path: '.env.local' })

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create games if they don't exist
  let pubg = await prisma.game.findFirst({ where: { name: 'PUBG' } })
  if (!pubg) {
    pubg = await prisma.game.create({
      data: {
        name: 'PUBG',
        description: 'PlayerUnknown\'s Battlegrounds'
      }
    })
  }

  let cod = await prisma.game.findFirst({ where: { name: 'Call of Duty' } })
  if (!cod) {
    cod = await prisma.game.create({
      data: {
        name: 'Call of Duty',
        description: 'Call of Duty Mobile'
      }
    })
  }

  let pes = await prisma.game.findFirst({ where: { name: 'PES' } })
  if (!pes) {
    pes = await prisma.game.create({
      data: {
        name: 'PES',
        description: 'Pro Evolution Soccer'
      }
    })
  }

  // Create admin user if it doesn't exist
  const adminPassword = await hashPassword('Bsvca2223')
  const adminUser = await prisma.user.upsert({
    where: { username: 'Tournaowner' },
    update: {},
    create: {
      username: 'Tournaowner',
      firstName: 'Tournament',
      lastName: 'Owner',
      phoneNumber: '+265983132770',
      email: '<EMAIL>',
      password: adminPassword,
      role: 'ADMIN',
      isActive: true
    }
  })

  // Create sample users if they don't exist
  const user1 = await prisma.user.upsert({
    where: { username: 'player1' },
    update: {},
    create: {
      username: 'player1',
      firstName: 'John',
      lastName: 'Doe',
      phoneNumber: '+265991234567',
      password: '$2b$10$rQJ8YQZ9X1Y2Z3A4B5C6D7E8F9G0H1I2J3K4L5M6N7O8P9Q0R1S2T3', // hashed 'password123'
      role: 'PLAYER'
    }
  })

  const user2 = await prisma.user.upsert({
    where: { username: 'player2' },
    update: {},
    create: {
      username: 'player2',
      firstName: 'Jane',
      lastName: 'Smith',
      phoneNumber: '+265991234568',
      password: '$2b$10$rQJ8YQZ9X1Y2Z3A4B5C6D7E8F9G0H1I2J3K4L5M6N7O8P9Q0R1S2T3', // hashed 'password123'
      role: 'PLAYER'
    }
  })

  const user3 = await prisma.user.upsert({
    where: { username: 'player3' },
    update: {},
    create: {
      username: 'player3',
      firstName: 'Mike',
      lastName: 'Johnson',
      phoneNumber: '+265991234569',
      password: '$2b$10$rQJ8YQZ9X1Y2Z3A4B5C6D7E8F9G0H1I2J3K4L5M6N7O8P9Q0R1S2T3', // hashed 'password123'
      role: 'PLAYER'
    }
  })

  // Create sample weekly tournaments
  const currentYear = new Date().getFullYear()
  const currentWeek = getCurrentTournamentWeek()

  // Upcoming tournaments
  await prisma.weeklyTournament.upsert({
    where: { gameId_weekNumber_year: { gameId: pubg.id, weekNumber: currentWeek + 1, year: currentYear } },
    update: {},
    create: {
      gameId: pubg.id,
      weekNumber: currentWeek + 1,
      year: currentYear,
      tournamentDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next week
      status: 'UPCOMING',
      totalParticipants: 0
    }
  })

  await prisma.weeklyTournament.upsert({
    where: { gameId_weekNumber_year: { gameId: cod.id, weekNumber: currentWeek + 1, year: currentYear } },
    update: {},
    create: {
      gameId: cod.id,
      weekNumber: currentWeek + 1,
      year: currentYear,
      tournamentDate: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000), // Next week + 1 day
      status: 'UPCOMING',
      totalParticipants: 0
    }
  })

  await prisma.weeklyTournament.upsert({
    where: { gameId_weekNumber_year: { gameId: pes.id, weekNumber: currentWeek + 1, year: currentYear } },
    update: {},
    create: {
      gameId: pes.id,
      weekNumber: currentWeek + 1,
      year: currentYear,
      tournamentDate: new Date(Date.now() + 9 * 24 * 60 * 60 * 1000), // Next week + 2 days
      status: 'UPCOMING',
      totalParticipants: 0
    }
  })

  // Completed tournaments
  const completedTournament1 = await prisma.weeklyTournament.upsert({
    where: { gameId_weekNumber_year: { gameId: pubg.id, weekNumber: currentWeek - 1, year: currentYear } },
    update: {},
    create: {
      gameId: pubg.id,
      weekNumber: currentWeek - 1,
      year: currentYear,
      tournamentDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last week
      status: 'COMPLETED',
      winnerId: user1.id,
      totalParticipants: 12
    }
  })

  const completedTournament2 = await prisma.weeklyTournament.upsert({
    where: { gameId_weekNumber_year: { gameId: cod.id, weekNumber: currentWeek - 1, year: currentYear } },
    update: {},
    create: {
      gameId: cod.id,
      weekNumber: currentWeek - 1,
      year: currentYear,
      tournamentDate: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // Last week + 1 day
      status: 'COMPLETED',
      winnerId: user2.id,
      totalParticipants: 8
    }
  })

  // Create weekly winners
  await prisma.weeklyWinner.upsert({
    where: { gameId_weekNumber_year: { gameId: pubg.id, weekNumber: currentWeek - 1, year: currentYear } },
    update: {},
    create: {
      userId: user1.id,
      gameId: pubg.id,
      weekNumber: currentWeek - 1,
      year: currentYear,
      tournamentId: completedTournament1.id
    }
  })

  await prisma.weeklyWinner.upsert({
    where: { gameId_weekNumber_year: { gameId: cod.id, weekNumber: currentWeek - 1, year: currentYear } },
    update: {},
    create: {
      userId: user2.id,
      gameId: cod.id,
      weekNumber: currentWeek - 1,
      year: currentYear,
      tournamentId: completedTournament2.id
    }
  })

  // Create player stats
  await prisma.playerStats.upsert({
    where: { userId_gameId: { userId: user1.id, gameId: pubg.id } },
    update: {},
    create: {
      userId: user1.id,
      gameId: pubg.id,
      tournamentsParticipated: 5,
      tournamentsWon: 2,
      totalWins: 15,
      totalLosses: 8,
      winPercentage: 65.2
    }
  })

  await prisma.playerStats.upsert({
    where: { userId_gameId: { userId: user2.id, gameId: cod.id } },
    update: {},
    create: {
      userId: user2.id,
      gameId: cod.id,
      tournamentsParticipated: 4,
      tournamentsWon: 1,
      totalWins: 12,
      totalLosses: 6,
      winPercentage: 66.7
    }
  })

  await prisma.playerStats.upsert({
    where: { userId_gameId: { userId: user3.id, gameId: pes.id } },
    update: {},
    create: {
      userId: user3.id,
      gameId: pes.id,
      tournamentsParticipated: 3,
      tournamentsWon: 0,
      totalWins: 8,
      totalLosses: 10,
      winPercentage: 44.4
    }
  })

  // Create player registrations
  await prisma.playerRegistration.upsert({
    where: { userId_gameId: { userId: user1.id, gameId: pubg.id } },
    update: {},
    create: {
      userId: user1.id,
      gameId: pubg.id,
      gameUsername: 'JohnDoe_PUBG'
    }
  })

  await prisma.playerRegistration.upsert({
    where: { userId_gameId: { userId: user2.id, gameId: cod.id } },
    update: {},
    create: {
      userId: user2.id,
      gameId: cod.id,
      gameUsername: 'JaneSmith_COD'
    }
  })

  await prisma.playerRegistration.upsert({
    where: { userId_gameId: { userId: user3.id, gameId: pes.id } },
    update: {},
    create: {
      userId: user3.id,
      gameId: pes.id,
      gameUsername: 'MikeJ_PES'
    }
  })

  console.log('✅ Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
