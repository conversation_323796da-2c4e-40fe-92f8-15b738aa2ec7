// Load environment variables
require('dotenv').config({ path: '.env.local' })

const { PrismaClient } = require('@prisma/client')

async function testDatabaseConnection() {
  const prisma = new PrismaClient()
  
  try {
    console.log('Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connection successful')
    
    // Test if we can query users table
    const userCount = await prisma.user.count()
    console.log(`✅ Users table accessible - ${userCount} users found`)
    
    // Test if we can query games table
    const gameCount = await prisma.game.count()
    console.log(`✅ Games table accessible - ${gameCount} games found`)
    
    // Test if we can query registrations table
    const registrationCount = await prisma.playerRegistration.count()
    console.log(`✅ Player registrations table accessible - ${registrationCount} registrations found`)
    
    console.log('\n🎉 All database tests passed!')
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
    console.error('Full error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testDatabaseConnection()
