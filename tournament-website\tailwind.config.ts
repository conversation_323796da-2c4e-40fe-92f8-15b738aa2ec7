import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/hooks/**/*.{js,ts,jsx,tsx}",
    "./src/lib/**/*.{js,ts,jsx,tsx}",
  ],

  // Enable dark mode support
  darkMode: "class",

  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",

        // Tournament brand colors
        primary: {
          50: "#eff6ff",
          100: "#dbeafe",
          500: "#3b82f6",
          600: "#2563eb",
          700: "#1d4ed8",
          900: "#1e3a8a",
        },

        // Gaming theme colors
        gaming: {
          dark: "#0a0a0a",
          accent: "#00ff88",
          warning: "#ff6b35",
          success: "#00d4aa",
        },
      },

      fontFamily: {
        sans: ["var(--font-geist-sans)", "system-ui", "sans-serif"],
        mono: ["var(--font-geist-mono)", "monospace"],
      },

      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "pulse-slow": "pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite",
      },

      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
      },

      screens: {
        xs: "475px",
      },

      spacing: {
        18: "4.5rem",
        88: "22rem",
      },
    },
  },

  plugins: [
    // Add any additional plugins here if needed
  ],

  // Production optimizations
  ...(process.env.NODE_ENV === "production" && {
    // Optimize for smaller bundle size in production
    corePlugins: {
      // Disable unused core plugins
      preflight: true,
      container: true,
      accessibility: true,
      pointerEvents: true,
      visibility: true,
      position: true,
      inset: true,
      isolation: true,
      zIndex: true,
      order: true,
      gridColumn: true,
      gridColumnStart: true,
      gridColumnEnd: true,
      gridRow: true,
      gridRowStart: true,
      gridRowEnd: true,
      float: true,
      clear: true,
      margin: true,
      boxSizing: true,
      display: true,
      aspectRatio: true,
      height: true,
      maxHeight: true,
      minHeight: true,
      width: true,
      minWidth: true,
      maxWidth: true,
      flex: true,
      flexShrink: true,
      flexGrow: true,
      flexBasis: true,
      tableLayout: true,
      borderCollapse: true,
      borderSpacing: true,
      transformOrigin: true,
      translate: true,
      rotate: true,
      skew: true,
      scale: true,
      transform: true,
      animation: true,
      cursor: true,
      touchAction: true,
      userSelect: true,
      resize: true,
      scrollSnapType: true,
      scrollSnapAlign: true,
      scrollSnapStop: true,
      scrollMargin: true,
      scrollPadding: true,
      listStylePosition: true,
      listStyleType: true,
      appearance: true,
      columns: true,
      breakBefore: true,
      breakInside: true,
      breakAfter: true,
      gridAutoColumns: true,
      gridAutoFlow: true,
      gridAutoRows: true,
      gridTemplateColumns: true,
      gridTemplateRows: true,
      flexDirection: true,
      flexWrap: true,
      placeContent: true,
      placeItems: true,
      alignContent: true,
      alignItems: true,
      justifyContent: true,
      justifyItems: true,
      gap: true,
      space: true,
      divideWidth: true,
      divideStyle: true,
      divideColor: true,
      divideOpacity: true,
      placeSelf: true,
      alignSelf: true,
      justifySelf: true,
      overflow: true,
      overscrollBehavior: true,
      scrollBehavior: true,
      textOverflow: true,
      whitespace: true,
      wordBreak: true,
      borderRadius: true,
      borderWidth: true,
      borderStyle: true,
      borderColor: true,
      borderOpacity: true,
      backgroundColor: true,
      backgroundOpacity: true,
      backgroundImage: true,
      gradientColorStops: true,
      backgroundSize: true,
      backgroundAttachment: true,
      backgroundClip: true,
      backgroundPosition: true,
      backgroundRepeat: true,
      backgroundOrigin: true,
      fill: true,
      stroke: true,
      strokeWidth: true,
      objectFit: true,
      objectPosition: true,
      padding: true,
      textAlign: true,
      textColor: true,
      textOpacity: true,
      textDecoration: true,
      textDecorationColor: true,
      textDecorationStyle: true,
      textDecorationThickness: true,
      textUnderlineOffset: true,
      fontFamily: true,
      fontSize: true,
      fontWeight: true,
      textTransform: true,
      fontStyle: true,
      fontVariantNumeric: true,
      lineHeight: true,
      letterSpacing: true,
      textIndent: true,
      verticalAlign: true,
      placeholderColor: true,
      placeholderOpacity: true,
      caretColor: true,
      accentColor: true,
      opacity: true,
      boxShadow: true,
      boxShadowColor: true,
      outlineWidth: true,
      outlineStyle: true,
      outlineColor: true,
      outlineOffset: true,
      ringWidth: true,
      ringColor: true,
      ringOpacity: true,
      ringOffsetWidth: true,
      ringOffsetColor: true,
      blur: true,
      brightness: true,
      contrast: true,
      dropShadow: true,
      grayscale: true,
      hueRotate: true,
      invert: true,
      saturate: true,
      sepia: true,
      filter: true,
      backdropBlur: true,
      backdropBrightness: true,
      backdropContrast: true,
      backdropGrayscale: true,
      backdropHueRotate: true,
      backdropInvert: true,
      backdropOpacity: true,
      backdropSaturate: true,
      backdropSepia: true,
      backdropFilter: true,
      transitionProperty: true,
      transitionDelay: true,
      transitionDuration: true,
      transitionTimingFunction: true,
      willChange: true,
      content: true,
    },
  }),
};

export default config;
