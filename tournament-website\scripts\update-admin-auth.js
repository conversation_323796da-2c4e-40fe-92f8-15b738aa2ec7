const fs = require('fs')
const path = require('path')

// Admin pages that need authentication protection
const adminPages = [
  'src/app/admin/registrations/page.tsx',
  'src/app/admin/schedules/page.tsx',
  'src/app/admin/announcements/page.tsx',
  'src/app/admin/leaderboard/page.tsx',
  'src/app/admin/stats/page.tsx',
  'src/app/admin/monitoring/page.tsx',
  'src/app/admin/player-management/page.tsx',
  'src/app/admin/completed/page.tsx',
  'src/app/admin/upcoming/page.tsx'
]

function updateAdminPage(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`)
      return
    }

    let content = fs.readFileSync(filePath, 'utf8')
    
    // Skip if already has AdminAuthGuard
    if (content.includes('AdminAuthGuard')) {
      console.log(`✅ Already protected: ${filePath}`)
      return
    }

    // Add imports at the top
    const importRegex = /('use client'\s*\n\n?)(import.*from.*\n)*/
    const newImports = `$1$2import { AdminAuthGuard } from '@/components/AdminAuthGuard'
import { useAdminAuth } from '@/hooks/useAdminAuth'
`
    
    content = content.replace(importRegex, newImports)

    // Find the main export function
    const exportRegex = /export default function (\w+)\(/
    const match = content.match(exportRegex)
    
    if (!match) {
      console.log(`⚠️  Could not find export function in: ${filePath}`)
      return
    }

    const functionName = match[1]
    const contentFunctionName = functionName.replace(/Page$/, 'Content')

    // Replace export default function with content function
    content = content.replace(
      `export default function ${functionName}(`,
      `function ${contentFunctionName}(`
    )

    // Add useAdminAuth hook if there's a router
    if (content.includes('const router = useRouter()')) {
      content = content.replace(
        'const router = useRouter()',
        `const router = useRouter()
  const { logout } = useAdminAuth()`
      )
    }

    // Update logout handlers if they exist
    if (content.includes('localStorage.removeItem(\'adminToken\')')) {
      content = content.replace(
        /const handleLogout = \(\) => \{\s*localStorage\.removeItem\('adminToken'\)\s*router\.push\('\/admin\/login'\)\s*\}/g,
        `const handleLogout = () => {
    logout()
  }`
      )
    }

    // Add the wrapper export at the end
    const wrapperFunction = `
export default function ${functionName}() {
  return (
    <AdminAuthGuard>
      <${contentFunctionName} />
    </AdminAuthGuard>
  )
}`

    content = content + wrapperFunction

    // Write the updated content
    fs.writeFileSync(filePath, content)
    console.log(`✅ Updated: ${filePath}`)

  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message)
  }
}

function main() {
  console.log('🔐 Updating Admin Pages with Authentication Guards...\n')

  adminPages.forEach(pagePath => {
    updateAdminPage(pagePath)
  })

  console.log('\n🎉 Admin authentication update completed!')
  console.log('\n📋 Summary:')
  console.log('- Added AdminAuthGuard to all admin pages')
  console.log('- Added useAdminAuth hook integration')
  console.log('- Updated logout handlers')
  console.log('- Server-side middleware protects routes')
  console.log('\n🔒 All admin routes are now fully protected!')
}

if (require.main === module) {
  main()
}

module.exports = { updateAdminPage }
