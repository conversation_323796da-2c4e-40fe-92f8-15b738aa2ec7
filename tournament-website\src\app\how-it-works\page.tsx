'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'

interface User {
  id: number
  username: string
  firstName: string
  lastName: string
  role: string
}

export default function HowItWorksPage() {
  const [user, setUser] = useState<User | null>(null)
  const [authLoading, setAuthLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
      }
    } catch (error) {
      console.error('Auth check failed:', error)
    } finally {
      setAuthLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      setUser(null)
      window.location.href = '/'
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/" className="text-3xl font-bold text-blue-600">eSports RXP</Link>
            </div>
            <nav className="flex space-x-6 items-center">
              <Link href="/" className="text-gray-500 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors" title="Home">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>

              </Link>
              <Link href="/leaderboard" className="text-gray-500 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors" title="Leaderboard">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>

              </Link>
              <Link href="/schedule" className="text-gray-500 hover:text-gray-900 p-2 hover:bg-gray-50 rounded-lg transition-colors" title="Schedule">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>

              </Link>
              <Link href="/how-it-works" className="text-blue-600 font-medium p-2 hover:bg-blue-50 rounded-lg transition-colors" title="How It Works">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>

              </Link>
              <a
                href="https://wa.me/265983132770"
                target="_blank"
                rel="noopener noreferrer"
                className="text-green-600 hover:text-green-700 p-2 hover:bg-green-50 rounded-lg transition-colors"
                title="Contact via WhatsApp"
              >
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>

              </a>

              {!authLoading && (
                <>
                  {user ? (
                    <div className="flex items-center space-x-4">
                      <Link
                        href="/profile"
                        className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 font-medium bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded-lg transition-colors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span>{user.firstName} {user.lastName}</span>
                      </Link>
                      {user.role === 'ADMIN' && (
                        <Link
                          href="/admin"
                          className="bg-red-600 text-white px-3 py-1 rounded-md text-sm hover:bg-red-700"
                        >
                          Admin
                        </Link>
                      )}
                      <button
                        onClick={handleLogout}
                        className="text-red-600 hover:text-red-700 p-2 hover:bg-red-50 rounded-lg transition-colors"
                        title="Logout"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>

                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-4">
                      <Link
                        href="/login"
                        className="text-gray-700 hover:text-gray-900 font-medium"
                      >
                        Login
                      </Link>
                      <Link
                        href="/signup"
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Sign Up
                      </Link>
                    </div>
                  )}
                </>
              )}
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">How eSports RXP Works</h1>
          
          {/* How It Works Section */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">🎮 Tournament System Overview</h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">1</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">Create Your Account</h3>
                    <p className="text-gray-600">Sign up with your details including Malawi phone number for verification and WhatsApp notifications.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">2</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">Register for Tournaments</h3>
                    <p className="text-gray-600">Choose from Call of Duty, PES and PUBG and tournaments. Your profile data auto-fills registration forms.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">3</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">Pay Entry Fee</h3>
                    <p className="text-gray-600">Complete payment to secure your spot. Paid players are prioritized in tournament lists.</p>
                  </div>
                </div>
              </div>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">4</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">Play Externally</h3>
                    <p className="text-gray-600">Tournaments are played on external platforms. This website is for registration and information only.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">5</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">Track Your Progress</h3>
                    <p className="text-gray-600">View your stats, tournament history and current leaderboard position in your profile.</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">6</div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">Win Prizes</h3>
                    <p className="text-gray-600">Top performers receive cash prizes distributed within the same working day after tournament conclusion.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tournament Limits */}
          <div className="mb-12 bg-blue-50 rounded-lg p-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">📊 Tournament Capacity</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">32/Phase</div>
                <div className="text-gray-600">PES Players Max</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">64/Phase</div>
                <div className="text-gray-600">PUBG Players Max</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600">64/Phase</div>
                <div className="text-gray-600">Call of Duty Max</div>
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-4 text-center">
              <strong>All games use phase systems</strong> for overflow management when exceeding capacity. PES, PUBG, and Call of Duty all support multiple phases to accommodate more players.
            </p>
          </div>

          {/* Prize Policy Section */}
          <div className="border-t pt-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">🏆 Tournament Prize Policy</h2>
            
            <div className="mb-8">
              <p className="text-lg text-gray-700 mb-4">
                Welcome to our tournament platform! We are committed to providing a fair, transparent and rewarding experience for all players. 
                Please read this prize distribution policy carefully.
              </p>
            </div>

            {/* Prize Pool Info */}
            <div className="mb-8 bg-yellow-50 rounded-lg p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-3">💰 Prize Pool</h3>
              <p className="text-gray-700">
                The total prize pool is calculated based on the number of registered players and their entry fees.
                All entry fees are collected before the tournament begins.
              </p>
            </div>

            {/* Top 4 Prize Distribution */}
            <div className="mb-12">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">🥇 Prize Distribution, PES (Top 8 Winners)</h3>

              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-lg font-bold text-gray-800 mb-4">Prize Payouts (32 Players)</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white rounded-lg shadow">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">Entry Fee per Player</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold text-gray-700">1st Place</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold text-gray-700">2nd Place</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold text-gray-700">3rd Place</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold text-gray-700">4th Place</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold text-gray-700">5th Place</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold text-gray-700">6th Place</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold text-gray-700">7th Place</th>
                        <th className="px-4 py-3 text-center text-sm font-semibold text-gray-700">8th Place</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      <tr>
                        <td className="px-4 py-3 font-bold">2,000</td>
                        <td className="px-4 py-3 text-center text-green-700 font-bold">12,000</td>
                        <td className="px-4 py-3 text-center text-green-600 font-bold">10,000</td>
                        <td className="px-4 py-3 text-center text-blue-600 font-bold">8,000</td>
                        <td className="px-4 py-3 text-center text-blue-500 font-bold">6,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">5,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">5,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">4,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">4,000</td>
                      </tr>

                      <tr className="bg-gray-50">
                        <td className="px-4 py-3 font-bold">3,000</td>
                        <td className="px-4 py-3 text-center text-green-700 font-bold">20,000</td>
                        <td className="px-4 py-3 text-center text-green-600 font-bold">15,000</td>
                        <td className="px-4 py-3 text-center text-blue-600 font-bold">12,000</td>
                        <td className="px-4 py-3 text-center text-blue-500 font-bold">9,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">7,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">7,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">6,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">6,000</td>
                      </tr>

                      <tr>
                        <td className="px-4 py-3 font-bold">4,000</td>
                        <td className="px-4 py-3 text-center text-green-700 font-bold">26,000</td>
                        <td className="px-4 py-3 text-center text-green-600 font-bold">20,000</td>
                        <td className="px-4 py-3 text-center text-blue-600 font-bold">16,000</td>
                        <td className="px-4 py-3 text-center text-blue-500 font-bold">12,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">9,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">9,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">8,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">8,000</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="px-4 py-3 font-bold">5,000</td>
                        <td className="px-4 py-3 text-center text-green-700 font-bold">32,000</td>
                        <td className="px-4 py-3 text-center text-green-600 font-bold">26,000</td>
                        <td className="px-4 py-3 text-center text-blue-600 font-bold">20,000</td>
                        <td className="px-4 py-3 text-center text-blue-500 font-bold">16,000</td>
                         <td className="px-4 py-3 text-center text-purple-600 font-bold">12,000</td>
                          <td className="px-4 py-3 text-center text-purple-600 font-bold">12,000</td>
                           <td className="px-4 py-3 text-center text-pink-600 font-bold">10,000</td>
                            <td className="px-4 py-3 text-center text-pink-600 font-bold">10,000</td>
                      </tr>
                      <tr>
                        <td className="px-4 py-3 font-bold">6,000</td>
                        <td className="px-4 py-3 text-center text-green-700 font-bold">38,000</td>
                        <td className="px-4 py-3 text-center text-green-600 font-bold">30,000</td>
                        <td className="px-4 py-3 text-center text-blue-600 font-bold">28,000</td>
                        <td className="px-4 py-3 text-center text-blue-500 font-bold">18,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">15,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">15,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">12,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">12,000</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="px-4 py-3 font-bold">7,000</td>
                        <td className="px-4 py-3 text-center text-green-700 font-bold">44,000</td>
                        <td className="px-4 py-3 text-center text-green-600 font-bold">35,000</td>
                        <td className="px-4 py-3 text-center text-blue-600 font-bold">28,000</td>
                        <td className="px-4 py-3 text-center text-blue-500 font-bold">22,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">18,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">18,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">14,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">14,000</td>
                      </tr>
                      <tr>
                        <td className="px-4 py-3 font-bold">8,000</td>
                        <td className="px-4 py-3 text-center text-green-700 font-bold">50,000</td>
                        <td className="px-4 py-3 text-center text-green-600 font-bold">40,000</td>
                        <td className="px-4 py-3 text-center text-blue-600 font-bold">32,000</td>
                        <td className="px-4 py-3 text-center text-blue-500 font-bold">24,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">20,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">20,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">16,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">16,000</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="px-4 py-3 font-bold">9,000</td>
                        <td className="px-4 py-3 text-center text-green-700 font-bold">56,000</td>
                        <td className="px-4 py-3 text-center text-green-600 font-bold">48,000</td>
                        <td className="px-4 py-3 text-center text-blue-600 font-bold">36,000</td>
                        <td className="px-4 py-3 text-center text-blue-500 font-bold">28,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">23,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">23,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">18,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">18,000</td>
                      </tr>
                      <tr>
                        <td className="px-4 py-3 font-bold">10,000</td>
                        <td className="px-4 py-3 text-center text-green-700 font-bold">65,000</td>
                        <td className="px-4 py-3 text-center text-green-600 font-bold">52,000</td>
                        <td className="px-4 py-3 text-center text-blue-600 font-bold">42,000</td>
                        <td className="px-4 py-3 text-center text-blue-500 font-bold">32,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">25,000</td>
                        <td className="px-4 py-3 text-center text-purple-600 font-bold">25,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">20,000</td>
                        <td className="px-4 py-3 text-center text-pink-600 font-bold">20,000</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <p className="text-sm text-gray-600 mt-4">
                  <strong>Note:</strong> The above table does not include administrative or operational deductions "Fees". Full terms apply.
                </p>
              </div>
            </div>

            {/* Top 10 Prize Distribution */}
            <div className="mb-12">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">🏆 Prize Distribution, PUBG/COD (Top 16 Winners)</h3>

              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-lg font-bold text-gray-800 mb-4">Example Payouts (64 Players)
                </h4>

                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white rounded-lg shadow text-sm">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="px-3 py-2 text-left font-semibold text-gray-700">Entry Fee</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">1st</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">2nd</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">3rd</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">4th</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">5th</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">6th</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">7th</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">8th</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">9th</th>
                        <th className="px-3 py-2 text-center font-semibold text-gray-700">10th</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      <tr>
                        <td className="px-3 py-2 font-medium">2,000</td>
                        <td className="px-3 py-2 text-center text-green-600 font-bold">25,000</td>
                        <td className="px-3 py-2 text-center text-blue-600 font-bold">20,000</td>
                        <td className="px-3 py-2 text-center text-orange-600 font-bold">13,000</td>
                        <td className="px-3 py-2 text-center font-bold">10,000</td>
                        <td className="px-3 py-2 text-center font-bold">8,000</td>
                        <td className="px-3 py-2 text-center font-bold">6,000</td>
                        <td className="px-3 py-2 text-center font-bold">5,000</td>
                        <td className="px-3 py-2 text-center font-bold">4,000</td>
                        <td className="px-3 py-2 text-center font-bold">3,000</td>
                        <td className="px-3 py-2 text-center font-bold">2,000</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="px-3 py-2 font-medium">3,000</td>
                        <td className="px-3 py-2 text-center text-green-600 font-bold">40,000</td>
                        <td className="px-3 py-2 text-center text-blue-600 font-bold">28,000</td>
                        <td className="px-3 py-2 text-center text-orange-600 font-bold">20,000</td>
                        <td className="px-3 py-2 text-center font-bold">15,000</td>
                        <td className="px-3 py-2 text-center font-bold">12,000</td>
                        <td className="px-3 py-2 text-center font-bold">9,000</td>
                        <td className="px-3 py-2 text-center font-bold">7,000</td>
                        <td className="px-3 py-2 text-center font-bold">6,000</td>
                        <td className="px-3 py-2 text-center font-bold">4,000</td>
                        <td className="px-3 py-2 text-center font-bold">3,000</td>
                      </tr>
                      <tr>
                        <td className="px-3 py-2 font-medium">4,000</td>
                        <td className="px-3 py-2 text-center text-green-600 font-bold">50,000</td>
                        <td className="px-3 py-2 text-center text-blue-600 font-bold">38,000</td>
                        <td className="px-3 py-2 text-center text-orange-600 font-bold">28,000</td>
                        <td className="px-3 py-2 text-center font-bold">20,000</td>
                        <td className="px-3 py-2 text-center font-bold">16,000</td>
                        <td className="px-3 py-2 text-center font-bold">12,000</td>
                        <td className="px-3 py-2 text-center font-bold">10,000</td>
                        <td className="px-3 py-2 text-center font-bold">8,000</td>
                        <td className="px-3 py-2 text-center font-bold">6,000</td>
                        <td className="px-3 py-2 text-center font-bold">4,000</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="px-3 py-2 font-medium">5,000</td>
                        <td className="px-3 py-2 text-center text-green-600 font-bold">65,000</td>
                        <td className="px-3 py-2 text-center text-blue-600 font-bold">48,000</td>
                        <td className="px-3 py-2 text-center text-orange-600 font-bold">34,000</td>
                        <td className="px-3 py-2 text-center font-bold">24,000</td>
                        <td className="px-3 py-2 text-center font-bold">20,000</td>
                        <td className="px-3 py-2 text-center font-bold">15,000</td>
                        <td className="px-3 py-2 text-center font-bold">12,000</td>
                        <td className="px-3 py-2 text-center font-bold">10,000</td>
                        <td className="px-3 py-2 text-center font-bold">7,000</td>
                        <td className="px-3 py-2 text-center font-bold">5,000</td>
                      </tr>
                      <tr>
                        <td className="px-3 py-2 font-medium">6,000</td>
                        <td className="px-3 py-2 text-center text-green-600 font-bold">80,000</td>
                        <td className="px-3 py-2 text-center text-blue-600 font-bold">58,000</td>
                        <td className="px-3 py-2 text-center text-orange-600 font-bold">40,000</td>
                        <td className="px-3 py-2 text-center font-bold">28,000</td>
                        <td className="px-3 py-2 text-center font-bold">22,000</td>
                        <td className="px-3 py-2 text-center font-bold">18,000</td>
                        <td className="px-3 py-2 text-center font-bold">15,000</td>
                        <td className="px-3 py-2 text-center font-bold">12,000</td>
                        <td className="px-3 py-2 text-center font-bold">9,000</td>
                        <td className="px-3 py-2 text-center font-bold">6,000</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="px-3 py-2 font-medium">7,000</td>
                        <td className="px-3 py-2 text-center text-green-600 font-bold">95,000</td>
                        <td className="px-3 py-2 text-center text-blue-600 font-bold">70,000</td>
                        <td className="px-3 py-2 text-center text-orange-600 font-bold">47,000</td>
                        <td className="px-3 py-2 text-center font-bold">33,000</td>
                        <td className="px-3 py-2 text-center font-bold">27,000</td>
                        <td className="px-3 py-2 text-center font-bold">20,000</td>
                        <td className="px-3 py-2 text-center font-bold">16,000</td>
                        <td className="px-3 py-2 text-center font-bold">13,000</td>
                        <td className="px-3 py-2 text-center font-bold">9,000</td>
                        <td className="px-3 py-2 text-center font-bold">7,000</td>
                      </tr>
                      <tr>
                        <td className="px-3 py-2 font-medium">8,000</td>
                        <td className="px-3 py-2 text-center text-green-600 font-bold">105,000</td>
                        <td className="px-3 py-2 text-center text-blue-600 font-bold">75,000</td>
                        <td className="px-3 py-2 text-center text-orange-600 font-bold">50,000</td>
                        <td className="px-3 py-2 text-center font-bold">35,000</td>
                        <td className="px-3 py-2 text-center font-bold">30,000</td>
                        <td className="px-3 py-2 text-center font-bold">25,000</td>
                        <td className="px-3 py-2 text-center font-bold">20,000</td>
                        <td className="px-3 py-2 text-center font-bold">16,000</td>
                        <td className="px-3 py-2 text-center font-bold">12,000</td>
                        <td className="px-3 py-2 text-center font-bold">8,000</td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="px-3 py-2 font-medium">9,000</td>
                        <td className="px-3 py-2 text-center text-green-600 font-bold">124,000</td>
                        <td className="px-3 py-2 text-center text-blue-600 font-bold">88,000</td>
                        <td className="px-3 py-2 text-center text-orange-600 font-bold">60,000</td>
                        <td className="px-3 py-2 text-center font-bold">44,000</td>
                        <td className="px-3 py-2 text-center font-bold">32,000</td>
                        <td className="px-3 py-2 text-center font-bold">26,000</td>
                        <td className="px-3 py-2 text-center font-bold">22,000</td>
                        <td className="px-3 py-2 text-center font-bold">18,000</td>
                        <td className="px-3 py-2 text-center font-bold">13,000</td>
                        <td className="px-3 py-2 text-center font-bold">9,000</td>
                      </tr>
                      <tr>
                        <td className="px-3 py-2 font-medium">10,000</td>
                        <td className="px-3 py-2 text-center text-green-600 font-bold">135,000</td>
                        <td className="px-3 py-2 text-center text-blue-600 font-bold">98,000</td>
                        <td className="px-3 py-2 text-center text-orange-600 font-bold">68,000</td>
                        <td className="px-3 py-2 text-center font-bold">48,000</td>
                        <td className="px-3 py-2 text-center font-bold">38,000</td>
                        <td className="px-3 py-2 text-center font-bold">30,000</td>
                        <td className="px-3 py-2 text-center font-bold">25,000</td>
                        <td className="px-3 py-2 text-center font-bold">20,000</td>
                        <td className="px-3 py-2 text-center font-bold">15,000</td>
                        <td className="px-3 py-2 text-center font-bold">10,000</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Payment and Terms */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">💳 Payment of Prizes</h3>
              <div className="bg-green-50 rounded-lg p-6 mb-6">
                <div className="flex items-center mb-4">
                  <svg className="w-6 h-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                  <h4 className="text-lg font-bold text-gray-800">Fast Payment</h4>
                </div>
                <p className="text-gray-700 mb-4">
                  Prizes are distributed within the <strong>same working day</strong> after tournament conclusion via mobile money and cash in hand.
                </p>
                <p className="text-gray-700">
                  Winners must provide proof of identity and complete any required forms to receive their prizes.
                </p>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="bg-red-50 rounded-lg p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4">⚠️ Changes and Exceptions</h3>
              <p className="text-gray-700 mb-4">
                The organizer reserves the right to adjust prize amounts if participant numbers change or in case of unforeseen circumstances.
              </p>
              <p className="text-gray-700">
                Any changes will be communicated before the tournament starts. Amounts are always rounded for simplicity and transparency.
                Any remaining funds are retained for tournament operations and future events.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
