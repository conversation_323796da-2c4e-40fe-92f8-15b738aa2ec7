import { logger } from '@/lib/logger'

// Mock console methods
const mockConsole = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
}

// Store original console
const originalConsole = { ...console }

describe('Logger', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Replace console methods with mocks
    Object.assign(console, mockConsole)
  })

  afterEach(() => {
    // Restore original console
    Object.assign(console, originalConsole)
  })

  describe('in development mode', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'development'
    })

    it('should log debug messages in development', () => {
      logger.debug('Test debug message', { data: 'test' })
      
      expect(mockConsole.debug).toHaveBeenCalledWith(
        '[DEBUG] Test debug message',
        { data: 'test' }
      )
    })

    it('should log info messages', () => {
      logger.info('Test info message')
      
      expect(mockConsole.info).toHaveBeenCalledWith('[INFO] Test info message')
    })

    it('should log warning messages', () => {
      logger.warn('Test warning message', { warning: true })
      
      expect(mockConsole.warn).toHaveBeenCalledWith(
        '[WARN] Test warning message',
        { warning: true }
      )
    })

    it('should log error messages', () => {
      logger.error('Test error message')
      
      expect(mockConsole.error).toHaveBeenCalledWith('[ERROR] Test error message')
    })
  })

  describe('in production mode', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'production'
    })

    it('should not log debug messages in production', () => {
      logger.debug('Test debug message')
      
      expect(mockConsole.debug).not.toHaveBeenCalled()
      expect(mockConsole.log).not.toHaveBeenCalled()
    })

    it('should log structured JSON in production', () => {
      logger.info('Test info message', { data: 'test' })
      
      expect(mockConsole.log).toHaveBeenCalledWith(
        expect.stringContaining('"level":"info"')
      )
      expect(mockConsole.log).toHaveBeenCalledWith(
        expect.stringContaining('"message":"Test info message"')
      )
    })
  })

  describe('convenience methods', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'development'
    })

    it('should log API errors with context', () => {
      const error = new Error('Test error')
      error.stack = 'Error stack trace'
      
      logger.apiError('/api/test', error, { userId: 123 })
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        '[ERROR] API Error in /api/test',
        expect.objectContaining({
          error: 'Test error',
          stack: 'Error stack trace',
          userId: 123
        })
      )
    })

    it('should log authentication events', () => {
      logger.authEvent('login', 123, { ip: '127.0.0.1' })
      
      expect(mockConsole.info).toHaveBeenCalledWith(
        '[INFO] Auth: login',
        expect.objectContaining({
          userId: 123,
          ip: '127.0.0.1'
        })
      )
    })

    it('should log database operations', () => {
      logger.dbOperation('SELECT', 'users', { count: 5 })
      
      expect(mockConsole.debug).toHaveBeenCalledWith(
        '[DEBUG] DB: SELECT on users',
        { count: 5 }
      )
    })
  })
})
