# Production Environment Variables Template
# Copy this file to .env.production and fill in the actual values

# Database Configuration
DATABASE_URL="postgresql://username:password@host:port/database_name"

# Next.js Configuration
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="generate-a-strong-random-secret-here-minimum-32-characters"
NODE_ENV="production"

# Admin Credentials (Change these for production!)
ADMIN_USERNAME="your-admin-username"
ADMIN_PASSWORD="your-secure-admin-password"

# WhatsApp Notification Settings
WHATSAPP_NUMBER="+265983132770"

# Optional: External Logging Service
# LOG_SERVICE_URL="https://your-logging-service.com/api/logs"
# LOG_SERVICE_API_KEY="your-api-key"

# Optional: Email Service (for future notifications)
# SMTP_HOST="smtp.your-provider.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-email-password"

# Security Settings
# SESSION_TIMEOUT="604800" # 7 days in seconds
# MAX_LOGIN_ATTEMPTS="5"
# RATE_LIMIT_WINDOW="900000" # 15 minutes in milliseconds
# RATE_LIMIT_MAX="100" # Max requests per window

# Production Optimizations
# ENABLE_COMPRESSION="true"
# ENABLE_CACHING="true"
# CACHE_TTL="3600" # 1 hour in seconds
