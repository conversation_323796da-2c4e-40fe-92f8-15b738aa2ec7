import { NextRequest, NextResponse } from 'next/server'
import { validateSession } from '@/lib/auth'
import { cookies } from 'next/headers'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'

export async function GET(request: NextRequest) {
  try {
    const sessionCookie = request.cookies.get('session')
    if (!sessionCookie) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    const session = await validateSession(sessionCookie.value)
    if (!session) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 })
    }

    // Get user profile data
    const user = await prisma.user.findUnique({
      where: { id: session.userId },
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        phoneNumber: true,
        email: true,
        gender: true,
        address: true,
        role: true,
        createdAt: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json({ user })

  } catch (error) {
    logger.error('Error fetching user profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const sessionCookie = request.cookies.get('session')
    if (!sessionCookie) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    const session = await validateSession(sessionCookie.value)
    if (!session) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 })
    }

    // Profile editing is now allowed without restrictions

    const body = await request.json()
    const {
      username,
      firstName,
      lastName,
      phoneNumber,
      email,
      gender,
      address
    } = body

    // Validate required fields
    if (!username || !firstName || !lastName || !phoneNumber) {
      return NextResponse.json({
        error: 'Username, first name, last name, and phone number are required'
      }, { status: 400 })
    }

    // Validate username format (alphanumeric and underscores only, 3-20 characters)
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
    if (!usernameRegex.test(username)) {
      return NextResponse.json({
        error: 'Username must be 3-20 characters long and contain only letters, numbers, and underscores'
      }, { status: 400 })
    }

    // Validate phone number format (Malawi)
    const phoneRegex = /^\+265[0-9]{9}$/
    if (!phoneRegex.test(phoneNumber)) {
      return NextResponse.json({
        error: 'Phone number must be in format +265XXXXXXXXX'
      }, { status: 400 })
    }

    // Check if username is already taken by another user
    const existingUsername = await prisma.user.findFirst({
      where: {
        username,
        id: { not: session.userId }
      }
    })

    if (existingUsername) {
      return NextResponse.json({
        error: 'Username is already taken'
      }, { status: 400 })
    }

    // Check if phone number is already taken by another user
    const existingUser = await prisma.user.findFirst({
      where: {
        phoneNumber,
        id: { not: session.userId }
      }
    })

    if (existingUser) {
      return NextResponse.json({ 
        error: 'Phone number is already registered to another user' 
      }, { status: 400 })
    }

    // Check if email is already taken by another user (if provided)
    if (email) {
      const existingEmailUser = await prisma.user.findFirst({
        where: {
          email,
          id: { not: session.userId }
        }
      })

      if (existingEmailUser) {
        return NextResponse.json({ 
          error: 'Email is already registered to another user' 
        }, { status: 400 })
      }
    }

    // Update user profile
    const updatedUser = await prisma.user.update({
      where: { id: session.userId },
      data: {
        username,
        firstName,
        lastName,
        phoneNumber,
        email: email || null,
        gender: gender || null,
        address: address || null
      },
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        phoneNumber: true,
        email: true,
        gender: true,
        address: true,
        role: true
      }
    })

    return NextResponse.json({ 
      message: 'Profile updated successfully',
      user: updatedUser
    })
  } catch (error) {
    logger.error('Error updating profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const sessionCookie = request.cookies.get('session')
    if (!sessionCookie) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    const session = await validateSession(sessionCookie.value)
    if (!session) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 })
    }

    // Get password from request body
    const body = await request.json()
    const { password } = body

    if (!password) {
      return NextResponse.json({ error: 'Password is required' }, { status: 400 })
    }

    // Get user to verify password
    const user = await prisma.user.findUnique({
      where: { id: session.userId }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      return NextResponse.json({ error: 'Invalid password' }, { status: 400 })
    }

    // Prevent admin users from deleting their accounts
    if (session.role === 'ADMIN') {
      return NextResponse.json({
        error: 'Admin accounts cannot be deleted'
      }, { status: 403 })
    }

    // Delete the user - Prisma cascade will handle related records
    await prisma.user.delete({
      where: { id: session.userId }
    })

    // Clear the session cookie
    const cookieStore = await cookies()
    cookieStore.delete('session')

    return NextResponse.json({
      message: 'Account deleted successfully'
    })

  } catch (error) {
    logger.error('Error deleting account:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
