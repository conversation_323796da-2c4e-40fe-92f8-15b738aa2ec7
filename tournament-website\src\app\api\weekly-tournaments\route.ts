import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const gameId = searchParams.get('gameId')
    const year = searchParams.get('year')
    const status = searchParams.get('status')

    const where: any = {}
    if (gameId) where.gameId = parseInt(gameId)
    if (year) where.year = parseInt(year)
    if (status) where.status = status

    const tournaments = await prisma.weeklyTournament.findMany({
      where,
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        },
        winner: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ]
    })

    return NextResponse.json(tournaments)
  } catch (error) {
    logger.error('Error fetching weekly tournaments:', error)
    return NextResponse.json(
      { error: 'Failed to fetch weekly tournaments' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { gameId, weekNumber, year, tournamentDate, totalParticipants, winnerId } = body

    if (!gameId || !weekNumber || !year || !tournamentDate) {
      return NextResponse.json(
        { error: 'Game ID, week number, year, and tournament date are required' },
        { status: 400 }
      )
    }

    const tournament = await prisma.weeklyTournament.create({
      data: {
        gameId: parseInt(gameId),
        weekNumber: parseInt(weekNumber),
        year: parseInt(year),
        tournamentDate: new Date(tournamentDate),
        totalParticipants: totalParticipants || 0,
        winnerId: winnerId ? parseInt(winnerId) : null,
        status: winnerId ? 'COMPLETED' : 'UPCOMING'
      },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        },
        winner: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        }
      }
    })

    // If there's a winner, create a weekly winner record and update stats
    if (winnerId) {
      await prisma.weeklyWinner.create({
        data: {
          userId: parseInt(winnerId),
          gameId: parseInt(gameId),
          weekNumber: parseInt(weekNumber),
          year: parseInt(year),
          tournamentId: tournament.id
        }
      })

      // Update player stats
      await prisma.playerStats.upsert({
        where: {
          userId_gameId: {
            userId: parseInt(winnerId),
            gameId: parseInt(gameId)
          }
        },
        update: {
          tournamentsParticipated: { increment: 1 },
          tournamentsWon: { increment: 1 },
          totalWins: { increment: 1 },
          lastUpdated: new Date()
        },
        create: {
          userId: parseInt(winnerId),
          gameId: parseInt(gameId),
          tournamentsParticipated: 1,
          tournamentsWon: 1,
          totalWins: 1
        }
      })
    }

    return NextResponse.json(tournament, { status: 201 })
  } catch (error) {
    logger.error('Error creating weekly tournament:', error)
    return NextResponse.json(
      { error: 'Failed to create weekly tournament' },
      { status: 500 }
    )
  }
}
