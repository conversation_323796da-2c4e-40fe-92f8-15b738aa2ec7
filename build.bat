@echo off
REM Mzuni Tournaments - Build Script (Windows)
REM This script handles the complete build process for deployment

echo 🏆 Starting Mzuni Tournaments Build Process...

REM Check if we're in the right directory
if not exist "tournament-website\package.json" (
    echo [ERROR] tournament-website\package.json not found. Please run this script from the project root.
    exit /b 1
)

REM Change to tournament-website directory
cd tournament-website

echo [INFO] Checking Node.js version...
node --version
npm --version

REM Clean previous builds
echo [INFO] Cleaning previous builds...
if exist ".next" (
    rmdir /s /q ".next"
    echo [SUCCESS] Removed .next directory
)

if exist "node_modules" (
    echo [WARNING] Removing existing node_modules for fresh install...
    rmdir /s /q "node_modules"
)

REM Install dependencies
echo [INFO] Installing dependencies...
npm ci --production=false
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    exit /b 1
)
echo [SUCCESS] Dependencies installed successfully

REM Generate Prisma client
echo [INFO] Generating Prisma client...
npx prisma generate
if %errorlevel% neq 0 (
    echo [ERROR] Failed to generate Prisma client
    exit /b 1
)
echo [SUCCESS] Prisma client generated

REM Check database connection (optional, for local builds)
if not "%NODE_ENV%"=="production" (
    echo [INFO] Checking database connection...
    npx prisma db push --accept-data-loss >nul 2>&1
    if %errorlevel% equ 0 (
        echo [SUCCESS] Database schema updated
    ) else (
        echo [WARNING] Database connection failed - continuing with build
    )
)

REM Run linting
echo [INFO] Running ESLint...
npm run lint
if %errorlevel% equ 0 (
    echo [SUCCESS] Linting passed
) else (
    echo [WARNING] Linting issues found - continuing with build
)

REM Run tests (if not in production)
if not "%NODE_ENV%"=="production" (
    echo [INFO] Running tests...
    npm run test:ci
    if %errorlevel% equ 0 (
        echo [SUCCESS] All tests passed
    ) else (
        echo [WARNING] Some tests failed - continuing with build
    )
)

REM Build the application
echo [INFO] Building Next.js application...
npm run build
if %errorlevel% neq 0 (
    echo [ERROR] Build failed
    exit /b 1
)
echo [SUCCESS] Application built successfully

REM Create admin user (for production deployment)
if "%NODE_ENV%"=="production" (
    echo [INFO] Setting up admin user...
    node create-admin.js
    if %errorlevel% equ 0 (
        echo [SUCCESS] Admin user created/verified
    ) else (
        echo [WARNING] Admin user setup failed - may already exist
    )
)

REM Optimize CSS (if script exists)
if exist "scripts\css-optimizer.js" (
    echo [INFO] Optimizing CSS...
    node scripts\css-optimizer.js
    echo [SUCCESS] CSS optimized
)

REM Health check
echo [INFO] Performing build health check...
if exist ".next" (
    if exist ".next\BUILD_ID" (
        echo [SUCCESS] Build completed successfully!
    ) else (
        echo [ERROR] Build verification failed
        exit /b 1
    )
) else (
    echo [ERROR] Build verification failed
    exit /b 1
)

REM Display build summary
echo.
echo 🎉 Build Summary:
echo ==================
echo ✅ Dependencies installed
echo ✅ Prisma client generated
echo ✅ Application built
echo ✅ Build verification passed

if "%NODE_ENV%"=="production" (
    echo ✅ Production setup completed
    echo.
    echo 🚀 Ready for deployment!
) else (
    echo ✅ Development build completed
    echo.
    echo 🏃 Ready to start development server with: npm run dev
)

echo.
echo [SUCCESS] Mzuni Tournaments build process completed successfully!

cd ..
