import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'
import { getTournamentWeekForDate } from '@/lib/tournament-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const scheduleId = parseInt(id)

    if (!scheduleId) {
      return NextResponse.json(
        { error: 'Invalid schedule ID' },
        { status: 400 }
      )
    }

    const schedule = await prisma.tournamentSchedule.findUnique({
      where: { id: scheduleId },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        },
        registrations: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                username: true
              }
            }
          }
        }
      }
    })

    if (!schedule) {
      return NextResponse.json(
        { error: 'Tournament schedule not found' },
        { status: 404 }
      )
    }

    // Add week number to the schedule
    const scheduleWithWeek = {
      ...schedule,
      weekNumber: getTournamentWeekForDate(schedule.scheduledDate),
      year: schedule.scheduledDate.getFullYear()
    }

    return NextResponse.json(scheduleWithWeek)
  } catch (error) {
    logger.error('Error fetching tournament schedule:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tournament schedule' },
      { status: 500 }
    )
  }
}
