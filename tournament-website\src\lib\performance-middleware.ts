/**
 * Performance monitoring middleware for API routes
 */

import { NextRequest, NextResponse } from 'next/server'
import { performanceMonitor } from './performance-monitor'
import { logger } from './logger'

/**
 * Higher-order function to wrap API routes with performance monitoring
 */
export function withPerformanceMonitoring<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse>,
  routeName?: string
) {
  return async (...args: T): Promise<NextResponse> => {
    const startTime = Date.now()
    let success = true
    let statusCode = 200
    
    // Extract request information if available
    const request = args.find(arg => arg instanceof NextRequest) as NextRequest | undefined
    const method = request?.method || 'UNKNOWN'
    const url = request?.url || routeName || 'unknown-route'
    const endpoint = new URL(url).pathname
    
    try {
      const response = await handler(...args)
      statusCode = response.status
      success = statusCode < 400
      
      return response
    } catch (error) {
      success = false
      statusCode = 500
      
      logger.error(`API Error in ${endpoint}`, {
        method,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      })
      
      throw error
    } finally {
      // Track the request performance
      performanceMonitor.trackApiRequest(endpoint, method, startTime, success)
      
      // Log slow requests
      const duration = Date.now() - startTime
      if (duration > 1000) { // Log requests over 1 second
        logger.warn(`Slow API request`, {
          endpoint,
          method,
          duration: `${duration}ms`,
          statusCode
        })
      }
    }
  }
}

/**
 * Database query performance wrapper
 */
export function withDatabaseMonitoring<T extends any[], R>(
  queryFunction: (...args: T) => Promise<R>,
  queryName: string
) {
  return async (...args: T): Promise<R> => {
    const startTime = Date.now()
    let success = true
    
    try {
      const result = await queryFunction(...args)
      return result
    } catch (error) {
      success = false
      throw error
    } finally {
      const duration = Date.now() - startTime
      performanceMonitor.trackDatabaseQuery(queryName, duration, success)
    }
  }
}

/**
 * Middleware for Next.js API routes
 */
export function performanceMiddleware(request: NextRequest) {
  const startTime = Date.now()
  
  // Add performance tracking to the request
  ;(request as any).performanceStartTime = startTime
  
  return NextResponse.next()
}

/**
 * Response interceptor to complete performance tracking
 */
export function interceptResponse(request: NextRequest, response: NextResponse) {
  const startTime = (request as any).performanceStartTime
  if (startTime) {
    const endpoint = new URL(request.url).pathname
    const method = request.method
    const success = response.status < 400
    
    performanceMonitor.trackApiRequest(endpoint, method, startTime, success)
  }
  
  return response
}

/**
 * Health check endpoint data
 */
export function getHealthCheckData() {
  const serverMetrics = performanceMonitor.getServerMetrics()
  const dbMetrics = performanceMonitor.getDatabaseMetrics()
  
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: Math.round(serverMetrics.uptime / 1000),
    memory: {
      used: Math.round(serverMetrics.memoryUsage.heapUsed / 1024 / 1024),
      total: Math.round(serverMetrics.memoryUsage.heapTotal / 1024 / 1024),
      unit: 'MB'
    },
    requests: {
      total: serverMetrics.requestCount,
      errors: serverMetrics.errorCount,
      errorRate: serverMetrics.requestCount > 0 ? 
        ((serverMetrics.errorCount / serverMetrics.requestCount) * 100).toFixed(2) + '%' : '0%'
    },
    database: {
      avgResponseTime: dbMetrics.avgResponseTime.toFixed(2) + 'ms',
      errorRate: (dbMetrics.errorRate * 100).toFixed(2) + '%'
    }
  }
}

/**
 * Performance metrics endpoint data
 */
export function getPerformanceMetrics() {
  const recentMetrics = performanceMonitor.getRecentMetrics(10) // Last 10 minutes
  
  // Group metrics by type
  const apiMetrics = recentMetrics.filter(m => m.name === 'api_response_time')
  const dbMetrics = recentMetrics.filter(m => m.name === 'db_query_time')
  const memoryMetrics = recentMetrics.filter(m => m.name === 'memory_usage')
  
  return {
    timestamp: new Date().toISOString(),
    period: '10 minutes',
    api: {
      totalRequests: apiMetrics.length,
      averageResponseTime: apiMetrics.length > 0 ? 
        (apiMetrics.reduce((sum, m) => sum + m.value, 0) / apiMetrics.length).toFixed(2) + 'ms' : '0ms',
      slowRequests: apiMetrics.filter(m => m.value > 1000).length
    },
    database: {
      totalQueries: dbMetrics.length,
      averageQueryTime: dbMetrics.length > 0 ? 
        (dbMetrics.reduce((sum, m) => sum + m.value, 0) / dbMetrics.length).toFixed(2) + 'ms' : '0ms',
      slowQueries: dbMetrics.filter(m => m.value > 500).length
    },
    memory: {
      current: memoryMetrics.length > 0 ? 
        memoryMetrics[memoryMetrics.length - 1].value.toFixed(2) + 'MB' : 'N/A',
      peak: memoryMetrics.length > 0 ? 
        Math.max(...memoryMetrics.map(m => m.value)).toFixed(2) + 'MB' : 'N/A'
    }
  }
}
