import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const announcements = await prisma.announcement.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(announcements)
  } catch (error) {
    logger.error('Error fetching announcements:', error)
    return NextResponse.json(
      { error: 'Failed to fetch announcements' },
      { status: 500 }
    )
  }
})

export const POST = withAdminAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const { title, content } = body

    if (!title || !content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      )
    }

    const announcement = await prisma.announcement.create({
      data: {
        title,
        content,
        isActive: true
      }
    })

    return NextResponse.json(announcement, { status: 201 })
  } catch (error) {
    logger.error('Error creating announcement:', error)
    return NextResponse.json(
      { error: 'Failed to create announcement' },
      { status: 500 }
    )
  }
})
