# Mzuni Tournaments - Environment Variables Example
# Copy this file to .env and fill in your actual values
# DO NOT commit .env file to version control

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/Gaming"

# =============================================================================
# NEXTAUTH CONFIGURATION
# =============================================================================
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-development-secret-key-here"

# =============================================================================
# APPLICATION ENVIRONMENT
# =============================================================================
NODE_ENV="development"

# =============================================================================
# ADMIN CREDENTIALS
# =============================================================================
ADMIN_USERNAME="Tournaowner"
ADMIN_PASSWORD="your-admin-password"

# =============================================================================
# WHATSAPP INTEGRATION
# =============================================================================
WHATSAPP_NUMBER="+265983132770"

# =============================================================================
# TOURNAMENT CONFIGURATION
# =============================================================================
TOURNAMENT_SEASON_START="2025-01-12"
MAX_PLAYERS_PES="32"
MAX_PLAYERS_PUBG="50"
MAX_PLAYERS_COD="50"
PRIZE_DISTRIBUTION_TOP4="45,25,15,5"
PRIZE_DISTRIBUTION_TOP10="28,20,14,10,8,6,5,4,3,2"
