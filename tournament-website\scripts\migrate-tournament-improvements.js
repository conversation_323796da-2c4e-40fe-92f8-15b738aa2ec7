const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function migrateTournamentImprovements() {
  console.log('🔄 Migrating Tournament System Improvements...\n')

  try {
    // Step 1: Add new columns to tournament_schedules table
    console.log('1️⃣ Adding new columns to tournament_schedules...')
    
    await prisma.$executeRaw`
      ALTER TABLE tournament_schedules 
      ADD COLUMN IF NOT EXISTS tournament_id VARCHAR(20),
      ADD COLUMN IF NOT EXISTS max_participants INTEGER,
      ADD COLUMN IF NOT EXISTS current_participants INTEGER DEFAULT 0,
      ADD COLUMN IF NOT EXISTS registration_deadline TIMESTAMP;
    `
    console.log('✅ Added new columns to tournament_schedules')

    // Step 2: Generate tournament IDs for existing schedules using raw SQL
    console.log('\n2️⃣ Generating tournament IDs for existing schedules...')

    const existingSchedules = await prisma.$queryRaw`
      SELECT id, scheduled_date FROM tournament_schedules ORDER BY created_at ASC
    `

    const currentYear = new Date().getFullYear()

    for (let i = 0; i < existingSchedules.length; i++) {
      const schedule = existingSchedules[i]
      const tournamentId = `T${currentYear}${String(i + 1).padStart(3, '0')}`
      const registrationDeadline = new Date(schedule.scheduled_date.getTime() - 24 * 60 * 60 * 1000)

      await prisma.$executeRaw`
        UPDATE tournament_schedules
        SET tournament_id = ${tournamentId},
            max_participants = 50,
            current_participants = 0,
            registration_deadline = ${registrationDeadline}
        WHERE id = ${schedule.id}
      `

      console.log(`   ✅ Updated schedule ${schedule.id} with tournament ID: ${tournamentId}`)
    }

    // Step 3: Make tournament_id unique and required
    console.log('\n3️⃣ Making tournament_id unique and required...')
    
    await prisma.$executeRaw`
      ALTER TABLE tournament_schedules 
      ALTER COLUMN tournament_id SET NOT NULL;
    `
    
    await prisma.$executeRaw`
      ALTER TABLE tournament_schedules 
      ADD CONSTRAINT tournament_schedules_tournament_id_key UNIQUE (tournament_id);
    `
    console.log('✅ Made tournament_id unique and required')

    // Step 4: Add tournament_schedule_id to player_registrations
    console.log('\n4️⃣ Adding tournament_schedule_id to player_registrations...')
    
    await prisma.$executeRaw`
      ALTER TABLE player_registrations 
      ADD COLUMN IF NOT EXISTS tournament_schedule_id INTEGER;
    `
    
    await prisma.$executeRaw`
      ALTER TABLE player_registrations 
      ADD CONSTRAINT fk_tournament_schedule 
      FOREIGN KEY (tournament_schedule_id) 
      REFERENCES tournament_schedules(id) 
      ON DELETE SET NULL;
    `
    console.log('✅ Added tournament_schedule_id to player_registrations')

    // Step 5: Update existing registrations to link to current tournaments (if any)
    console.log('\n5️⃣ Linking existing registrations to tournaments...')
    
    const registrations = await prisma.playerRegistration.findMany({
      include: { game: true }
    })

    for (const registration of registrations) {
      // Find the most recent scheduled tournament for this game
      const recentTournament = await prisma.tournamentSchedule.findFirst({
        where: {
          gameId: registration.gameId,
          status: 'SCHEDULED'
        },
        orderBy: { scheduledDate: 'asc' }
      })

      if (recentTournament) {
        await prisma.playerRegistration.update({
          where: { id: registration.id },
          data: { tournamentScheduleId: recentTournament.id }
        })
        
        // Update participant count
        await prisma.tournamentSchedule.update({
          where: { id: recentTournament.id },
          data: { 
            currentParticipants: { increment: 1 }
          }
        })
        
        console.log(`   ✅ Linked registration ${registration.id} to tournament ${recentTournament.tournamentId}`)
      }
    }

    // Step 6: Verify the migration
    console.log('\n6️⃣ Verifying migration...')
    
    const updatedSchedules = await prisma.tournamentSchedule.findMany({
      include: {
        game: { select: { name: true } },
        registrations: { select: { id: true } }
      }
    })

    console.log('\n📊 Updated Tournament Schedules:')
    updatedSchedules.forEach(schedule => {
      console.log(`   ${schedule.tournamentId}: ${schedule.game.name} - ${schedule.scheduledDate.toDateString()}`)
      console.log(`      Participants: ${schedule.currentParticipants}/${schedule.maxParticipants || '∞'}`)
      console.log(`      Registrations: ${schedule.registrations.length}`)
    })

    console.log('\n🎉 Tournament system improvements migration completed successfully!')
    console.log('\nNew Features Available:')
    console.log('✅ Tournament IDs (********, ********, etc.)')
    console.log('✅ Participant tracking and limits')
    console.log('✅ Registration deadlines')
    console.log('✅ Tournament-specific registrations')

  } catch (error) {
    console.error('❌ Error during migration:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the migration
migrateTournamentImprovements()
