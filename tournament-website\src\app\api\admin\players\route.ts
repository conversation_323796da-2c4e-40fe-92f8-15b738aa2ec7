import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const players = await prisma.user.findMany({
      where: {
        role: 'PLAYER'
      },
      include: {
        registrations: {
          include: {
            game: true
          },
          orderBy: {
            registrationDate: 'desc'
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(players)
  } catch (error) {
    logger.error('Error fetching players:', error)
    return NextResponse.json(
      { error: 'Failed to fetch players' },
      { status: 500 }
    )
  }
})
