# Mzuni Tournaments - Render Environment Variables Template
# Copy these variables to your Render service environment variables
# DO NOT commit actual values to version control

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# This will be automatically set by Render when you connect a PostgreSQL database
DATABASE_URL="postgresql://username:password@host:port/database_name"

# =============================================================================
# NEXTAUTH CONFIGURATION
# =============================================================================
# This will be automatically set by Render based on your service URL
NEXTAUTH_URL="https://your-service-name.onrender.com"

# Generate a secure random string for production (32+ characters recommended)
# You can generate one at: https://generate-secret.vercel.app/32
NEXTAUTH_SECRET="your-super-secure-nextauth-secret-key-here-32-chars-minimum"

# =============================================================================
# APPLICATION ENVIRONMENT
# =============================================================================
NODE_ENV="production"

# =============================================================================
# ADMIN CREDENTIALS
# =============================================================================
# Set these in Render dashboard (mark as sensitive)
ADMIN_USERNAME="your-admin-username-here"
ADMIN_PASSWORD="your-secure-admin-password-here"

# =============================================================================
# WHATSAPP INTEGRATION
# =============================================================================
WHATSAPP_NUMBER="+265XXXXXXXXX"

# =============================================================================
# TOURNAMENT CONFIGURATION
# =============================================================================
# Tournament season start date (YYYY-MM-DD format)
TOURNAMENT_SEASON_START="2025-01-12"

# Maximum players per game
MAX_PLAYERS_PES="32"
MAX_PLAYERS_PUBG="50"
MAX_PLAYERS_COD="50"

# Prize distribution percentages (comma-separated)
PRIZE_DISTRIBUTION_TOP4="45,25,15,5"
PRIZE_DISTRIBUTION_TOP10="28,20,14,10,8,6,5,4,3,2"

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================
# Port (Render will set this automatically)
PORT="3000"

# Logging level
LOG_LEVEL="info"

# Session timeout (in seconds)
SESSION_TIMEOUT="86400"

# Rate limiting
RATE_LIMIT_MAX="100"
RATE_LIMIT_WINDOW="900000"

# =============================================================================
# RENDER-SPECIFIC SETTINGS
# =============================================================================
# Build command optimization
SKIP_ENV_VALIDATION="true"

# Disable telemetry for faster builds
NEXT_TELEMETRY_DISABLED="1"

# =============================================================================
# INSTRUCTIONS FOR RENDER DEPLOYMENT
# =============================================================================
# 1. Go to your Render dashboard
# 2. Select your web service
# 3. Go to Environment tab
# 4. Add each variable above with appropriate values
# 5. Mark sensitive variables (passwords, secrets) as "Secret"
# 6. DATABASE_URL and NEXTAUTH_URL will be auto-populated by Render
# 7. Generate a secure NEXTAUTH_SECRET using a password generator
# 8. Set ADMIN_PASSWORD to a strong password
# 9. Deploy your service

# =============================================================================
# SECURITY NOTES
# =============================================================================
# - Never commit actual passwords or secrets to version control
# - Use Render's "Secret" option for sensitive environment variables
# - Regularly rotate secrets and passwords
# - Monitor your application logs for any exposed credentials
# - Use strong, unique passwords for admin accounts
