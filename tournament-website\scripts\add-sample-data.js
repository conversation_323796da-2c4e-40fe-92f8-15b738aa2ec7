require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  console.log('Adding sample data...')

  try {
    // Add sample users
    const users = await Promise.all([
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          username: 'john_doe',
          email: '<EMAIL>',
          firstName: '<PERSON>',
          lastName: 'Doe',
          phoneNumber: '+265991234567',
          studentId: 'MZUNI2021001',
          role: 'PLAYER'
        }
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          username: 'jane_smith',
          email: '<EMAIL>',
          firstName: '<PERSON>',
          lastName: '<PERSON>',
          phoneNumber: '+265991234568',
          studentId: 'MZUNI2021002',
          role: 'PLAYER'
        }
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          username: 'mike_wilson',
          email: '<EMAIL>',
          firstName: 'Mike',
          lastName: 'Wilson',
          phoneNumber: '+265991234569',
          studentId: 'MZUNI2021003',
          role: 'PLAYER'
        }
      }),
      prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          username: 'admin',
          email: '<EMAIL>',
          firstName: 'Tournament',
          lastName: 'Admin',
          phoneNumber: '+265991234560',
          studentId: 'ADMIN001',
          role: 'ADMIN'
        }
      })
    ])

    console.log('Users created:', users.length)

    // Get games
    const games = await prisma.game.findMany()
    console.log('Games found:', games.length)

    // Add player registrations
    for (const user of users.slice(0, 3)) { // Skip admin
      for (const game of games) {
        await prisma.playerRegistration.upsert({
          where: {
            userId_gameId: {
              userId: user.id,
              gameId: game.id
            }
          },
          update: {},
          create: {
            userId: user.id,
            gameId: game.id,
            gameUsername: `${user.username}_${game.name.toLowerCase().replace(/\s+/g, '')}`
          }
        })

        // Create player stats
        await prisma.playerStats.upsert({
          where: {
            userId_gameId: {
              userId: user.id,
              gameId: game.id
            }
          },
          update: {},
          create: {
            userId: user.id,
            gameId: game.id,
            tournamentsParticipated: Math.floor(Math.random() * 10) + 1,
            tournamentsWon: Math.floor(Math.random() * 3),
            totalWins: Math.floor(Math.random() * 15) + 1,
            totalLosses: Math.floor(Math.random() * 10),
            winPercentage: Math.random() * 100
          }
        })
      }
    }

    console.log('Player registrations and stats created')

    // Add tournament schedules for upcoming weekends
    const today = new Date()
    const nextSaturday = new Date(today)
    nextSaturday.setDate(today.getDate() + (6 - today.getDay()))
    
    const nextSunday = new Date(nextSaturday)
    nextSunday.setDate(nextSaturday.getDate() + 1)

    for (const game of games) {
      // Check if Saturday tournament already exists
      const existingSaturday = await prisma.tournamentSchedule.findFirst({
        where: {
          gameId: game.id,
          scheduledDate: nextSaturday
        }
      })

      if (!existingSaturday) {
        await prisma.tournamentSchedule.create({
          data: {
            gameId: game.id,
            scheduledDate: nextSaturday,
            scheduledTime: new Date('1970-01-01T14:00:00'),
            description: `Weekend ${game.name} tournament - Saturday session`,
            status: 'SCHEDULED'
          }
        })
      }

      // Check if Sunday tournament already exists
      const existingSunday = await prisma.tournamentSchedule.findFirst({
        where: {
          gameId: game.id,
          scheduledDate: nextSunday
        }
      })

      if (!existingSunday) {
        await prisma.tournamentSchedule.create({
          data: {
            gameId: game.id,
            scheduledDate: nextSunday,
            scheduledTime: new Date('1970-01-01T15:00:00'),
            description: `Weekend ${game.name} tournament - Sunday session`,
            status: 'SCHEDULED'
          }
        })
      }
    }

    console.log('Tournament schedules created')

    // Add some past weekly tournaments with winners
    const currentYear = new Date().getFullYear()
    const currentWeek = Math.floor((Date.now() - new Date(currentYear, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1

    for (let week = Math.max(1, currentWeek - 4); week < currentWeek; week++) {
      for (const game of games) {
        const randomWinner = users[Math.floor(Math.random() * 3)] // Random winner from first 3 users
        
        const tournament = await prisma.weeklyTournament.create({
          data: {
            gameId: game.id,
            weekNumber: week,
            year: currentYear,
            tournamentDate: new Date(currentYear, 0, week * 7),
            totalParticipants: Math.floor(Math.random() * 10) + 5,
            winnerId: randomWinner.id,
            status: 'COMPLETED'
          }
        })

        // Add weekly winner record
        await prisma.weeklyWinner.create({
          data: {
            userId: randomWinner.id,
            gameId: game.id,
            weekNumber: week,
            year: currentYear,
            tournamentId: tournament.id
          }
        })

        // Update winner's stats
        await prisma.playerStats.update({
          where: {
            userId_gameId: {
              userId: randomWinner.id,
              gameId: game.id
            }
          },
          data: {
            tournamentsWon: { increment: 1 },
            totalWins: { increment: 1 },
            lastUpdated: new Date()
          }
        })
      }
    }

    console.log('Past tournaments and winners created')

    // Update win percentages
    const allStats = await prisma.playerStats.findMany()
    for (const stat of allStats) {
      const totalGames = stat.totalWins + stat.totalLosses
      const winPercentage = totalGames > 0 ? (stat.totalWins / totalGames) * 100 : 0
      
      await prisma.playerStats.update({
        where: { id: stat.id },
        data: { winPercentage }
      })
    }

    console.log('Win percentages updated')
    console.log('Sample data added successfully!')

  } catch (error) {
    console.error('Error adding sample data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
