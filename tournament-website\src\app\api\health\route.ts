import { NextResponse } from 'next/server'
import { getHealthCheckData } from '@/lib/performance-middleware'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'

export async function GET() {
  const startTime = Date.now()

  try {
    // Test database connection with timeout
    const dbPromise = prisma.$queryRaw`SELECT 1`
    const dbTimeout = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Database timeout')), 5000)
    )

    await Promise.race([dbPromise, dbTimeout])

    const healthData = getHealthCheckData()
    const responseTime = Date.now() - startTime

    // Render-specific health check response
    const healthResponse = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      responseTime: `${responseTime}ms`,
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',

      // Core services
      services: {
        database: 'healthy',
        api: 'healthy',
        monitoring: 'active'
      },

      // Database health
      database: {
        ...healthData.database,
        connected: true,
        responseTime: `${responseTime}ms`
      },

      // System metrics
      system: {
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          unit: 'MB'
        },
        cpu: process.cpuUsage(),
        platform: process.platform,
        nodeVersion: process.version
      },

      // Render-specific checks
      render: {
        port: process.env.PORT || '3000',
        hostname: process.env.HOSTNAME || 'localhost',
        region: process.env.RENDER_REGION || 'unknown',
        service: process.env.RENDER_SERVICE_NAME || 'mzuni-tournaments'
      },

      // Application health
      application: {
        ...healthData,
        ready: true,
        healthy: true
      }
    }

    return NextResponse.json(healthResponse, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })

  } catch (error) {
    const responseTime = Date.now() - startTime
    logger.error('Health check failed', error)

    const errorResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      responseTime: `${responseTime}ms`,
      environment: process.env.NODE_ENV || 'development',
      error: error instanceof Error ? error.message : 'Unknown error',

      services: {
        database: 'unhealthy',
        api: 'degraded',
        monitoring: 'active'
      },

      database: {
        connected: false,
        error: error instanceof Error ? error.message : 'Connection failed'
      },

      system: {
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          unit: 'MB'
        },
        platform: process.platform,
        nodeVersion: process.version
      },

      render: {
        port: process.env.PORT || '3000',
        hostname: process.env.HOSTNAME || 'localhost',
        region: process.env.RENDER_REGION || 'unknown',
        service: process.env.RENDER_SERVICE_NAME || 'mzuni-tournaments'
      }
    }

    return NextResponse.json(errorResponse, {
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}
