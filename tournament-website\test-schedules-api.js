#!/usr/bin/env node

const http = require('http')

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    http.get(url, (res) => {
      let data = ''
      res.on('data', (chunk) => data += chunk)
      res.on('end', () => {
        try {
          resolve({ status: res.statusCode, data: JSON.parse(data) })
        } catch (e) {
          resolve({ status: res.statusCode, data: data })
        }
      })
    }).on('error', reject)
  })
}

async function testSchedulesAPI() {
  console.log('🧪 Testing Schedules API')
  console.log('========================\n')

  try {
    // Test public schedules API
    console.log('1. Testing public schedules API...')
    const schedulesResponse = await makeRequest('http://localhost:3000/api/schedules')
    console.log(`   Status: ${schedulesResponse.status}`)
    
    if (schedulesResponse.status === 200) {
      console.log('   ✅ API working')
      console.log(`   Schedules found: ${Array.isArray(schedulesResponse.data) ? schedulesResponse.data.length : 'Invalid format'}`)
      
      if (Array.isArray(schedulesResponse.data)) {
        if (schedulesResponse.data.length === 0) {
          console.log('   📝 No schedules in database')
        } else {
          console.log('   📅 Available schedules:')
          schedulesResponse.data.forEach((schedule, index) => {
            console.log(`      ${index + 1}. ${schedule.game?.name || 'Unknown Game'} - ${schedule.scheduledDate} at ${schedule.scheduledTime}`)
          })
        }
      }
    } else {
      console.log('   ❌ API failed')
      console.log(`   Error: ${JSON.stringify(schedulesResponse.data)}`)
    }

    // Test admin schedules API
    console.log('\n2. Testing admin schedules API...')
    const adminSchedulesResponse = await makeRequest('http://localhost:3000/api/admin/schedules')
    console.log(`   Status: ${adminSchedulesResponse.status}`)
    
    if (adminSchedulesResponse.status === 401) {
      console.log('   ✅ Correctly requires authentication')
    } else if (adminSchedulesResponse.status === 200) {
      console.log('   ✅ API working (somehow authenticated)')
      console.log(`   Admin schedules: ${Array.isArray(adminSchedulesResponse.data) ? adminSchedulesResponse.data.length : 'Invalid format'}`)
    } else {
      console.log('   ❌ Unexpected response')
      console.log(`   Error: ${JSON.stringify(adminSchedulesResponse.data)}`)
    }

    // Test games API for comparison
    console.log('\n3. Testing games API for comparison...')
    const gamesResponse = await makeRequest('http://localhost:3000/api/games')
    console.log(`   Status: ${gamesResponse.status}`)
    
    if (gamesResponse.status === 200) {
      console.log('   ✅ Games API working')
      console.log(`   Games found: ${Array.isArray(gamesResponse.data) ? gamesResponse.data.length : 'Invalid format'}`)
      
      if (Array.isArray(gamesResponse.data)) {
        gamesResponse.data.forEach(game => {
          console.log(`      - ${game.name}: ${game._count?.registrations || 0} registrations`)
        })
      }
    }

    console.log('\n📊 Diagnosis:')
    console.log('-------------')
    if (schedulesResponse.status === 200 && Array.isArray(schedulesResponse.data)) {
      if (schedulesResponse.data.length === 0) {
        console.log('✅ API is working correctly')
        console.log('📝 Issue: No schedules exist in database')
        console.log('💡 Solution: Admin needs to create tournament schedules')
        console.log('🔗 Admin can add schedules at: http://localhost:3000/admin/schedules')
      } else {
        console.log('✅ API is working and has data')
        console.log('🤔 Issue might be in the frontend schedule page')
      }
    } else {
      console.log('❌ API has issues that need to be fixed')
    }

  } catch (error) {
    console.error('❌ Error testing schedules API:', error.message)
  }
}

testSchedulesAPI()
