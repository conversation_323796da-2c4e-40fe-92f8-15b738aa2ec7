@echo off
REM Mzuni Tournaments PM2 Management Script for Windows
REM Usage: scripts\pm2-manager.bat [command]

setlocal enabledelayedexpansion

set APP_NAME=mzuni-tournaments
set ECOSYSTEM_FILE=ecosystem.config.js

REM Check if PM2 is installed
where pm2 >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] PM2 is not installed. Installing PM2...
    npm install -g pm2
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install PM2
        exit /b 1
    )
    echo [SUCCESS] PM2 installed successfully
)

REM Create logs directory
if not exist "logs" (
    mkdir logs
    echo [INFO] Created logs directory
)

REM Parse command line argument
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=help

if "%COMMAND%"=="start-prod" goto start_production
if "%COMMAND%"=="start-dev" goto start_development
if "%COMMAND%"=="stop" goto stop_app
if "%COMMAND%"=="restart" goto restart_app
if "%COMMAND%"=="reload" goto reload_app
if "%COMMAND%"=="delete" goto delete_app
if "%COMMAND%"=="status" goto status_app
if "%COMMAND%"=="logs" goto logs_app
if "%COMMAND%"=="monitor" goto monitor_app
if "%COMMAND%"=="setup-startup" goto setup_startup
if "%COMMAND%"=="health" goto health_check
if "%COMMAND%"=="help" goto show_help
goto show_help

:start_production
echo [INFO] Starting %APP_NAME% in production mode...
echo [INFO] Building application...
call npm run build
if %errorlevel% neq 0 (
    echo [ERROR] Build failed
    exit /b 1
)
call pm2 start %ECOSYSTEM_FILE% --env production
call pm2 save
echo [SUCCESS] %APP_NAME% started in production mode
call pm2 status
goto end

:start_development
echo [INFO] Starting %APP_NAME% in development mode...
call pm2 start %ECOSYSTEM_FILE% --env development
echo [SUCCESS] %APP_NAME% started in development mode
call pm2 status
goto end

:stop_app
echo [INFO] Stopping %APP_NAME%...
call pm2 stop %APP_NAME%
echo [SUCCESS] %APP_NAME% stopped
goto end

:restart_app
echo [INFO] Restarting %APP_NAME%...
call pm2 restart %APP_NAME%
echo [SUCCESS] %APP_NAME% restarted
goto end

:reload_app
echo [INFO] Reloading %APP_NAME% (zero-downtime)...
call pm2 reload %APP_NAME%
echo [SUCCESS] %APP_NAME% reloaded
goto end

:delete_app
echo [WARNING] Deleting %APP_NAME% from PM2...
call pm2 delete %APP_NAME%
echo [SUCCESS] %APP_NAME% deleted from PM2
goto end

:status_app
call pm2 status %APP_NAME%
goto end

:logs_app
call pm2 logs %APP_NAME% --lines 50
goto end

:monitor_app
call pm2 monit
goto end

:setup_startup
echo [INFO] Setting up PM2 startup script...
call pm2 startup
echo [WARNING] Please run the command shown above as administrator
goto end

:health_check
echo [INFO] Performing health check...

REM Check if app is running
call pm2 list | findstr /C:"%APP_NAME%" | findstr /C:"online" >nul
if %errorlevel% equ 0 (
    echo [SUCCESS] Application is running
    
    REM Check HTTP endpoint (using curl if available, otherwise skip)
    where curl >nul 2>nul
    if %errorlevel% equ 0 (
        curl -f http://localhost:3002/api/health >nul 2>nul
        if %errorlevel% equ 0 (
            echo [SUCCESS] Health endpoint is responding
        ) else (
            echo [ERROR] Health endpoint is not responding
            exit /b 1
        )
    ) else (
        echo [INFO] curl not available, skipping HTTP health check
    )
) else (
    echo [ERROR] Application is not running
    exit /b 1
)
goto end

:show_help
echo Mzuni Tournaments PM2 Management Script for Windows
echo.
echo Usage: %0 [command]
echo.
echo Commands:
echo   start-prod     Start application in production mode
echo   start-dev      Start application in development mode
echo   stop           Stop application
echo   restart        Restart application
echo   reload         Reload application (zero-downtime)
echo   delete         Delete application from PM2
echo   status         Show application status
echo   logs           Show application logs
echo   monitor        Open PM2 monitor
echo   setup-startup  Setup PM2 startup script
echo   health         Perform health check
echo   help           Show this help message
echo.
goto end

:end
endlocal
