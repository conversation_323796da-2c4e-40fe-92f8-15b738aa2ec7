'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { logger } from '@/lib/logger'

interface User {
  id: number
  username: string
  firstName: string
  lastName: string
  phoneNumber: string
  email?: string
}

interface Game {
  id: number
  name: string
  description: string
}

export default function TournamentRegisterPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const preselectedGame = searchParams.get('game')
  const scheduleId = searchParams.get('scheduleId')
  const weekNumber = searchParams.get('week')
  const [user, setUser] = useState<User | null>(null)
  const [games, setGames] = useState<Game[]>([])
  const [tournamentSchedule, setTournamentSchedule] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [message, setMessage] = useState('')
  const [showWhatsAppButton, setShowWhatsAppButton] = useState(false)
  const [formData, setFormData] = useState({
    gameId: '',
    gameUsername: ''
  })

  const fetchTournamentSchedule = async () => {
    if (!scheduleId) return

    try {
      const response = await fetch(`/api/schedules/${scheduleId}`)
      if (response.ok) {
        const schedule = await response.json()
        setTournamentSchedule(schedule)
        // Auto-select the game for this tournament
        setFormData(prev => ({ ...prev, gameId: schedule.gameId.toString() }))
      }
    } catch (error) {
      logger.error('Error fetching tournament schedule:', error)
    }
  }

  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
      } else {
        router.push('/login')
      }
    } catch (error) {
      logger.error('Auth check failed:', error)
      router.push('/login')
    } finally {
      setLoading(false)
    }
  }

  const fetchGames = async () => {
    try {
      const response = await fetch('/api/games')
      if (response.ok) {
        const data = await response.json()
        setGames(data)
      }
    } catch (error) {
      logger.error('Error fetching games:', error)
    }
  }

  useEffect(() => {
    checkAuth()
    fetchGames()
    if (scheduleId) {
      fetchTournamentSchedule()
    } else {
      // Redirect to schedule page if no tournament specified
      router.push('/schedule')
    }
  }, [])

  useEffect(() => {
    if (preselectedGame && games.length > 0) {
      setFormData(prev => ({ ...prev, gameId: preselectedGame }))
    }
  }, [preselectedGame, games])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    setMessage('')

    // Validate required fields
    if (!formData.gameId || (!isPESSelected && !formData.gameUsername)) {
      setMessage('Please fill in all required fields.')
      setSubmitting(false)
      return
    }

    try {
      // If registering for a specific tournament, use tournament registration endpoint
      if (scheduleId && user) {
        const tournamentResponse = await fetch('/api/tournament-registration', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            tournamentScheduleId: parseInt(scheduleId),
            gameUsername: formData.gameUsername
          })
        })

        if (tournamentResponse.ok) {
          const result = await tournamentResponse.json()
          setMessage(`✅ Successfully registered for ${tournamentSchedule?.game?.name} Tournament Week ${weekNumber}! ${result.message}`)
          setShowWhatsAppButton(true)
        } else {
          const error = await tournamentResponse.json()
          setMessage(error.error || 'Tournament registration failed. Please try again.')
        }
      } else {
        // Fallback to general game registration
        const registrationResponse = await fetch('/api/registrations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            gameId: parseInt(formData.gameId),
            gameUsername: formData.gameUsername
          })
        })

        if (registrationResponse.ok) {
          setMessage('✅ Registration successful! Details will be sent to our WhatsApp for confirmation.')
          setShowWhatsAppButton(true)
        } else {
          const error = await registrationResponse.json()
          setMessage(error.error || 'Registration failed. Please try again.')
        }
      }

    } catch (error) {
      logger.error('Error submitting registration:', error)
      setMessage('Registration failed. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  // Get selected game name to check if it's PES
  const selectedGame = games.find(game => game.id.toString() === formData.gameId)
  const isPESSelected = selectedGame?.name === 'PES'

  // Generate WhatsApp message with user details
  const generateWhatsAppMessage = () => {
    if (!user) return ''
    
    const selectedGameName = selectedGame?.name || 'Unknown Game'
    const message = `New Tournament Registration:

Name: ${user.firstName} ${user.lastName}
Username: ${user.username}
Phone: ${user.phoneNumber}
Game: ${selectedGameName}${!isPESSelected ? `
In-Game Username: ${formData.gameUsername}` : ''}

Please confirm my registration for the weekend tournament.`

    return encodeURIComponent(message)
  }

  const whatsappNumber = "+265983132770"

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="text-3xl font-bold text-blue-600">eSports RXP</Link>
            </div>
            <nav className="flex space-x-8 items-center">
              <Link href="/" className="text-gray-500 hover:text-gray-900 flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span>Home</span>
              </Link>
              <Link href="/profile" className="text-gray-500 hover:text-gray-900 flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>Profile</span>
              </Link>
               
              <Link href="/schedule" className="text-gray-500 hover:text-gray-900 flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span>Schedule</span>
              </Link>
              <span className="text-blue-600 font-medium flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 013.138-3.138z" />
                </svg>
                <span>Register</span>
              </span>
              <span className="text-gray-700 flex items-center space-x-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>{user.firstName} {user.lastName}</span>
              </span>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              {scheduleId && tournamentSchedule ?
                `${tournamentSchedule.game?.name} Tournament Registration` :
                'Tournament Registration'
              }
            </h1>
            <p className="mt-2 text-gray-600">
              {scheduleId && weekNumber ?
                `Register for Week ${weekNumber} tournament` :
                'Register for weekend gaming tournaments'
              }
            </p>

            {/* Tournament-specific information */}
            {tournamentSchedule && (
              <div className="mt-4 bg-purple-50 border border-purple-200 rounded-md p-4">
                <h3 className="text-lg font-semibold text-purple-900 mb-2">Tournament Details</h3>
                <div className="text-sm text-purple-800 space-y-1">
                  <p><strong>Game:</strong> {tournamentSchedule.game?.name}</p>
                  <p><strong>Week:</strong> {weekNumber}</p>
                  <p><strong>Date:</strong> {new Date(tournamentSchedule.scheduledDate).toLocaleDateString('en-US', {
                    weekday: 'long', month: 'long', day: 'numeric', year: 'numeric'
                  })}</p>
                  <p><strong>Time:</strong> {new Date(tournamentSchedule.scheduledTime).toLocaleTimeString('en-US', {
                    hour: 'numeric', minute: '2-digit', hour12: true
                  })}</p>
                  <p><strong>Participants:</strong> {tournamentSchedule.currentParticipants || 0} / {tournamentSchedule.maxParticipants || 'Unlimited'}</p>
                  {tournamentSchedule.registrationDeadline && (
                    <p><strong>Registration Deadline:</strong> {new Date(tournamentSchedule.registrationDeadline).toLocaleDateString()}</p>
                  )}
                </div>
              </div>
            )}

            <div className="mt-4 bg-blue-50 border border-blue-200 rounded-md p-4">
              <p className="text-sm text-blue-800">
                <strong>Registering as:</strong> {user.firstName} {user.lastName} ({user.username})
              </p>
              <p className="text-sm text-blue-600">
                Phone: {user.phoneNumber}
              </p>
            </div>
          </div>

          {message && (
            <div className={`mb-6 p-4 rounded-md ${
              message.includes('successful') 
                ? 'bg-green-50 text-green-800 border border-green-200' 
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {message}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Game Selection - Hidden when coming from specific tournament */}
            {scheduleId && tournamentSchedule ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tournament Game
                </label>
                <div className="w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-50">
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {tournamentSchedule.game?.name}
                    </span>
                    <span className="text-sm text-gray-600">
                      (Locked for this tournament)
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <label htmlFor="gameId" className="block text-sm font-medium text-gray-700 mb-2">
                  Select Game *
                </label>
                <select
                  id="gameId"
                  name="gameId"
                  required
                  value={formData.gameId}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Choose a game...</option>
                  {games.map((game) => (
                    <option key={game.id} value={game.id.toString()}>
                      {game.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {!isPESSelected && (
              <div>
                <label htmlFor="gameUsername" className="block text-sm font-medium text-gray-700 mb-2">
                  In-Game Username *
                </label>
                <input
                  type="text"
                  id="gameUsername"
                  name="gameUsername"
                  required={!isPESSelected}
                  value={formData.gameUsername}
                  onChange={handleChange}
                  placeholder="Your username in the selected game"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Enter your exact username as it appears in the game
                </p>
              </div>
            )}

            <div className="pt-6">
              {!showWhatsAppButton ? (
                <button
                  type="submit"
                  disabled={submitting}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-md font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Registering...' : 'Register for Tournament'}
                </button>
              ) : (
                <a
                  href={`https://wa.me/${whatsappNumber.replace('+', '')}?text=${generateWhatsAppMessage()}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center w-full bg-green-600 text-white py-3 px-6 rounded-md font-semibold hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
                >
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                  </svg>
                  Send to WhatsApp (+265983132770)
                </a>
              )}
            </div>
          </form>

          <div className="mt-6 text-center">
            <Link href="/profile" className="text-gray-500 hover:text-gray-700">
              ← Back to Profile
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
