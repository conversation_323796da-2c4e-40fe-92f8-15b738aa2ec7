/**
 * CSS Performance Monitoring Utility
 * Tracks CSS loading performance and provides optimization insights
 */

interface CSSMetrics {
  loadTime: number
  renderTime: number
  criticalCSS: boolean
  totalStylesheets: number
  inlineStyles: number
}

class CSSPerformanceMonitor {
  private metrics: CSSMetrics = {
    loadTime: 0,
    renderTime: 0,
    criticalCSS: false,
    totalStylesheets: 0,
    inlineStyles: 0,
  }

  /**
   * Initialize CSS performance monitoring
   */
  init() {
    if (typeof window === 'undefined') return

    // Monitor when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.measureCSS())
    } else {
      this.measureCSS()
    }

    // Monitor when all resources are loaded
    window.addEventListener('load', () => this.measureRenderTime())
  }

  /**
   * Measure CSS loading metrics
   */
  private measureCSS() {
    if (typeof window === 'undefined') return

    const startTime = performance.now()

    // Count stylesheets
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
    this.metrics.totalStylesheets = stylesheets.length

    // Count inline styles
    const inlineStyles = document.querySelectorAll('style')
    this.metrics.inlineStyles = inlineStyles.length

    // Check for critical CSS (inline styles in head)
    const headStyles = document.head.querySelectorAll('style')
    this.metrics.criticalCSS = headStyles.length > 0

    // Measure CSS load time
    Promise.all(
      Array.from(stylesheets).map(
        (link) =>
          new Promise((resolve) => {
            if ((link as HTMLLinkElement).sheet) {
              resolve(true)
            } else {
              link.addEventListener('load', () => resolve(true))
              link.addEventListener('error', () => resolve(false))
            }
          })
      )
    ).then(() => {
      this.metrics.loadTime = performance.now() - startTime
      this.reportMetrics()
    })
  }

  /**
   * Measure render time after CSS is applied
   */
  private measureRenderTime() {
    if (typeof window === 'undefined') return

    // Use paint timing API if available
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const fcp = entries.find((entry) => entry.name === 'first-contentful-paint')
        
        if (fcp) {
          this.metrics.renderTime = fcp.startTime
          observer.disconnect()
        }
      })

      observer.observe({ entryTypes: ['paint'] })
    }
  }

  /**
   * Get current CSS metrics
   */
  getMetrics(): CSSMetrics {
    return { ...this.metrics }
  }

  /**
   * Report metrics to console in development
   */
  private reportMetrics() {
    if (process.env.NODE_ENV === 'development') {
      console.group('🎨 CSS Performance Metrics')
      console.log('Load Time:', `${this.metrics.loadTime.toFixed(2)}ms`)
      console.log('Render Time:', `${this.metrics.renderTime.toFixed(2)}ms`)
      console.log('Total Stylesheets:', this.metrics.totalStylesheets)
      console.log('Inline Styles:', this.metrics.inlineStyles)
      console.log('Critical CSS:', this.metrics.criticalCSS ? '✅' : '❌')
      
      // Performance recommendations
      this.provideRecommendations()
      console.groupEnd()
    }
  }

  /**
   * Provide performance recommendations
   */
  private provideRecommendations() {
    const recommendations: string[] = []

    if (this.metrics.loadTime > 100) {
      recommendations.push('CSS load time is high (>100ms). Consider reducing CSS bundle size.')
    }

    if (this.metrics.totalStylesheets > 3) {
      recommendations.push('Multiple stylesheets detected. Consider bundling CSS files.')
    }

    if (this.metrics.inlineStyles > 5) {
      recommendations.push('Many inline styles detected. Consider moving to CSS classes.')
    }

    if (!this.metrics.criticalCSS) {
      recommendations.push('No critical CSS detected. Consider inlining critical styles.')
    }

    if (recommendations.length > 0) {
      console.group('💡 Recommendations')
      recommendations.forEach((rec) => console.log(`• ${rec}`))
      console.groupEnd()
    } else {
      console.log('✅ CSS performance looks good!')
    }
  }

  /**
   * Check for CSS-related Core Web Vitals issues
   */
  checkCoreWebVitals() {
    if (typeof window === 'undefined') return

    // Monitor Cumulative Layout Shift (CLS)
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        
        entries.forEach((entry: any) => {
          if (entry.hadRecentInput) return // Ignore user-initiated shifts
          
          if (entry.value > 0.1) {
            console.warn('🚨 High Cumulative Layout Shift detected:', entry.value)
            console.log('This might be caused by:')
            console.log('• Images without dimensions')
            console.log('• Web fonts causing FOIT/FOUT')
            console.log('• Dynamic content insertion')
          }
        })
      })

      observer.observe({ entryTypes: ['layout-shift'] })
    }
  }

  /**
   * Monitor font loading performance
   */
  monitorFontLoading() {
    if (typeof window === 'undefined' || !('fonts' in document)) return

    document.fonts.ready.then(() => {
      const loadedFonts = Array.from(document.fonts.values())
      
      if (process.env.NODE_ENV === 'development') {
        console.group('🔤 Font Loading Metrics')
        console.log('Loaded Fonts:', loadedFonts.length)
        
        loadedFonts.forEach((font: any) => {
          console.log(`• ${font.family} (${font.weight})`)
        })
        
        console.groupEnd()
      }
    })
  }

  /**
   * Analyze CSS bundle size (client-side estimation)
   */
  analyzeBundleSize() {
    if (typeof window === 'undefined') return

    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
    let totalEstimatedSize = 0

    stylesheets.forEach((link) => {
      const href = (link as HTMLLinkElement).href
      
      // Estimate size based on URL (rough approximation)
      if (href.includes('_next/static/css/')) {
        // Next.js CSS bundles are typically optimized
        totalEstimatedSize += 50 // KB estimate
      }
    })

    if (process.env.NODE_ENV === 'development') {
      console.log(`📦 Estimated CSS Bundle Size: ~${totalEstimatedSize}KB`)
      
      if (totalEstimatedSize > 100) {
        console.warn('⚠️ Large CSS bundle detected. Consider code splitting.')
      }
    }
  }
}

// Create singleton instance
export const cssPerformanceMonitor = new CSSPerformanceMonitor()

// Auto-initialize in browser
if (typeof window !== 'undefined') {
  cssPerformanceMonitor.init()
  cssPerformanceMonitor.checkCoreWebVitals()
  cssPerformanceMonitor.monitorFontLoading()
  cssPerformanceMonitor.analyzeBundleSize()
}
