import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { Decimal } from '@prisma/client/runtime/library'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const PATCH = withAdminAuth(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const body = await request.json()
    const { id } = await params
    const statsId = parseInt(id)

    if (!statsId) {
      return NextResponse.json(
        { error: 'Invalid stats ID' },
        { status: 400 }
      )
    }

    // Get current stats to calculate win percentage properly
    const currentStats = await prisma.playerStats.findUnique({
      where: { id: statsId }
    })

    if (!currentStats) {
      return NextResponse.json(
        { error: 'Player stats not found' },
        { status: 404 }
      )
    }

    // Calculate win percentage if wins or losses are being updated
    let winPercentage = currentStats.winPercentage
    if (body.totalWins !== undefined || body.totalLosses !== undefined) {
      const wins = body.totalWins !== undefined ? body.totalWins : currentStats.totalWins
      const losses = body.totalLosses !== undefined ? body.totalLosses : currentStats.totalLosses
      const totalGames = wins + losses
      winPercentage = new Decimal(totalGames > 0 ? (wins / totalGames) * 100 : 0)
    }

    // Update the player stats
    const updatedStats = await prisma.playerStats.update({
      where: { id: statsId },
      data: {
        ...body,
        winPercentage
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        },
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    return NextResponse.json(updatedStats)
  } catch (error) {
    logger.error('Error updating player stats:', error)
    return NextResponse.json(
      { error: 'Failed to update stats' },
      { status: 500 }
    )
  }
})
