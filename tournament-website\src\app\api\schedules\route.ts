import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'
import { getTournamentWeekForDate } from '@/lib/tournament-utils'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const gameId = searchParams.get('gameId')
    const status = searchParams.get('status')

    const where: any = {}
    if (gameId) where.gameId = parseInt(gameId)
    if (status) where.status = status

    const schedules = await prisma.tournamentSchedule.findMany({
      where,
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: [
        { scheduledDate: 'asc' },
        { scheduledTime: 'asc' }
      ]
    })

    // Add week number to each schedule
    const schedulesWithWeeks = schedules.map(schedule => ({
      ...schedule,
      weekNumber: getTournamentWeekForDate(schedule.scheduledDate),
      year: schedule.scheduledDate.getFullYear()
    }))

    return NextResponse.json(schedulesWithWeeks)
  } catch (error) {
    logger.error('Error fetching schedules:', error)
    return NextResponse.json(
      { error: 'Failed to fetch schedules' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { gameId, scheduledDate, scheduledTime, description } = body

    if (!gameId || !scheduledDate || !scheduledTime) {
      return NextResponse.json(
        { error: 'Game ID, scheduled date, and scheduled time are required' },
        { status: 400 }
      )
    }

    // Generate tournament ID
    const currentYear = new Date().getFullYear()
    const existingCount = await prisma.tournamentSchedule.count({
      where: {
        createdAt: {
          gte: new Date(`${currentYear}-01-01`),
          lt: new Date(`${currentYear + 1}-01-01`)
        }
      }
    })
    const tournamentId = `T${currentYear}${String(existingCount + 1).padStart(3, '0')}`

    const scheduleDate = new Date(scheduledDate)
    const weekNumber = getTournamentWeekForDate(scheduleDate)
    const year = scheduleDate.getFullYear()

    const schedule = await prisma.tournamentSchedule.create({
      data: {
        tournamentId,
        gameId: parseInt(gameId),
        scheduledDate: scheduleDate,
        scheduledTime: new Date(`1970-01-01T${scheduledTime}:00.000Z`),
        description: description || `${gameId === 1 ? 'PUBG' : gameId === 2 ? 'Call of Duty' : 'PES'} Tournament - Week ${weekNumber}`
      },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Also create a corresponding WeeklyTournament record for the leaderboard
    try {
      await prisma.weeklyTournament.upsert({
        where: {
          gameId_weekNumber_year: {
            gameId: parseInt(gameId),
            weekNumber: weekNumber,
            year: year
          }
        },
        update: {
          tournamentDate: scheduleDate,
          status: 'UPCOMING'
        },
        create: {
          gameId: parseInt(gameId),
          weekNumber: weekNumber,
          year: year,
          tournamentDate: scheduleDate,
          status: 'UPCOMING',
          totalParticipants: 0
        }
      })
    } catch (error) {
      logger.warn('Could not create/update WeeklyTournament record:', error)
    }

    return NextResponse.json(schedule, { status: 201 })
  } catch (error) {
    logger.error('Error creating schedule:', error)
    return NextResponse.json(
      { error: 'Failed to create schedule' },
      { status: 500 }
    )
  }
}
