module.exports = {
  apps: [
    {
      name: 'mzuni-tournaments',
      script: 'npm',
      args: 'start',
      cwd: './',

      // Production Performance Settings
      instances: 'max', // Use all CPU cores
      exec_mode: 'cluster',

      // Restart Configuration
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      max_restarts: 10,
      min_uptime: '10s',

      // Logging Configuration
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // Process Management
      kill_timeout: 5000,
      listen_timeout: 3000,

      // Development Environment
      env: {
        NODE_ENV: 'development',
        PORT: 3002,
        INSTANCES: 1
      },

      // Production Environment
      env_production: {
        NODE_ENV: 'production',
        PORT: 3002,

        // Performance Optimizations
        NODE_OPTIONS: '--max-old-space-size=2048',

        // Security Headers
        FORCE_HTTPS: 'true',

        // Caching
        ENABLE_COMPRESSION: 'true',
        CACHE_CONTROL_MAX_AGE: '31536000',

        // Rate Limiting
        RATE_LIMIT_WINDOW_MS: '900000', // 15 minutes
        RATE_LIMIT_MAX: '100', // requests per window

        // Session Configuration
        SESSION_TIMEOUT: '604800', // 7 days
        COOKIE_SECURE: 'true',

        // Database Connection Pool
        DATABASE_POOL_MIN: '2',
        DATABASE_POOL_MAX: '10',

        // Monitoring
        ENABLE_METRICS: 'true',
        HEALTH_CHECK_INTERVAL: '30000' // 30 seconds
      },

      // Staging Environment
      env_staging: {
        NODE_ENV: 'production',
        PORT: 3003,
        DATABASE_URL: process.env.STAGING_DATABASE_URL,
        NEXTAUTH_URL: process.env.STAGING_NEXTAUTH_URL
      }
    }
  ],

  // Deployment Configuration
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: 'https://github.com/your-username/mzuni-tournaments.git',
      path: '/var/www/mzuni-tournaments',
      'post-deploy': 'npm ci --production && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    },

    staging: {
      user: 'deploy',
      host: ['staging-server.com'],
      ref: 'origin/develop',
      repo: 'https://github.com/your-username/mzuni-tournaments.git',
      path: '/var/www/mzuni-tournaments-staging',
      'post-deploy': 'npm ci && npm run build && pm2 reload ecosystem.config.js --env staging'
    }
  }
}


