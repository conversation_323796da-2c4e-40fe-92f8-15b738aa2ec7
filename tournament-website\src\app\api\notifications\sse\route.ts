import { NextRequest } from 'next/server'
import { validateSession } from '@/lib/auth'

// Store active connections
const connections = new Map<string, ReadableStreamDefaultController>()

export async function GET(request: NextRequest) {
  // Validate user session
  const sessionCookie = request.cookies.get('session')
  if (!sessionCookie) {
    return new Response('Unauthorized', { status: 401 })
  }

  const user = await validateSession(sessionCookie.value)
  if (!user) {
    return new Response('Unauthorized', { status: 401 })
  }

  const userId = user.userId.toString()

  // Create Server-Sent Events stream
  const stream = new ReadableStream({
    start(controller) {
      // Store connection for this user
      connections.set(userId, controller)

      // Send initial connection message
      controller.enqueue(`data: ${JSON.stringify({
        type: 'connected',
        message: 'Connected to live updates',
        timestamp: new Date().toISOString()
      })}\n\n`)

      // Send heartbeat every 30 seconds
      const heartbeat = setInterval(() => {
        try {
          controller.enqueue(`data: ${JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          })}\n\n`)
        } catch (error) {
          clearInterval(heartbeat)
          connections.delete(userId)
        }
      }, 30000)

      // Clean up on close
      request.signal.addEventListener('abort', () => {
        clearInterval(heartbeat)
        connections.delete(userId)
        try {
          controller.close()
        } catch (error) {
          // Connection already closed
        }
      })
    },
    cancel() {
      connections.delete(userId)
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  })
}

// Function to broadcast notifications to all connected users
export function broadcastNotification(notification: {
  type: string
  message: string
  data?: any
  targetUsers?: string[]
}) {
  const message = `data: ${JSON.stringify({
    ...notification,
    timestamp: new Date().toISOString()
  })}\n\n`

  if (notification.targetUsers) {
    // Send to specific users
    notification.targetUsers.forEach(userId => {
      const controller = connections.get(userId)
      if (controller) {
        try {
          controller.enqueue(message)
        } catch (error) {
          connections.delete(userId)
        }
      }
    })
  } else {
    // Broadcast to all connected users
    connections.forEach((controller, userId) => {
      try {
        controller.enqueue(message)
      } catch (error) {
        connections.delete(userId)
      }
    })
  }
}

// Function to send notification to specific user
export function sendNotificationToUser(userId: string, notification: {
  type: string
  message: string
  data?: any
}) {
  const controller = connections.get(userId)
  if (controller) {
    try {
      controller.enqueue(`data: ${JSON.stringify({
        ...notification,
        timestamp: new Date().toISOString()
      })}\n\n`)
    } catch (error) {
      connections.delete(userId)
    }
  }
}

// Export for use in other API routes
export { connections }
