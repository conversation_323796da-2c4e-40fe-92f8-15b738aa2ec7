#!/usr/bin/env node

require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')

async function clearSampleDataKeepUsers() {
  const prisma = new PrismaClient()
  
  try {
    console.log('🧹 Clearing Sample Data (Keeping Users)...')
    console.log('==========================================\n')

    // Get current user count before clearing
    const userCount = await prisma.user.count()
    console.log(`👥 Current users in database: ${userCount}`)

    // Clear tournament-related data but keep users
    console.log('\n🗑️  Clearing tournament data...')

    // 1. Clear player registrations
    const deletedRegistrations = await prisma.playerRegistration.deleteMany({})
    console.log(`   ✅ Cleared ${deletedRegistrations.count} player registrations`)

    // 2. Skip tournament registrations (doesn't exist in schema)

    // 3. Clear player stats
    const deletedStats = await prisma.playerStats.deleteMany({})
    console.log(`   ✅ Cleared ${deletedStats.count} player stats`)

    // 4. Clear weekly winners
    const deletedWinners = await prisma.weeklyWinner.deleteMany({})
    console.log(`   ✅ Cleared ${deletedWinners.count} weekly winners`)

    // 5. Clear weekly tournaments
    const deletedWeeklyTournaments = await prisma.weeklyTournament.deleteMany({})
    console.log(`   ✅ Cleared ${deletedWeeklyTournaments.count} weekly tournaments`)

    // 6. Clear tournament schedules
    const deletedSchedules = await prisma.tournamentSchedule.deleteMany({})
    console.log(`   ✅ Cleared ${deletedSchedules.count} tournament schedules`)

    // 7. Clear announcements
    const deletedAnnouncements = await prisma.announcement.deleteMany({})
    console.log(`   ✅ Cleared ${deletedAnnouncements.count} announcements`)

    // 8. Clear user sessions (force re-login)
    const deletedSessions = await prisma.userSession.deleteMany({})
    console.log(`   ✅ Cleared ${deletedSessions.count} user sessions (users will need to re-login)`)

    // Keep games but reset their data
    console.log('\n🎮 Resetting games data...')
    const games = await prisma.game.findMany()
    console.log(`   ℹ️  Keeping ${games.length} games: ${games.map(g => g.name).join(', ')}`)

    // Verify users are still there
    const finalUserCount = await prisma.user.count()
    console.log(`\n👥 Users preserved: ${finalUserCount}`)

    if (finalUserCount !== userCount) {
      console.log('⚠️  WARNING: User count changed! This should not happen.')
    }

    // Show preserved users
    const users = await prisma.user.findMany({
      select: {
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        isActive: true
      }
    })

    console.log('\n✅ Preserved Users:')
    users.forEach(user => {
      console.log(`   - ${user.username} (${user.firstName} ${user.lastName}) - ${user.role} - ${user.isActive ? 'Active' : 'Inactive'}`)
    })

    console.log('\n🎯 Sample data cleared successfully!')
    console.log('📝 What was cleared:')
    console.log('   • All player registrations')
    console.log('   • All player statistics')
    console.log('   • All tournament schedules')
    console.log('   • All weekly tournaments')
    console.log('   • All announcements')
    console.log('   • All user sessions (requires re-login)')
    
    console.log('\n💾 What was preserved:')
    console.log('   • All user accounts')
    console.log('   • All games (PUBG, Call of Duty, PES)')
    console.log('   • User passwords and profiles')
    
    console.log('\n🔑 Login credentials still valid:')
    console.log('   Admin: Tournaowner / Bsvca2223')
    console.log('   Test User: testuser / testpass123')
    console.log('   + All other registered users')

    console.log('\n🌐 Ready for fresh tournament data!')
    console.log('   Visit: http://localhost:3000')

  } catch (error) {
    console.error('❌ Error clearing sample data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

clearSampleDataKeepUsers()
