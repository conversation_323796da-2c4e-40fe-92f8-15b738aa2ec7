#!/usr/bin/env node

/**
 * TOURNAMENT DATA RESET SCRIPT
 * ============================
 * 
 * This script clears all tournament-related data while preserving:
 * - All user accounts (players, admin, etc.)
 * - All games (PUBG, Call of Duty, PES)
 * - User passwords and profiles
 * 
 * Use this when you want to start fresh with tournament data
 * but keep all registered users.
 */

require('dotenv').config({ path: '.env.local' })
const { PrismaClient } = require('@prisma/client')

async function resetTournamentData() {
  const prisma = new PrismaClient()
  
  try {
    console.log('🔄 RESETTING TOURNAMENT DATA')
    console.log('============================\n')

    // Confirm action
    console.log('⚠️  This will clear ALL tournament data but keep users!')
    console.log('   • Player registrations')
    console.log('   • Tournament schedules')
    console.log('   • Player statistics')
    console.log('   • Weekly tournaments')
    console.log('   • Announcements')
    console.log('   • User sessions (requires re-login)\n')

    // Get counts before clearing
    const counts = {
      users: await prisma.user.count(),
      registrations: await prisma.playerRegistration.count(),
      stats: await prisma.playerStats.count(),
      schedules: await prisma.tournamentSchedule.count(),
      tournaments: await prisma.weeklyTournament.count(),
      announcements: await prisma.announcement.count(),
      sessions: await prisma.userSession.count()
    }

    console.log('📊 Current Data:')
    console.log(`   Users: ${counts.users}`)
    console.log(`   Registrations: ${counts.registrations}`)
    console.log(`   Schedules: ${counts.schedules}`)
    console.log(`   Statistics: ${counts.stats}`)
    console.log(`   Tournaments: ${counts.tournaments}`)
    console.log(`   Announcements: ${counts.announcements}`)
    console.log(`   Sessions: ${counts.sessions}\n`)

    // Clear tournament data
    console.log('🗑️  Clearing tournament data...')
    
    await prisma.playerRegistration.deleteMany({})
    console.log('   ✅ Cleared player registrations')
    
    await prisma.playerStats.deleteMany({})
    console.log('   ✅ Cleared player statistics')
    
    await prisma.weeklyWinner.deleteMany({})
    console.log('   ✅ Cleared weekly winners')
    
    await prisma.weeklyTournament.deleteMany({})
    console.log('   ✅ Cleared weekly tournaments')
    
    await prisma.tournamentSchedule.deleteMany({})
    console.log('   ✅ Cleared tournament schedules')
    
    await prisma.announcement.deleteMany({})
    console.log('   ✅ Cleared announcements')
    
    await prisma.userSession.deleteMany({})
    console.log('   ✅ Cleared user sessions')

    // Verify users and games are preserved
    const finalCounts = {
      users: await prisma.user.count(),
      games: await prisma.game.count()
    }

    console.log('\n✅ RESET COMPLETE!')
    console.log(`   Users preserved: ${finalCounts.users}`)
    console.log(`   Games preserved: ${finalCounts.games}`)

    if (finalCounts.users !== counts.users) {
      console.log('⚠️  WARNING: User count changed unexpectedly!')
    }

    console.log('\n🎮 Games available for new tournaments:')
    const games = await prisma.game.findMany()
    games.forEach(game => {
      console.log(`   - ${game.name}: ${game.description}`)
    })

    console.log('\n🔑 Login credentials still work:')
    console.log('   Admin: Tournaowner / Bsvca2223')
    console.log('   Test User: testuser / testpass123')

    console.log('\n🌐 Website ready: http://localhost:3000')
    console.log('   Registration counts reset to 0')
    console.log('   Ready for new tournament schedules')

  } catch (error) {
    console.error('❌ Error resetting tournament data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

resetTournamentData()
