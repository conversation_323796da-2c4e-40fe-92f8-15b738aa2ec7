import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    // Only get players who have actually participated in tournaments or have registrations
    // This excludes users who just have accounts but never engaged with tournaments

    // Get players with tournament stats (these are the main leaderboard)
    const playersWithStats = await prisma.playerStats.findMany({
      orderBy: [
        { tournamentsWon: 'desc' },
        { winPercentage: 'desc' },
        { totalWins: 'desc' }
      ],
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        },
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Get players who have registered for games but haven't played tournaments yet
    const playersWithRegistrationsOnly = await prisma.user.findMany({
      where: {
        role: 'PLAYER',
        registrations: {
          some: {} // Has at least one registration
        },
        NOT: {
          id: {
            in: playersWithStats.map(p => p.userId)
          }
        }
      },
      include: {
        registrations: {
          include: {
            game: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    })

    // Transform registered players without stats
    const registeredPlayersFormatted = playersWithRegistrationsOnly.flatMap(user => {
      return user.registrations.map(reg => ({
        id: `${user.id}-${reg.gameId}`,
        userId: user.id,
        gameId: reg.gameId,
        tournamentsWon: 0,
        tournamentsParticipated: 0,
        totalWins: 0,
        totalLosses: 0,
        winPercentage: 0,
        user: {
          id: user.id,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName
        },
        game: reg.game
      }))
    })

    // Combine lists: players with stats first, then registered players
    const allPlayers = [...playersWithStats, ...registeredPlayersFormatted]

    return NextResponse.json(allPlayers)
  } catch (error) {
    logger.error('Error fetching top players:', error)
    return NextResponse.json(
      { error: 'Failed to fetch top players' },
      { status: 500 }
    )
  }
})
