# Mzuni Tournaments - Production Deployment Guide

## 🚀 Production Readiness Checklist

### ✅ Environment Variables Setup

1. **Copy the environment template:**
   ```bash
   cp .env.production.template .env.production
   ```

2. **Configure production environment variables:**
   ```bash
   # Required Variables
   DATABASE_URL="postgresql://username:password@host:port/database_name"
   NEXTAUTH_URL="https://your-domain.com"
   NEXTAUTH_SECRET="your-super-secure-secret-minimum-32-characters"
   NODE_ENV="production"
   
   # Admin Credentials (CHANGE THESE!)
   ADMIN_USERNAME="your-secure-admin-username"
   ADMIN_PASSWORD="your-secure-admin-password"
   
   # WhatsApp Notifications
   WHATSAPP_NUMBER="+************"
   ```

### 🔒 Security Configuration

1. **Generate Strong Secrets:**
   ```bash
   # Generate NEXTAUTH_SECRET
   openssl rand -base64 32
   
   # Or use Node.js
   node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
   ```

2. **Database Security:**
   - Use strong database passwords
   - Enable SSL connections in production
   - Restrict database access to application servers only
   - Regular database backups

3. **Admin Security:**
   - Change default admin credentials
   - Use strong passwords (minimum 12 characters)
   - Enable 2FA if possible
   - Monitor admin login attempts

### 🌐 Production Build

1. **Install dependencies:**
   ```bash
   npm ci --production
   ```

2. **Build the application:**
   ```bash
   npm run build
   ```

3. **Database setup:**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

4. **Create admin user:**
   ```bash
   node create-admin.js
   ```

### 🚀 Deployment Options

#### Option 1: PM2 (Recommended)
```bash
# Install PM2 globally
npm install -g pm2

# Using the management script (Linux/Mac)
./scripts/pm2-manager.sh start-prod

# Or using the batch file (Windows)
scripts\pm2-manager.bat start-prod

# Manual PM2 commands
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup

# Production features enabled:
# - Cluster mode with all CPU cores
# - Auto-restart on crashes
# - Memory limit monitoring
# - Comprehensive logging
# - Zero-downtime reloads
```

#### Option 2: Docker (Single Container)
```bash
# Build Docker image
docker build -t mzuni-tournaments .

# Run container
docker run -d \
  --name mzuni-tournaments \
  -p 3002:3002 \
  --env-file .env.production \
  mzuni-tournaments
```

#### Option 3: Docker Compose (Full Stack)
```bash
# Copy environment file
cp .env.production.template .env.production
# Edit .env.production with your values

# Start full stack (app + database + nginx)
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

#### Option 3: Vercel/Netlify
- Configure environment variables in platform dashboard
- Connect GitHub repository
- Enable automatic deployments

### 🔍 Production Monitoring

1. **Health Checks:**
   - Monitor `/api/health` endpoint
   - Set up uptime monitoring
   - Configure alerts for downtime

2. **Performance Monitoring:**
   - Monitor response times
   - Track memory usage
   - Monitor database connections

3. **Security Monitoring:**
   - Monitor failed login attempts
   - Track admin access
   - Set up WhatsApp alerts for admin logins

### 📊 Database Maintenance

1. **Regular Backups:**
   ```bash
   # Daily backup script
   pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql
   ```

2. **Performance Optimization:**
   - Monitor slow queries
   - Optimize database indexes
   - Regular VACUUM and ANALYZE

### 🛡️ Security Best Practices

1. **HTTPS Only:**
   - Use SSL certificates
   - Redirect HTTP to HTTPS
   - Enable HSTS headers

2. **Rate Limiting:**
   - Implement API rate limiting
   - Protect against brute force attacks
   - Monitor suspicious activity

3. **Regular Updates:**
   - Keep dependencies updated
   - Apply security patches
   - Monitor vulnerability alerts

### 🎛️ PM2 Management Commands

```bash
# Quick start production
npm run setup:production

# PM2 operations
npm run pm2:start-prod    # Start in production
npm run pm2:status        # Check status
npm run pm2:logs          # View logs
npm run pm2:restart       # Restart app
npm run pm2:reload        # Zero-downtime reload
npm run pm2:stop          # Stop app
npm run pm2:monitor       # Open monitor

# Using management scripts
./scripts/pm2-manager.sh [command]     # Linux/Mac
scripts\pm2-manager.bat [command]      # Windows

# Health check
npm run health
```

### 🔧 Troubleshooting

1. **Common Issues:**
   - Database connection errors
   - Environment variable misconfigurations
   - Build failures
   - Port conflicts
   - Memory issues

2. **Logs Location:**
   - Application logs: `logs/combined.log`
   - Error logs: `logs/err.log`
   - Output logs: `logs/out.log`
   - PM2 logs: `~/.pm2/logs/`
   - System logs: `/var/log/`

3. **Debug Commands:**
   ```bash
   # Check application status
   pm2 status mzuni-tournaments

   # View real-time logs
   pm2 logs mzuni-tournaments --lines 100

   # Monitor resources
   pm2 monit

   # Restart application
   pm2 restart mzuni-tournaments

   # Reload with zero downtime
   pm2 reload mzuni-tournaments

   # Check health endpoint
   curl http://localhost:3002/api/health
   ```

4. **Performance Optimization:**
   ```bash
   # Check memory usage
   pm2 show mzuni-tournaments

   # Scale instances
   pm2 scale mzuni-tournaments 4

   # Reset restart counter
   pm2 reset mzuni-tournaments
   ```

### 📞 Support

For deployment support, contact the development team or refer to the main README.md file.

---

**⚠️ Important:** Always test the deployment in a staging environment before going live!
