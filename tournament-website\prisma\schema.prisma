// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              Int      @id @default(autoincrement())
  username        String   @unique @db.VarChar(50)
  email           String?  @unique @db.VarChar(100)
  firstName       String   @map("first_name") @db.VarChar(50)
  lastName        String   @map("last_name") @db.VarChar(50)
  phoneNumber     String   @map("phone_number") @db.VarChar(20)
  studentId       String?  @map("student_id") @db.VarChar(20)
  password        String   @db.VarChar(255)
  gender          Gender?
  address         String?  @db.Text
  emergencyContact String? @map("emergency_contact") @db.VarChar(20)

  isActive        Boolean  @default(true) @map("is_active")
  role            Role     @default(PLAYER)
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @default(now()) @updatedAt @map("updated_at")

  // Relations
  registrations    PlayerRegistration[]
  stats           PlayerStats[]
  weeklyWins      WeeklyWinner[]
  tournamentWins  WeeklyTournament[]   @relation("TournamentWinner")
  sessions        UserSession[]


  @@map("users")
}

model Game {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  description String?
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  registrations     PlayerRegistration[]
  stats            PlayerStats[]
  weeklyTournaments WeeklyTournament[]
  weeklyWinners    WeeklyWinner[]
  schedules        TournamentSchedule[]

  @@map("games")
}

model PlayerRegistration {
  id                    Int      @id @default(autoincrement())
  userId                Int      @map("user_id")
  gameId                Int      @map("game_id")
  tournamentScheduleId  Int?     @map("tournament_schedule_id") // Link to specific tournament
  gameUsername          String?  @map("game_username") @db.VarChar(100)
  registrationDate      DateTime @default(now()) @map("registration_date")
  isActive              Boolean  @default(true) @map("is_active")
  paymentStatus         PaymentStatus @default(UNPAID) @map("payment_status")
  paymentDate           DateTime? @map("payment_date")
  paymentAmount         Decimal?  @map("payment_amount") @db.Decimal(10, 2)
  paymentNotes          String?   @map("payment_notes")

  // Relations
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  game                  Game     @relation(fields: [gameId], references: [id], onDelete: Cascade)
  tournamentSchedule    TournamentSchedule? @relation("TournamentRegistrations", fields: [tournamentScheduleId], references: [id], onDelete: SetNull)

  @@unique([userId, gameId])
  @@map("player_registrations")
}

enum PaymentStatus {
  PAID
  UNPAID
  PARTIAL
  REFUNDED
}

model WeeklyTournament {
  id                 Int      @id @default(autoincrement())
  gameId             Int      @map("game_id")
  weekNumber         Int      @map("week_number")
  year               Int
  tournamentDate     DateTime @map("tournament_date") @db.Date
  status             TournamentStatus @default(UPCOMING)
  winnerId           Int?     @map("winner_id")
  totalParticipants  Int      @default(0) @map("total_participants")
  createdAt          DateTime @default(now()) @map("created_at")

  // Relations
  game               Game     @relation(fields: [gameId], references: [id], onDelete: Cascade)
  winner             User?    @relation("TournamentWinner", fields: [winnerId], references: [id], onDelete: SetNull)
  weeklyWinners      WeeklyWinner[]

  @@unique([gameId, weekNumber, year])
  @@map("weekly_tournaments")
}

model PlayerStats {
  id                    Int      @id @default(autoincrement())
  userId                Int      @map("user_id")
  gameId                Int      @map("game_id")
  tournamentsParticipated Int    @default(0) @map("tournaments_participated")
  tournamentsWon        Int      @default(0) @map("tournaments_won")
  totalWins             Int      @default(0) @map("total_wins")
  totalLosses           Int      @default(0) @map("total_losses")
  winPercentage         Decimal  @default(0.00) @map("win_percentage") @db.Decimal(5, 2)
  lastUpdated           DateTime @default(now()) @map("last_updated")

  // Relations
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  game                  Game     @relation(fields: [gameId], references: [id], onDelete: Cascade)

  @@unique([userId, gameId])
  @@map("player_stats")
}

model TournamentSchedule {
  id                  Int      @id @default(autoincrement())
  tournamentId        String   @unique @map("tournament_id") // ********, ********, etc.
  gameId              Int      @map("game_id")
  scheduledDate       DateTime @map("scheduled_date") @db.Date
  scheduledTime       String   @map("scheduled_time") @db.VarChar(8) // Store as "HH:MM" format
  description         String?
  status              ScheduleStatus @default(SCHEDULED)
  maxParticipants     Int?     @map("max_participants")
  currentParticipants Int      @default(0) @map("current_participants")
  registrationDeadline DateTime? @map("registration_deadline")
  createdAt           DateTime @default(now()) @map("created_at")

  // Relations
  game                Game     @relation(fields: [gameId], references: [id], onDelete: Cascade)
  registrations       PlayerRegistration[] @relation("TournamentRegistrations")

  @@map("tournament_schedules")
}

model WeeklyWinner {
  id           Int      @id @default(autoincrement())
  userId       Int      @map("user_id")
  gameId       Int      @map("game_id")
  weekNumber   Int      @map("week_number")
  year         Int
  tournamentId Int      @map("tournament_id")
  createdAt    DateTime @default(now()) @map("created_at")

  // Relations
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  game         Game     @relation(fields: [gameId], references: [id], onDelete: Cascade)
  tournament   WeeklyTournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)

  @@unique([gameId, weekNumber, year])
  @@map("weekly_winners")
}

model Announcement {
  id        Int      @id @default(autoincrement())
  title     String   @db.VarChar(200)
  content   String   @db.Text
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("announcements")
}

model UserSession {
  id        String   @id @default(cuid())
  userId    Int      @map("user_id")
  token     String   @unique @db.VarChar(255)
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}



// Enums
enum Role {
  ADMIN
  PLAYER

  @@map("role")
}

enum Gender {
  MALE
  FEMALE
  OTHER

  @@map("gender")
}

enum TournamentStatus {
  UPCOMING
  IN_PROGRESS
  COMPLETED
  CANCELLED

  @@map("tournament_status")
}

enum ScheduleStatus {
  SCHEDULED
  COMPLETED
  CANCELLED

  @@map("schedule_status")
}




