// Using built-in fetch (Node.js 18+)

async function testAdminAuth() {
  console.log('🔐 Testing Admin Authentication...\n')

  try {
    // Step 1: Test admin login
    console.log('1️⃣ Testing Admin Login:')
    const loginResponse = await fetch('http://localhost:3000/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: '<PERSON>naowner',
        password: 'Bsvca2223'
      })
    })

    if (loginResponse.ok) {
      const loginData = await loginResponse.json()
      console.log('✅ Login successful!')
      console.log(`   Token: ${loginData.token.substring(0, 20)}...`)
      console.log(`   User: ${loginData.user.username}`)

      // Step 2: Test admin stats API with token
      console.log('\n2️⃣ Testing Admin Stats API with Token:')
      const statsResponse = await fetch('http://localhost:3000/api/admin/stats', {
        headers: {
          'Authorization': `Bearer ${loginData.token}`
        }
      })

      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        console.log('✅ Admin stats API working!')
        console.log(`   Total Players: ${statsData.totalPlayers}`)
        console.log(`   Total Registrations: ${statsData.totalRegistrations}`)
        console.log(`   Upcoming Tournaments: ${statsData.upcomingTournaments}`)
        console.log(`   Completed Tournaments: ${statsData.completedTournaments}`)
      } else {
        console.log(`❌ Admin stats API failed: ${statsResponse.status} ${statsResponse.statusText}`)
        const errorText = await statsResponse.text()
        console.log(`   Error: ${errorText}`)
      }

      // Step 3: Test admin registrations API
      console.log('\n3️⃣ Testing Admin Registrations API:')
      const registrationsResponse = await fetch('http://localhost:3000/api/admin/registrations', {
        headers: {
          'Authorization': `Bearer ${loginData.token}`
        }
      })

      if (registrationsResponse.ok) {
        const registrationsData = await registrationsResponse.json()
        console.log('✅ Admin registrations API working!')
        console.log(`   Total registrations found: ${registrationsData.length}`)
      } else {
        console.log(`❌ Admin registrations API failed: ${registrationsResponse.status}`)
      }

      // Step 4: Test admin players API
      console.log('\n4️⃣ Testing Admin Players API:')
      const playersResponse = await fetch('http://localhost:3000/api/admin/players', {
        headers: {
          'Authorization': `Bearer ${loginData.token}`
        }
      })

      if (playersResponse.ok) {
        const playersData = await playersResponse.json()
        console.log('✅ Admin players API working!')
        console.log(`   Total players found: ${playersData.length}`)
      } else {
        console.log(`❌ Admin players API failed: ${playersResponse.status}`)
      }

    } else {
      console.log(`❌ Login failed: ${loginResponse.status} ${loginResponse.statusText}`)
      const errorText = await loginResponse.text()
      console.log(`   Error: ${errorText}`)
    }

    // Step 5: Test without token (should fail)
    console.log('\n5️⃣ Testing API without Token (should fail):')
    const noTokenResponse = await fetch('http://localhost:3000/api/admin/stats')
    console.log(`   Status: ${noTokenResponse.status} (${noTokenResponse.status === 403 ? '✅ Correctly blocked' : '❌ Should be blocked'})`)

    console.log('\n🎯 Admin Authentication Test Complete!')

  } catch (error) {
    console.error('❌ Error testing admin auth:', error)
  }
}

// Run the test
testAdminAuth()
