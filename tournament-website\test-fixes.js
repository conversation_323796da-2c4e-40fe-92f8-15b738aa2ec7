#!/usr/bin/env node

const https = require('https')
const http = require('http')

console.log('🧪 Testing Tournament Website Fixes')
console.log('===================================\n')

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    http.get(url, (res) => {
      let data = ''
      res.on('data', (chunk) => data += chunk)
      res.on('end', () => {
        try {
          resolve(JSON.parse(data))
        } catch (e) {
          resolve(data)
        }
      })
    }).on('error', reject)
  })
}

async function testFixes() {
  try {
    // Test 1: Check games API returns correct registration counts
    console.log('1. Testing Games API Registration Counts...')
    const games = await makeRequest('http://localhost:3000/api/games')
    if (Array.isArray(games)) {
      console.log('✅ Games API working')
      games.forEach(game => {
        console.log(`   - ${game.name}: ${game._count.registrations} registrations`)
      })
    } else {
      console.log('❌ Games API failed:', games)
    }

    console.log('\n2. Testing Schedule Time Format...')
    console.log('   ℹ️  To test time input fix:')
    console.log('   1. Go to http://localhost:3000/admin/login')
    console.log('   2. Login with: Tournaowner / Bsvca2223')
    console.log('   3. Go to Schedules and add a new schedule')
    console.log('   4. Set time to 01:00PM and verify it shows correctly')

    console.log('\n3. Testing Schedule Completion Reset...')
    console.log('   ℹ️  To test registration reset:')
    console.log('   1. Go to admin schedules')
    console.log('   2. Change a schedule status to "COMPLETED"')
    console.log('   3. Check home page - registration counts should reset to 0')

    console.log('\n✅ Test setup complete!')
    console.log('🌐 Visit http://localhost:3000 to test the fixes')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testFixes()
