import { NextRequest, NextResponse } from 'next/server'
import { validateAdminSession } from '@/lib/auth'

/**
 * Validate admin token from Authorization header or localStorage
 * @param token - The admin token to validate
 * @returns boolean - True if valid admin token
 */
function validateAdminToken(token: string): boolean {
  try {
    // Decode the token (format: base64(username:timestamp))
    const decoded = Buffer.from(token, 'base64').toString('utf-8')
    const [username, timestamp] = decoded.split(':')

    // Check if it's the admin username
    if (username === process.env.ADMIN_USERNAME || username === 'Tournaowner') {
      // Check if token is not too old (24 hours)
      const tokenTime = parseInt(timestamp)
      const now = Date.now()
      const maxAge = 24 * 60 * 60 * 1000 // 24 hours

      return (now - tokenTime) < maxAge
    }

    return false
  } catch (error) {
    return false
  }
}

/**
 * Middleware to check admin authentication for admin API endpoints
 * @param request - The NextRequest object
 * @returns Promise<NextResponse | null> - Returns error response if not authenticated, null if authenticated
 */
export async function requireAdminAuth(request: NextRequest): Promise<NextResponse | null> {
  // Check for Authorization header first (for API calls)
  const authHeader = request.headers.get('Authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7)
    if (validateAdminToken(token)) {
      return null // Authentication successful
    }
  }

  // Check for session cookie (fallback)
  const sessionCookie = request.cookies.get('session')
  if (sessionCookie) {
    const admin = await validateAdminSession(sessionCookie.value)
    if (admin && admin.role === 'ADMIN') {
      return null // Authentication successful
    }
  }

  // Check if it's a direct admin token in cookies (for localStorage-based auth)
  const adminToken = request.cookies.get('adminToken')
  if (adminToken && validateAdminToken(adminToken.value)) {
    return null // Authentication successful
  }

  return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
}

/**
 * Wrapper function for admin API handlers
 * @param handler - The actual API handler function
 * @returns Wrapped handler with admin authentication
 */
export function withAdminAuth(handler: (request: NextRequest, context?: any) => Promise<NextResponse>) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    const authError = await requireAdminAuth(request)
    if (authError) {
      return authError
    }

    return handler(request, context)
  }
}
