#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

console.log('🐛 COMPREHENSIVE BUG CHECK ANALYSIS')
console.log('===================================\n')

let totalIssues = 0
let criticalBugs = 0
let warningBugs = 0
let infoBugs = 0

// Helper function to scan files
function scanDirectory(dir, callback, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const results = []
  
  function scan(currentDir) {
    try {
      const files = fs.readdirSync(currentDir)
      
      for (const file of files) {
        const filePath = path.join(currentDir, file)
        const stat = fs.statSync(filePath)
        
        if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
          scan(filePath)
        } else if (extensions.some(ext => file.endsWith(ext))) {
          const content = fs.readFileSync(filePath, 'utf8')
          const result = callback(filePath, content, file)
          if (result) results.push(result)
        }
      }
    } catch (error) {
      console.log(`⚠️  Error scanning ${currentDir}: ${error.message}`)
    }
  }
  
  scan(dir)
  return results
}

// 1. Console Log Check
console.log('1. 🖨️  CONSOLE LOG CHECK')
const consoleLogs = scanDirectory('./src', (filePath, content) => {
  const matches = content.match(/console\.(log|error|warn|debug|info)/g)
  if (matches && !filePath.includes('logger.ts') && !filePath.includes('test')) {
    return { file: filePath, count: matches.length, type: 'console' }
  }
})

if (consoleLogs.length === 0) {
  console.log('✅ No console statements found in production code')
} else {
  console.log(`❌ Found ${consoleLogs.length} files with console statements:`)
  consoleLogs.forEach(log => {
    console.log(`   ${log.file}: ${log.count} statements`)
    warningBugs++
  })
}

// 2. Unused Variables Check
console.log('\n2. 🔍 UNUSED VARIABLES CHECK')
const unusedVars = scanDirectory('./src', (filePath, content) => {
  const issues = []
  
  // Check for unused imports
  const importMatches = content.match(/import\s+{[^}]+}\s+from/g)
  if (importMatches) {
    importMatches.forEach(imp => {
      const vars = imp.match(/{([^}]+)}/)?.[1]?.split(',').map(v => v.trim())
      if (vars) {
        vars.forEach(varName => {
          const cleanVar = varName.replace(/\s+as\s+\w+/, '').trim()
          const regex = new RegExp(`\\b${cleanVar}\\b`, 'g')
          const matches = content.match(regex)
          if (matches && matches.length <= 1) {
            issues.push(`Potentially unused import: ${cleanVar}`)
          }
        })
      }
    })
  }
  
  // Check for unused const declarations
  const constMatches = content.match(/const\s+(\w+)\s*=/g)
  if (constMatches) {
    constMatches.forEach(match => {
      const varName = match.match(/const\s+(\w+)/)?.[1]
      if (varName) {
        const regex = new RegExp(`\\b${varName}\\b`, 'g')
        const matches = content.match(regex)
        if (matches && matches.length <= 1) {
          issues.push(`Potentially unused variable: ${varName}`)
        }
      }
    })
  }
  
  if (issues.length > 0) {
    return { file: filePath, issues, type: 'unused' }
  }
})

if (unusedVars.length === 0) {
  console.log('✅ No obvious unused variables detected')
} else {
  console.log(`⚠️  Found ${unusedVars.length} files with potentially unused variables:`)
  unusedVars.forEach(item => {
    console.log(`   ${item.file}:`)
    item.issues.forEach(issue => console.log(`     - ${issue}`))
    infoBugs++
  })
}

// 3. Missing Dependencies Check
console.log('\n3. 📦 MISSING DEPENDENCIES CHECK')
const missingDeps = scanDirectory('./src', (filePath, content) => {
  const issues = []
  
  // Check for useEffect dependencies
  const useEffectMatches = content.match(/useEffect\s*\(\s*\(\s*\)\s*=>\s*{[^}]*},\s*\[[^\]]*\]/gs)
  if (useEffectMatches) {
    useEffectMatches.forEach(effect => {
      if (effect.includes('fetch') && !effect.includes('[]')) {
        issues.push('useEffect with fetch may have missing dependencies')
      }
    })
  }
  
  if (issues.length > 0) {
    return { file: filePath, issues, type: 'dependencies' }
  }
})

if (missingDeps.length === 0) {
  console.log('✅ No obvious missing dependencies detected')
} else {
  console.log(`⚠️  Found ${missingDeps.length} files with potential dependency issues:`)
  missingDeps.forEach(item => {
    console.log(`   ${item.file}:`)
    item.issues.forEach(issue => console.log(`     - ${issue}`))
    warningBugs++
  })
}

// 4. Security Issues Check
console.log('\n4. 🔒 SECURITY ISSUES CHECK')
const securityIssues = scanDirectory('./src', (filePath, content) => {
  const issues = []
  
  // Check for hardcoded secrets
  if (content.match(/password\s*[:=]\s*["'][^"']+["']/i)) {
    issues.push('Potential hardcoded password')
  }
  
  if (content.match(/api[_-]?key\s*[:=]\s*["'][^"']+["']/i)) {
    issues.push('Potential hardcoded API key')
  }
  
  if (content.match(/secret\s*[:=]\s*["'][^"']+["']/i)) {
    issues.push('Potential hardcoded secret')
  }
  
  // Check for SQL injection risks
  if (content.includes('${') && content.includes('SELECT')) {
    issues.push('Potential SQL injection risk with template literals')
  }
  
  // Check for XSS risks
  if (content.includes('dangerouslySetInnerHTML')) {
    issues.push('Using dangerouslySetInnerHTML - XSS risk')
  }
  
  if (issues.length > 0) {
    return { file: filePath, issues, type: 'security' }
  }
})

if (securityIssues.length === 0) {
  console.log('✅ No obvious security issues detected')
} else {
  console.log(`❌ Found ${securityIssues.length} files with potential security issues:`)
  securityIssues.forEach(item => {
    console.log(`   ${item.file}:`)
    item.issues.forEach(issue => console.log(`     - ${issue}`))
    criticalBugs++
  })
}

// 5. Performance Issues Check
console.log('\n5. ⚡ PERFORMANCE ISSUES CHECK')
const perfIssues = scanDirectory('./src', (filePath, content) => {
  const issues = []
  
  // Check for missing React.memo
  if (content.includes('export default function') && !content.includes('memo(')) {
    issues.push('Component not memoized - consider React.memo for performance')
  }
  
  // Check for inline object creation in JSX
  if (content.match(/style\s*=\s*{{/)) {
    issues.push('Inline style objects cause re-renders')
  }
  
  // Check for missing key props in lists
  if (content.includes('.map(') && !content.includes('key=')) {
    issues.push('Missing key prop in mapped elements')
  }
  
  if (issues.length > 0) {
    return { file: filePath, issues, type: 'performance' }
  }
})

if (perfIssues.length === 0) {
  console.log('✅ No obvious performance issues detected')
} else {
  console.log(`⚠️  Found ${perfIssues.length} files with potential performance issues:`)
  perfIssues.forEach(item => {
    console.log(`   ${item.file}:`)
    item.issues.forEach(issue => console.log(`     - ${issue}`))
    infoBugs++
  })
}

// 6. File Structure Check
console.log('\n6. 📁 FILE STRUCTURE CHECK')
const requiredFiles = [
  'package.json',
  'next.config.ts',
  'tailwind.config.ts',
  'postcss.config.mjs',
  'ecosystem.config.js',
  '.env.local',
  'prisma/schema.prisma'
]

const missingFiles = requiredFiles.filter(file => !fs.existsSync(file))
if (missingFiles.length === 0) {
  console.log('✅ All required configuration files present')
} else {
  console.log(`❌ Missing required files:`)
  missingFiles.forEach(file => {
    console.log(`   - ${file}`)
    criticalBugs++
  })
}

// Summary
console.log('\n' + '='.repeat(50))
console.log('📊 BUG CHECK SUMMARY')
console.log('='.repeat(50))
console.log(`🔴 Critical Issues: ${criticalBugs}`)
console.log(`🟡 Warning Issues: ${warningBugs}`)
console.log(`🔵 Info Issues: ${infoBugs}`)
console.log(`📊 Total Issues: ${criticalBugs + warningBugs + infoBugs}`)

if (criticalBugs === 0 && warningBugs === 0) {
  console.log('\n🎉 EXCELLENT! No critical or warning issues found!')
  console.log('Your codebase is in great shape for production!')
} else if (criticalBugs === 0) {
  console.log('\n✅ GOOD! No critical issues found.')
  console.log('Consider addressing warning issues for better code quality.')
} else {
  console.log('\n⚠️  ATTENTION NEEDED! Critical issues found.')
  console.log('Please address critical issues before production deployment.')
}

console.log('\n🔧 NEXT STEPS:')
console.log('1. Fix critical issues first')
console.log('2. Address warning issues for better quality')
console.log('3. Consider info issues for optimization')
console.log('4. Run tests to ensure functionality')
console.log('5. Perform security audit before deployment')
