const { default: fetch } = require('node-fetch')

async function testAPIEndpoints() {
  const baseURL = 'http://localhost:3000'
  
  const endpoints = [
    '/api/games',
    '/api/announcements',
    '/api/weekly-tournaments?year=2025',
    '/api/stats/leaderboard',
    '/api/auth/me'
  ]
  
  console.log('Testing API endpoints...\n')
  
  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint}...`)
      const response = await fetch(`${baseURL}${endpoint}`)
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ ${endpoint} - Status: ${response.status}`)
        console.log(`   Response: ${JSON.stringify(data).substring(0, 100)}...`)
      } else {
        console.log(`❌ ${endpoint} - Status: ${response.status}`)
        const errorText = await response.text()
        console.log(`   Error: ${errorText.substring(0, 100)}...`)
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Network Error: ${error.message}`)
    }
    console.log('')
  }
}

testAPIEndpoints()
