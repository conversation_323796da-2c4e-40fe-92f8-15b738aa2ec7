/**
 * @jest-environment node
 */

import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

// This test requires a test database to be set up
// Skip if DATABASE_URL is not set to a test database
const isTestEnvironment = process.env.DATABASE_URL?.includes('test') || process.env.NODE_ENV === 'test'

describe('Database Integration Tests', () => {
  let prisma: PrismaClient

  beforeAll(async () => {
    if (!isTestEnvironment) {
      // Skip database tests in non-test environment
      return
    }
    
    prisma = new PrismaClient()
    await prisma.$connect()
  })

  afterAll(async () => {
    if (prisma) {
      await prisma.$disconnect()
    }
  })

  // Skip tests if not in test environment
  const testIf = (condition: boolean) => condition ? test : test.skip

  testIf(isTestEnvironment)('should connect to database', async () => {
    const result = await prisma.$queryRaw`SELECT 1 as test`
    expect(result).toBeDefined()
  })

  testIf(isTestEnvironment)('should have all required tables', async () => {
    // Check if main tables exist
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    ` as any[]

    const tableNames = tables.map(t => t.table_name)
    
    expect(tableNames).toContain('users')
    expect(tableNames).toContain('games')
    expect(tableNames).toContain('player_registrations')
    expect(tableNames).toContain('tournament_schedules')
    expect(tableNames).toContain('weekly_tournaments')
  })

  testIf(isTestEnvironment)('should create and retrieve a test user', async () => {
    const testUser = {
      username: `testuser_${Date.now()}`,
      firstName: 'Test',
      lastName: 'User',
      phoneNumber: '+265999999999',
      password: await bcrypt.hash('testpassword123', 12),
      email: `test_${Date.now()}@example.com`
    }

    const createdUser = await prisma.user.create({
      data: testUser
    })

    expect(createdUser.username).toBe(testUser.username)
    expect(createdUser.firstName).toBe(testUser.firstName)
    expect(createdUser.lastName).toBe(testUser.lastName)

    // Clean up
    await prisma.user.delete({
      where: { id: createdUser.id }
    })
  })

  testIf(isTestEnvironment)('should enforce unique constraints', async () => {
    const testUser = {
      username: `uniquetest_${Date.now()}`,
      firstName: 'Test',
      lastName: 'User',
      phoneNumber: '+265999999998',
      password: await bcrypt.hash('testpassword123', 12),
      email: `unique_${Date.now()}@example.com`
    }

    // Create first user
    const user1 = await prisma.user.create({
      data: testUser
    })

    // Try to create second user with same username
    await expect(
      prisma.user.create({
        data: {
          ...testUser,
          email: `different_${Date.now()}@example.com`
        }
      })
    ).rejects.toThrow()

    // Clean up
    await prisma.user.delete({
      where: { id: user1.id }
    })
  })

  testIf(isTestEnvironment)('should handle cascade deletes properly', async () => {
    // Create a test user
    const testUser = await prisma.user.create({
      data: {
        username: `cascadetest_${Date.now()}`,
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: '+265999999997',
        password: await bcrypt.hash('testpassword123', 12),
        email: `cascade_${Date.now()}@example.com`
      }
    })

    // Get a game to register for
    const game = await prisma.game.findFirst()
    if (!game) {
      await prisma.user.delete({ where: { id: testUser.id } })
      return
    }

    // Create a registration
    const registration = await prisma.playerRegistration.create({
      data: {
        userId: testUser.id,
        gameId: game.id
      }
    })

    // Delete the user - registration should be deleted too (cascade)
    await prisma.user.delete({
      where: { id: testUser.id }
    })

    // Check that registration was deleted
    const deletedRegistration = await prisma.playerRegistration.findUnique({
      where: { id: registration.id }
    })

    expect(deletedRegistration).toBeNull()
  })
})
