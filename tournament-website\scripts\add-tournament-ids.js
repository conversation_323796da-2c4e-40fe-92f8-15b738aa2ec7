const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function addTournamentIds() {
  console.log('🏆 Adding Tournament IDs to Existing Schedules...\n')

  try {
    // Get all existing tournament schedules
    const schedules = await prisma.tournamentSchedule.findMany({
      orderBy: {
        createdAt: 'asc'
      }
    })

    console.log(`Found ${schedules.length} existing tournament schedules`)

    if (schedules.length === 0) {
      console.log('No schedules to update')
      return
    }

    // Generate tournament IDs for existing schedules
    const year = new Date().getFullYear()
    
    for (let i = 0; i < schedules.length; i++) {
      const schedule = schedules[i]
      const tournamentId = `T${year}${String(i + 1).padStart(3, '0')}`
      
      await prisma.tournamentSchedule.update({
        where: { id: schedule.id },
        data: { 
          // We'll add this field to the schema
          description: schedule.description 
            ? `${schedule.description} [Tournament ID: ${tournamentId}]`
            : `Tournament ID: ${tournamentId}`
        }
      })
      
      console.log(`✅ Updated schedule ${schedule.id} with tournament ID: ${tournamentId}`)
    }

    console.log('\n🎉 Tournament IDs added successfully!')
    console.log('\nNext steps:')
    console.log('1. Add tournamentId field to database schema')
    console.log('2. Update schedule display to show tournament IDs')
    console.log('3. Add registration buttons to schedule page')
    console.log('4. Link registrations to specific tournaments')

  } catch (error) {
    console.error('❌ Error adding tournament IDs:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
addTournamentIds()
