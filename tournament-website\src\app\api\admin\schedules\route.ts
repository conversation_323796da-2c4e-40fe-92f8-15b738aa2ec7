import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'
import { getTournamentWeekForDate } from '@/lib/tournament-utils'

export const GET = withAdminAuth(async (request: NextRequest) => {
  try {
    const schedules = await prisma.tournamentSchedule.findMany({
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        },
        registrations: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
                username: true
              }
            }
          }
        }
      },
      orderBy: {
        scheduledDate: 'asc'
      }
    })

    return NextResponse.json(schedules)
  } catch (error) {
    logger.error('Error fetching schedules:', error)
    return NextResponse.json(
      { error: 'Failed to fetch schedules' },
      { status: 500 }
    )
  }
})

export const POST = withAdminAuth(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const { gameId, scheduledDate, scheduledTime, description, maxParticipants } = body

    if (!gameId || !scheduledDate || !scheduledTime) {
      return NextResponse.json(
        { error: 'Game ID, date, and time are required' },
        { status: 400 }
      )
    }

    // Generate tournament ID
    const currentYear = new Date().getFullYear()
    const existingCount = await prisma.tournamentSchedule.count({
      where: {
        createdAt: {
          gte: new Date(`${currentYear}-01-01`),
          lt: new Date(`${currentYear + 1}-01-01`)
        }
      }
    })
    const tournamentId = `T${currentYear}${String(existingCount + 1).padStart(3, '0')}`

    // Get game details to set appropriate max participants
    const game = await prisma.game.findUnique({
      where: { id: parseInt(gameId) }
    })

    const getGameMaxParticipants = (gameName: string) => {
      switch (gameName?.toLowerCase()) {
        case 'pes': return 32
        case 'pubg': return 100
        case 'call of duty': return 100
        default: return 50
      }
    }

    // Set registration deadline to 1 day before tournament
    const registrationDeadline = new Date(scheduledDate)
    registrationDeadline.setDate(registrationDeadline.getDate() - 1)

    const scheduleDate = new Date(scheduledDate)
    const weekNumber = getTournamentWeekForDate(scheduleDate)
    const year = scheduleDate.getFullYear()

    const schedule = await prisma.tournamentSchedule.create({
      data: {
        tournamentId,
        gameId: parseInt(gameId),
        scheduledDate: scheduleDate,
        scheduledTime: scheduledTime, // Store time as string to avoid timezone issues
        description: description || `${game?.name} Tournament - Week ${weekNumber}`,
        status: 'SCHEDULED',
        maxParticipants: maxParticipants ? parseInt(maxParticipants) : getGameMaxParticipants(game?.name || ''),
        currentParticipants: 0,
        registrationDeadline
      },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Also create a corresponding WeeklyTournament record for the leaderboard
    try {
      await prisma.weeklyTournament.upsert({
        where: {
          gameId_weekNumber_year: {
            gameId: parseInt(gameId),
            weekNumber: weekNumber,
            year: year
          }
        },
        update: {
          tournamentDate: scheduleDate,
          status: 'UPCOMING'
        },
        create: {
          gameId: parseInt(gameId),
          weekNumber: weekNumber,
          year: year,
          tournamentDate: scheduleDate,
          status: 'UPCOMING',
          totalParticipants: 0
        }
      })
    } catch (error) {
      logger.warn('Could not create/update WeeklyTournament record:', error)
    }

    return NextResponse.json(schedule, { status: 201 })
  } catch (error) {
    logger.error('Error creating schedule:', error)
    return NextResponse.json(
      { error: 'Failed to create schedule' },
      { status: 500 }
    )
  }
})
