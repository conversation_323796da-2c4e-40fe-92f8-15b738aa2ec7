/**
 * Tournament utility functions
 */

// Tournament season start date - First weekend (Saturday, January 12th, 2025)
const TOURNAMENT_SEASON_START = new Date('2025-01-12') // Saturday, January 12th, 2025 - First Weekend

/**
 * Get current tournament week number
 * Tournament season starts from first weekend (Saturday, January 12th, 2025)
 * Each week runs Saturday to Sunday
 * @returns Current week number (1-52)
 */
export function getCurrentTournamentWeek(): number {
  const now = new Date()

  // If we haven't reached the first weekend yet, return 0 (pre-season)
  if (now < TOURNAMENT_SEASON_START) {
    return 0
  }

  // Calculate days since tournament season start (first Saturday)
  const daysSinceStart = Math.floor((now.getTime() - TOURNAMENT_SEASON_START.getTime()) / (1000 * 60 * 60 * 24))

  // Calculate week number (1-based, each week is 7 days starting from Saturday)
  const weekNumber = Math.floor(daysSinceStart / 7) + 1

  // Cap at 52 weeks maximum and ensure minimum of 1
  return Math.min(Math.max(weekNumber, 1), 52)
}

/**
 * Get tournament week for a specific date
 * @param date The date to calculate week for
 * @returns Week number (0 for pre-season, 1-52 for tournament weeks)
 */
export function getTournamentWeekForDate(date: Date): number {
  // If date is before tournament season start, return 0 (pre-season)
  if (date < TOURNAMENT_SEASON_START) {
    return 0
  }

  // Calculate days since tournament season start (first Saturday)
  const daysSinceStart = Math.floor((date.getTime() - TOURNAMENT_SEASON_START.getTime()) / (1000 * 60 * 60 * 24))

  // Calculate week number (1-based, each week is 7 days starting from Saturday)
  const weekNumber = Math.floor(daysSinceStart / 7) + 1

  // Cap at 52 weeks maximum and ensure minimum of 1
  return Math.min(Math.max(weekNumber, 1), 52)
}

/**
 * Get the tournament season start date
 * @returns Tournament season start date (First Saturday)
 */
export function getTournamentSeasonStart(): Date {
  return new Date(TOURNAMENT_SEASON_START)
}

/**
 * Get the weekend dates (Saturday and Sunday) for a specific tournament week
 * @param weekNumber The tournament week number (1-52)
 * @returns Object with Saturday and Sunday dates
 */
export function getWeekendDatesForWeek(weekNumber: number): { saturday: Date; sunday: Date } {
  // Calculate the Saturday of the specified week
  const saturday = new Date(TOURNAMENT_SEASON_START)
  saturday.setDate(saturday.getDate() + ((weekNumber - 1) * 7))

  // Sunday is the next day
  const sunday = new Date(saturday)
  sunday.setDate(sunday.getDate() + 1)

  return { saturday, sunday }
}

/**
 * Get current weekend dates (Saturday and Sunday)
 * @returns Object with current weekend dates or null if pre-season
 */
export function getCurrentWeekendDates(): { saturday: Date; sunday: Date } | null {
  const currentWeek = getCurrentTournamentWeek()
  if (currentWeek === 0) {
    return null // Pre-season
  }
  return getWeekendDatesForWeek(currentWeek)
}

/**
 * Check if a date falls on a tournament weekend (Saturday or Sunday)
 * @param date The date to check
 * @returns True if the date is a tournament weekend day
 */
export function isWeekendDate(date: Date): boolean {
  const dayOfWeek = date.getDay() // 0 = Sunday, 6 = Saturday
  return dayOfWeek === 0 || dayOfWeek === 6
}

/**
 * Get a human-readable description of the tournament week
 * @param weekNumber The tournament week number
 * @returns Description string
 */
export function getWeekDescription(weekNumber: number): string {
  if (weekNumber === 0) {
    return 'Pre-season (before January 12th, 2025)'
  }

  const { saturday, sunday } = getWeekendDatesForWeek(weekNumber)
  const saturdayStr = saturday.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  const sundayStr = sunday.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })

  return `Week ${weekNumber}: ${saturdayStr} - ${sundayStr}, 2025`
}

/**
 * Check if a week number is valid
 * @param weekNumber Week number to validate
 * @returns True if valid (1-52)
 */
export function isValidTournamentWeek(weekNumber: number): boolean {
  return weekNumber >= 1 && weekNumber <= 52
}
