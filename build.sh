#!/bin/bash

# Mzuni Tournaments - Build Script
# This script handles the complete build process for deployment

set -e  # Exit on any error

echo "🏆 Starting Mzuni Tournaments Build Process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "tournament-website/package.json" ]; then
    print_error "tournament-website/package.json not found. Please run this script from the project root."
    exit 1
fi

# Change to tournament-website directory
cd tournament-website

print_status "Checking Node.js version..."
node --version
npm --version

# Clean previous builds
print_status "Cleaning previous builds..."
if [ -d ".next" ]; then
    rm -rf .next
    print_success "Removed .next directory"
fi

if [ -d "node_modules" ]; then
    print_warning "Removing existing node_modules for fresh install..."
    rm -rf node_modules
fi

# Install dependencies
print_status "Installing dependencies..."
npm ci --production=false
print_success "Dependencies installed successfully"

# Generate Prisma client
print_status "Generating Prisma client..."
npx prisma generate
print_success "Prisma client generated"

# Check database connection (optional, for local builds)
if [ "$NODE_ENV" != "production" ]; then
    print_status "Checking database connection..."
    if npx prisma db push --accept-data-loss 2>/dev/null; then
        print_success "Database schema updated"
    else
        print_warning "Database connection failed - continuing with build"
    fi
fi

# Run linting
print_status "Running ESLint..."
if npm run lint; then
    print_success "Linting passed"
else
    print_warning "Linting issues found - continuing with build"
fi

# Run tests (if not in production)
if [ "$NODE_ENV" != "production" ]; then
    print_status "Running tests..."
    if npm run test:ci; then
        print_success "All tests passed"
    else
        print_warning "Some tests failed - continuing with build"
    fi
fi

# Build the application
print_status "Building Next.js application..."
npm run build
print_success "Application built successfully"

# Create admin user (for production deployment)
if [ "$NODE_ENV" = "production" ]; then
    print_status "Setting up admin user..."
    if node create-admin.js; then
        print_success "Admin user created/verified"
    else
        print_warning "Admin user setup failed - may already exist"
    fi
fi

# Optimize CSS (if script exists)
if [ -f "scripts/css-optimizer.js" ]; then
    print_status "Optimizing CSS..."
    node scripts/css-optimizer.js
    print_success "CSS optimized"
fi

# Health check
print_status "Performing build health check..."
if [ -d ".next" ] && [ -f ".next/BUILD_ID" ]; then
    BUILD_ID=$(cat .next/BUILD_ID)
    print_success "Build completed successfully! Build ID: $BUILD_ID"
else
    print_error "Build verification failed"
    exit 1
fi

# Display build summary
echo ""
echo "🎉 Build Summary:"
echo "=================="
echo "✅ Dependencies installed"
echo "✅ Prisma client generated"
echo "✅ Application built"
echo "✅ Build verification passed"

if [ "$NODE_ENV" = "production" ]; then
    echo "✅ Production setup completed"
    echo ""
    echo "🚀 Ready for deployment!"
else
    echo "✅ Development build completed"
    echo ""
    echo "🏃 Ready to start development server with: npm run dev"
fi

echo ""
print_success "Mzuni Tournaments build process completed successfully!"
