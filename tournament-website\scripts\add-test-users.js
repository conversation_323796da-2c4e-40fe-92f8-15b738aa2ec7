const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function addTestUsers() {
  console.log('👥 Adding test users for admin counting verification...\n')

  try {
    // Create 3 test users
    const testUsers = [
      {
        username: 'player1',
        firstName: '<PERSON>',
        lastName: '<PERSON><PERSON>',
        phoneNumber: '+265991234567',
        password: 'password123'
      },
      {
        username: 'player2',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        phoneNumber: '+265991234568',
        password: 'password123'
      },
      {
        username: 'player3',
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        phoneNumber: '+265991234569',
        password: 'password123'
      }
    ]

    // Get games for registrations
    const games = await prisma.game.findMany()
    console.log(`Found ${games.length} games: ${games.map(g => g.name).join(', ')}\n`)

    for (const userData of testUsers) {
      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 12)

      // Create user
      const user = await prisma.user.create({
        data: {
          username: userData.username,
          password: hashedPassword,
          firstName: userData.firstName,
          lastName: userData.lastName,
          phoneNumber: userData.phoneNumber,
          role: 'PLAYER'
        }
      })

      console.log(`✅ Created user: ${user.username} (${user.firstName} ${user.lastName})`)

      // Register user for 1-2 random games
      const gamesToRegister = games.slice(0, Math.floor(Math.random() * 2) + 1)
      
      for (const game of gamesToRegister) {
        const gameUsername = game.name === 'PES' ? user.username : `${user.username}_${game.name.toLowerCase().replace(/\s+/g, '')}`
        
        await prisma.playerRegistration.create({
          data: {
            userId: user.id,
            gameId: game.id,
            gameUsername: gameUsername
          }
        })

        console.log(`   📝 Registered for ${game.name} as "${gameUsername}"`)
      }
    }

    console.log('\n📊 Testing admin counting after adding users...')
    
    // Test counting functionality
    const totalPlayers = await prisma.user.count({
      where: { role: 'PLAYER' }
    })
    
    const totalRegistrations = await prisma.playerRegistration.count()
    
    console.log(`\n✅ Admin Counting Results:`)
    console.log(`   Total Players: ${totalPlayers}`)
    console.log(`   Total Registrations: ${totalRegistrations}`)
    
    // Count by game
    console.log(`\n📋 Registrations by Game:`)
    for (const game of games) {
      const count = await prisma.playerRegistration.count({
        where: { gameId: game.id }
      })
      console.log(`   ${game.name}: ${count} registrations`)
    }

    console.log('\n🎉 Test users added successfully!')
    console.log('Admin can now count registered users and total players.')

  } catch (error) {
    console.error('❌ Error adding test users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
addTestUsers()
