#!/bin/bash

# Mzuni Tournaments PM2 Management Script
# Usage: ./scripts/pm2-manager.sh [command]

set -e

APP_NAME="mzuni-tournaments"
ECOSYSTEM_FILE="ecosystem.config.js"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PM2 is installed
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 is not installed. Installing PM2..."
        npm install -g pm2
        log_success "PM2 installed successfully"
    fi
}

# Create logs directory
setup_logs() {
    if [ ! -d "logs" ]; then
        mkdir -p logs
        log_info "Created logs directory"
    fi
}

# Start application in production
start_production() {
    log_info "Starting $APP_NAME in production mode..."
    
    # Build the application
    log_info "Building application..."
    npm run build
    
    # Start with PM2
    pm2 start $ECOSYSTEM_FILE --env production
    
    # Save PM2 configuration
    pm2 save
    
    log_success "$APP_NAME started in production mode"
    pm2 status
}

# Start application in development
start_development() {
    log_info "Starting $APP_NAME in development mode..."
    pm2 start $ECOSYSTEM_FILE --env development
    log_success "$APP_NAME started in development mode"
    pm2 status
}

# Stop application
stop_app() {
    log_info "Stopping $APP_NAME..."
    pm2 stop $APP_NAME
    log_success "$APP_NAME stopped"
}

# Restart application
restart_app() {
    log_info "Restarting $APP_NAME..."
    pm2 restart $APP_NAME
    log_success "$APP_NAME restarted"
}

# Reload application (zero-downtime)
reload_app() {
    log_info "Reloading $APP_NAME (zero-downtime)..."
    pm2 reload $APP_NAME
    log_success "$APP_NAME reloaded"
}

# Delete application from PM2
delete_app() {
    log_warning "Deleting $APP_NAME from PM2..."
    pm2 delete $APP_NAME
    log_success "$APP_NAME deleted from PM2"
}

# Show application status
status_app() {
    pm2 status $APP_NAME
}

# Show application logs
logs_app() {
    pm2 logs $APP_NAME --lines 50
}

# Monitor application
monitor_app() {
    pm2 monit
}

# Setup PM2 startup script
setup_startup() {
    log_info "Setting up PM2 startup script..."
    pm2 startup
    log_warning "Please run the command shown above as root/sudo"
}

# Deploy to production server
deploy_production() {
    log_info "Deploying to production server..."
    pm2 deploy production
    log_success "Deployed to production"
}

# Deploy to staging server
deploy_staging() {
    log_info "Deploying to staging server..."
    pm2 deploy staging
    log_success "Deployed to staging"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    # Check if app is running
    if pm2 list | grep -q "$APP_NAME.*online"; then
        log_success "Application is running"
        
        # Check HTTP endpoint
        if curl -f http://localhost:3002/api/health > /dev/null 2>&1; then
            log_success "Health endpoint is responding"
        else
            log_error "Health endpoint is not responding"
            return 1
        fi
    else
        log_error "Application is not running"
        return 1
    fi
}

# Show help
show_help() {
    echo "Mzuni Tournaments PM2 Management Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start-prod     Start application in production mode"
    echo "  start-dev      Start application in development mode"
    echo "  stop           Stop application"
    echo "  restart        Restart application"
    echo "  reload         Reload application (zero-downtime)"
    echo "  delete         Delete application from PM2"
    echo "  status         Show application status"
    echo "  logs           Show application logs"
    echo "  monitor        Open PM2 monitor"
    echo "  setup-startup  Setup PM2 startup script"
    echo "  deploy-prod    Deploy to production server"
    echo "  deploy-staging Deploy to staging server"
    echo "  health         Perform health check"
    echo "  help           Show this help message"
    echo ""
}

# Main script logic
main() {
    check_pm2
    setup_logs
    
    case "${1:-help}" in
        "start-prod")
            start_production
            ;;
        "start-dev")
            start_development
            ;;
        "stop")
            stop_app
            ;;
        "restart")
            restart_app
            ;;
        "reload")
            reload_app
            ;;
        "delete")
            delete_app
            ;;
        "status")
            status_app
            ;;
        "logs")
            logs_app
            ;;
        "monitor")
            monitor_app
            ;;
        "setup-startup")
            setup_startup
            ;;
        "deploy-prod")
            deploy_production
            ;;
        "deploy-staging")
            deploy_staging
            ;;
        "health")
            health_check
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function
main "$@"
