import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'
import { getTournamentWeekForDate } from '@/lib/tournament-utils'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    const registrations = await prisma.playerRegistration.findMany({
      where: { 
        userId: parseInt(userId),
        isActive: true
      },
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        },
        tournamentSchedule: {
          select: {
            id: true,
            tournamentId: true,
            scheduledDate: true,
            scheduledTime: true,
            description: true,
            status: true,
            maxParticipants: true,
            currentParticipants: true
          }
        }
      },
      orderBy: {
        registrationDate: 'desc'
      }
    })

    // Add week information to tournament registrations
    const registrationsWithWeeks = registrations.map(reg => ({
      ...reg,
      weekNumber: reg.tournamentSchedule 
        ? getTournamentWeekForDate(reg.tournamentSchedule.scheduledDate)
        : null
    }))

    return NextResponse.json(registrationsWithWeeks)
  } catch (error) {
    logger.error('Error fetching user tournament registrations:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tournament registrations' },
      { status: 500 }
    )
  }
}
