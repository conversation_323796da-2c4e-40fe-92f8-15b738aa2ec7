#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🧪 MZUNI TOURNAMENTS - TEST RUNNER')
console.log('=================================\n')

// Check if Jest is installed
const packageJsonPath = path.join(__dirname, '..', 'package.json')
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))

const hasJest = packageJson.devDependencies?.jest || packageJson.dependencies?.jest

if (!hasJest) {
  console.log('❌ Jest is not installed. Installing test dependencies...')
  try {
    execSync('npm install --save-dev jest @types/jest', { stdio: 'inherit' })
    console.log('✅ Test dependencies installed successfully')
  } catch (error) {
    console.error('❌ Failed to install test dependencies:', error.message)
    process.exit(1)
  }
}

// Check if test files exist
const testDir = path.join(__dirname, '..', 'src', '__tests__')
if (!fs.existsSync(testDir)) {
  console.log('⚠️  No test directory found. Creating basic test structure...')
  fs.mkdirSync(testDir, { recursive: true })
  fs.mkdirSync(path.join(testDir, 'api'), { recursive: true })
  fs.mkdirSync(path.join(testDir, 'components'), { recursive: true })
  fs.mkdirSync(path.join(testDir, 'lib'), { recursive: true })
  console.log('✅ Test directory structure created')
}

// Run different test suites
const testSuites = [
  {
    name: 'Unit Tests',
    command: 'npm test -- --testPathPattern="__tests__/(lib|components)"',
    description: 'Testing utility functions and components'
  },
  {
    name: 'API Tests',
    command: 'npm test -- --testPathPattern="__tests__/api"',
    description: 'Testing API endpoints'
  },
  {
    name: 'Integration Tests',
    command: 'npm test -- --testPathPattern="__tests__/integration"',
    description: 'Testing database and system integration'
  }
]

let totalPassed = 0
let totalFailed = 0

for (const suite of testSuites) {
  console.log(`\n🔍 Running ${suite.name}`)
  console.log(`📝 ${suite.description}`)
  console.log('─'.repeat(50))
  
  try {
    execSync(suite.command, { stdio: 'inherit', cwd: path.join(__dirname, '..') })
    console.log(`✅ ${suite.name} passed`)
    totalPassed++
  } catch (error) {
    console.log(`❌ ${suite.name} failed`)
    totalFailed++
  }
}

// Summary
console.log('\n📊 TEST SUMMARY')
console.log('===============')
console.log(`✅ Passed: ${totalPassed}`)
console.log(`❌ Failed: ${totalFailed}`)
console.log(`📊 Total: ${totalPassed + totalFailed}`)

if (totalFailed === 0) {
  console.log('\n🎉 All tests passed! Your code is ready for production.')
} else {
  console.log('\n⚠️  Some tests failed. Please review and fix the issues.')
  process.exit(1)
}

// Generate coverage report if all tests pass
if (totalFailed === 0) {
  console.log('\n📈 Generating coverage report...')
  try {
    execSync('npm run test:coverage', { stdio: 'inherit', cwd: path.join(__dirname, '..') })
    console.log('✅ Coverage report generated in coverage/ directory')
  } catch (error) {
    console.log('⚠️  Could not generate coverage report')
  }
}
