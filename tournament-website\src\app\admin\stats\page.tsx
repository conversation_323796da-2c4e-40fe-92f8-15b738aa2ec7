'use client'
import { useState } from 'react'
import { useRouter } from 'next/navigation' //delete
import { AdminAuthGuard } from '@/components/AdminAuthGuard'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { useEffect, useCallback } from 'react'
import Link from 'next/link'
import { logger } from '@/lib/logger'


interface PlayerStats {
  id: number
  user: {
    id: number
    username: string
    firstName: string
    lastName: string
  }
  game: {
    id: number
    name: string
  }
  tournamentsParticipated: number
  tournamentsWon: number
  totalWins: number
  totalLosses: number
  winPercentage: number | string | null
}

function AdminStatsContent() {
  const [stats, setStats] = useState<PlayerStats[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedGame, setSelectedGame] = useState<string>('all')
  const [games, setGames] = useState<any[]>([])
  const router = useRouter()
  const { logout } = useAdminAuth()

  useEffect(() => {
    // Check if admin is logged in
    const token = localStorage.getItem('adminToken')
    if (!token) {
      router.push('/admin/login')
      return
    }

    fetchGames()
    fetchStats()
  }, [router])

  const fetchGames = async () => {
    try {
      const response = await fetch('/api/games')
      if (response.ok) {
        const data = await response.json()
        setGames(data)
      }
    } catch (error) {
      logger.error('Error fetching games:', error)
    }
  }

  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('adminToken')
      const url = selectedGame === 'all' ? '/api/stats' : `/api/stats?gameId=${selectedGame}`
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      }
    } catch (error) {
      logger.error('Error fetching stats:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (games.length > 0) {
      fetchStats()
    }
  }, [selectedGame, games])

  const updatePlayerStats = async (statsId: number, updates: Partial<PlayerStats>) => {
    try {
      const token = localStorage.getItem('adminToken')
      const response = await fetch(`/api/admin/stats/${statsId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updates)
      })

      if (response.ok) {
        fetchStats() // Refresh the data
        logger.info('✅ Player stats updated successfully!')
      } else if (response.status === 403 || response.status === 401) {
        localStorage.removeItem('adminToken')
        router.push('/admin/login')
      } else {
        const errorData = await response.json()
        logger.error('❌ Error updating player stats:', errorData)
      }
    } catch (error) {
      logger.error('Error updating stats:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading stats...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/admin/dashboard" className="text-3xl font-bold text-blue-600">
                eSports RXP
              </Link>
              <span className="ml-4 px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded-full">
                Manage Stats
              </span>
            </div>
            <Link
              href="/admin/dashboard"
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex items-center space-x-4">
            <label htmlFor="gameFilter" className="text-sm font-medium text-gray-700">
              Filter by Game:
            </label>
            <select
              id="gameFilter"
              name="gameFilter"
              value={selectedGame}
              onChange={(e) => setSelectedGame(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Games</option>
              {games.map((game) => (
                <option key={game.id} value={game.id}>
                  {game.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Stats Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Player Statistics</h2>
            <p className="text-sm text-gray-600">Manage and update player tournament statistics</p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Player
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Game
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tournaments
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Wins/Losses
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Win Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {stats.map((stat) => (
                  <tr key={stat.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {stat.user.firstName} {stat.user.lastName}
                        </div>
                        <div className="text-sm text-gray-500">@{stat.user.username}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        {stat.game.name}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {stat.tournamentsParticipated} played / {stat.tournamentsWon} won
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {stat.totalWins}W - {stat.totalLosses}L
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {stat.winPercentage ? Number(stat.winPercentage).toFixed(1) : '0.0'}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => {
                          const newWins = prompt('Enter new total wins:', stat.totalWins.toString())
                          const newLosses = prompt('Enter new total losses:', stat.totalLosses.toString())
                          if (newWins !== null && newLosses !== null) {
                            const parsedWins = parseInt(newWins, 10)
                            const parsedLosses = parseInt(newLosses, 10)
                            if (isNaN(parsedWins) || isNaN(parsedLosses) || parsedWins < 0 || parsedLosses < 0) {
                              alert('Please enter valid non-negative numbers')
                              return
                            }
                            updatePlayerStats(stat.id, {
                              totalWins: parsedWins,
                              totalLosses: parsedLosses
                            })
                          }
                        }}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        Update W/L
                      </button>
                      <button
                        onClick={() => {
                          const newTournamentWins = prompt('Enter tournament wins:', stat.tournamentsWon.toString())
                          const newTournamentPlayed = prompt('Enter tournaments played:', stat.tournamentsParticipated.toString())
                          if (newTournamentWins !== null && newTournamentPlayed !== null) {
                            const parsedTournamentWins = parseInt(newTournamentWins, 10)
                            const parsedTournamentPlayed = parseInt(newTournamentPlayed, 10)
                            if (isNaN(parsedTournamentWins) || isNaN(parsedTournamentPlayed) ||
                                parsedTournamentWins < 0 || parsedTournamentPlayed < 0 ||
                                parsedTournamentWins > parsedTournamentPlayed) {
                              alert('Please enter valid numbers. Tournament wins cannot exceed tournaments played.')
                              return
                            }
                            updatePlayerStats(stat.id, {
                              tournamentsWon: parsedTournamentWins,
                              tournamentsParticipated: parsedTournamentPlayed
                            })
                          }
                        }}
                        className="text-green-600 hover:text-green-900"
                      >
                        Update Tournaments
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {stats.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No player statistics found.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default function AdminStatsPage() {
  return (
    <AdminAuthGuard>
      <AdminStatsContent />
    </AdminAuthGuard>
  )
}