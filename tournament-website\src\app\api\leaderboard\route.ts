import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { logger } from '@/lib/logger'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'cumulative' // 'cumulative' or 'weekly'
    const week = searchParams.get('week')
    const year = searchParams.get('year')
    const game = searchParams.get('game')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    if (type === 'weekly') {
      return getWeeklyLeaderboard(week, year, game, page, limit)
    } else {
      return getCumulativeLeaderboard(game, page, limit)
    }
  } catch (error) {
    logger.error('Error fetching leaderboard:', error)
    return NextResponse.json(
      { error: 'Failed to fetch leaderboard' },
      { status: 500 }
    )
  }
}

async function getCumulativeLeaderboard(gameFilter?: string | null, page: number = 1, limit: number = 20) {
  try {
    // Get players with tournament stats (these are the main leaderboard)
    const playersWithStats = await prisma.playerStats.findMany({
      where: gameFilter ? {
        game: {
          name: {
            contains: gameFilter,
            mode: 'insensitive'
          }
        }
      } : undefined,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        },
        game: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Get players who have registered for games but haven't played tournaments yet
    const playersWithRegistrationsOnly = await prisma.user.findMany({
      where: {
        role: 'PLAYER',
        registrations: {
          some: gameFilter ? {
            game: {
              name: {
                contains: gameFilter,
                mode: 'insensitive'
              }
            }
          } : {}
        },
        NOT: {
          id: {
            in: playersWithStats.map(p => p.userId)
          }
        }
      },
      include: {
        registrations: {
          where: gameFilter ? {
            game: {
              name: {
                contains: gameFilter,
                mode: 'insensitive'
              }
            }
          } : undefined,
          include: {
            game: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    })

    // Transform registered players without stats
    const registeredPlayersFormatted = playersWithRegistrationsOnly.flatMap(user => {
      return user.registrations.map(reg => ({
        id: `${user.id}-${reg.gameId}`,
        userId: user.id,
        gameId: reg.gameId,
        tournamentsWon: 0,
        tournamentsParticipated: 0,
        totalWins: 0,
        totalLosses: 0,
        winPercentage: 0,
        user: {
          id: user.id,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName
        },
        game: reg.game
      }))
    })

    // Combine lists: players with stats first, then registered players
    const allPlayers = [...playersWithStats, ...registeredPlayersFormatted]

    // Group players by game and rank within each game
    const gameGroups: { [key: string]: any[] } = {}

    allPlayers.forEach(stat => {
      const gameName = stat.game.name
      if (!gameGroups[gameName]) {
        gameGroups[gameName] = []
      }
      gameGroups[gameName].push(stat)
    })

    // Sort players within each game with improved logic
    Object.keys(gameGroups).forEach(gameName => {
      gameGroups[gameName].sort((a, b) => {
        // First priority: Players who have actually participated in tournaments
        const aHasParticipated = a.tournamentsParticipated > 0 || a.tournamentsWon > 0 || a.totalWins > 0
        const bHasParticipated = b.tournamentsParticipated > 0 || b.tournamentsWon > 0 || b.totalWins > 0
        
        if (aHasParticipated !== bHasParticipated) {
          return bHasParticipated ? 1 : -1 // Participated players first
        }
        
        // Second priority: Sort by tournaments won
        if (b.tournamentsWon !== a.tournamentsWon) {
          return b.tournamentsWon - a.tournamentsWon
        }
        
        // Third priority: Sort by win percentage (only meaningful if they've played)
        if (aHasParticipated && bHasParticipated && b.winPercentage !== a.winPercentage) {
          return Number(b.winPercentage) - Number(a.winPercentage)
        }
        
        // Fourth priority: Sort by total wins
        if (b.totalWins !== a.totalWins) {
          return b.totalWins - a.totalWins
        }
        
        // Final priority: Sort by tournaments participated (engagement level)
        return b.tournamentsParticipated - a.tournamentsParticipated
      })
    })

    // Interleave rankings: #1 from each game, then #2 from each game, etc.
    const result: any[] = []
    const maxRankings = Math.max(...Object.values(gameGroups).map(group => group.length))

    for (let rank = 0; rank < maxRankings; rank++) {
      Object.keys(gameGroups).sort().forEach(gameName => {
        if (gameGroups[gameName][rank]) {
          const player = gameGroups[gameName][rank]
          const gameRank = rank + 1
          result.push({
            ...player,
            gameRank,
            overallRank: result.length + 1
          })
        }
      })
    }

    // Apply pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedResult = result.slice(startIndex, endIndex)

    return NextResponse.json({
      leaderboard: paginatedResult,
      pagination: {
        page,
        limit,
        total: result.length,
        totalPages: Math.ceil(result.length / limit)
      },
      type: 'cumulative'
    })
  } catch (error) {
    logger.error('Error fetching cumulative leaderboard:', error)
    throw error
  }
}

async function getWeeklyLeaderboard(week?: string | null, year?: string | null, gameFilter?: string | null, page: number = 1, limit: number = 20) {
  try {
    const currentYear = year ? parseInt(year) : new Date().getFullYear()
    const weekNumber = week ? parseInt(week) : undefined

    const whereClause: any = {
      status: 'COMPLETED',
      year: currentYear
    }

    if (weekNumber) {
      whereClause.weekNumber = weekNumber
    }

    if (gameFilter) {
      whereClause.game = {
        name: {
          contains: gameFilter,
          mode: 'insensitive'
        }
      }
    }

    const weeklyTournaments = await prisma.weeklyTournament.findMany({
      where: whereClause,
      include: {
        game: {
          select: {
            id: true,
            name: true
          }
        },
        winner: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: [
        { weekNumber: 'desc' },
        { totalParticipants: 'desc' }
      ]
    })

    // Apply pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedResult = weeklyTournaments.slice(startIndex, endIndex)

    return NextResponse.json({
      leaderboard: paginatedResult,
      pagination: {
        page,
        limit,
        total: weeklyTournaments.length,
        totalPages: Math.ceil(weeklyTournaments.length / limit)
      },
      type: 'weekly'
    })
  } catch (error) {
    logger.error('Error fetching weekly leaderboard:', error)
    throw error
  }
}
