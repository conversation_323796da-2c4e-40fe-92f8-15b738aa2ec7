const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  console.log('Adding sample announcements...')

  // Add sample announcements
  const announcements = [
    {
      title: "🎮 Weekend Tournament Schedule Released!",
      content: "This weekend's tournaments are confirmed! PUBG at 2 PM, Call of Duty at 4 PM, and PES at 6 PM. Register now to secure your spot!",
      isActive: true
    },
    {
      title: "🏆 New Prize Structure Announced",
      content: "We're excited to announce enhanced prizes for tournament winners! 1st place gets K5,000, 2nd place K3,000, and 3rd place K2,000.",
      isActive: true
    },
    {
      title: "📱 WhatsApp Registration Now Available",
      content: "You can now register for tournaments directly via WhatsApp! Just fill out the form and click the WhatsApp button to send your details.",
      isActive: true
    }
  ]

  for (const announcement of announcements) {
    await prisma.announcement.create({
      data: announcement
    })
    console.log(`✅ Added announcement: ${announcement.title}`)
  }

  console.log('✅ Sample announcements added successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error adding sample announcements:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
