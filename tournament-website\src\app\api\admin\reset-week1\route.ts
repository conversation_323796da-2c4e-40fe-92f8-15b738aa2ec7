import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { withAdminAuth } from '@/lib/admin-middleware'
import { logger } from '@/lib/logger'

export const POST = withAdminAuth(async (request: NextRequest) => {
  try {
    logger.info('🔄 Starting Week 1 reset...')

    // Step 1: Clear existing tournament data (keep users and games)
    await prisma.weeklyWinner.deleteMany({})
    await prisma.weeklyTournament.deleteMany({})
    await prisma.tournamentSchedule.deleteMany({})
    await prisma.playerRegistration.deleteMany({})
    
    // Reset player stats
    await prisma.playerStats.updateMany({
      data: {
        tournamentsParticipated: 0,
        tournamentsWon: 0,
        totalWins: 0,
        totalLosses: 0,
        winPercentage: 0.00
      }
    })

    logger.info('✅ Cleared existing tournament data')

    // Step 2: Create Week 1 tournaments for all games
    const games = await prisma.game.findMany()
    const week1Date = new Date('2025-07-07') // This Sunday (Week 1)
    
    const createdTournaments = []

    for (const game of games) {
      // Create Week 1 tournament
      const tournament = await prisma.weeklyTournament.create({
        data: {
          gameId: game.id,
          weekNumber: 1,
          year: 2025,
          tournamentDate: week1Date,
          status: 'UPCOMING',
          totalParticipants: 0
        }
      })
      
      // Create corresponding schedule
      const schedule = await prisma.tournamentSchedule.create({
        data: {
          tournamentId: `T2025${String(game.id).padStart(3, '0')}`,
          gameId: game.id,
          scheduledDate: week1Date,
          scheduledTime: new Date('1970-01-01T14:00:00'), // 2:00 PM
          description: `${game.name} Tournament - Week 1`,
          status: 'SCHEDULED',
          maxParticipants: game.name === 'PES' ? 32 : 100,
          currentParticipants: 0,
          registrationDeadline: new Date('2025-07-06T23:59:59') // Day before
        }
      })
      
      createdTournaments.push({
        game: game.name,
        tournament: tournament,
        schedule: schedule
      })
      
      logger.info(`✅ Created Week 1 tournament for ${game.name}`)
    }

    logger.info('🎉 Week 1 reset completed successfully!')

    return NextResponse.json({
      success: true,
      message: 'Tournament system reset to Week 1',
      data: {
        clearedData: {
          weeklyWinners: 'cleared',
          weeklyTournaments: 'cleared',
          tournamentSchedules: 'cleared',
          playerRegistrations: 'cleared',
          playerStats: 'reset'
        },
        createdTournaments: createdTournaments.length,
        tournaments: createdTournaments.map(t => ({
          game: t.game,
          weekNumber: 1,
          year: 2025,
          date: week1Date.toDateString(),
          status: 'UPCOMING'
        })),
        tournamentSeasonStart: '2025-07-01',
        currentWeek: 1
      }
    })

  } catch (error) {
    logger.error('❌ Error resetting to Week 1:', error)
    return NextResponse.json(
      { 
        error: 'Failed to reset tournament system',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
})
